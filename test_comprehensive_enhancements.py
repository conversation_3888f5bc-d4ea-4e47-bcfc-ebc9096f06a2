"""Comprehensive test for all CLA v2.0 Bot enhancements."""

import os
import sys

# Add src to path
sys.path.insert(0, 'src')

def test_cache_period_extension():
    """Test 7-day cache period configuration."""
    print("🧪 Testing Cache Period Extension...")
    
    try:
        from config import config
        
        cache_hours = config.cache.ca_expiry_hours
        print(f"✅ Cache expiry period: {cache_hours} hours")
        
        if cache_hours == 168:
            print("   ✅ Correctly set to 7 days (168 hours)")
        else:
            print(f"   ❌ Expected 168 hours, got {cache_hours}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Cache period test failed: {e}")
        return False

def test_new_source_groups():
    """Test new source groups configuration."""
    print("\n🧪 Testing New Source Groups Configuration...")
    
    try:
        from config import config
        
        # Test all group IDs
        all_groups = config.target_group.all_group_ids
        active_groups = config.target_group.active_group_ids
        
        print(f"✅ Total groups configured: {len(all_groups)}")
        print(f"✅ Active groups: {len(active_groups)}")
        
        # Expected groups
        expected_groups = [
            -1002380594298,  # FREE WHALE SIGNALS
            -1002270988204,  # Solana Activity Tracker
            -1002064145465,  # 🌹 MANIFEST
            -1001763265784,  # 👤 Mark Degens
            -1002139128702,  # 💎 FINDERTRENDING
            -1002202241417,  # 🧠 GMGN Featured Signals (PAUSED)
            -1002333406905   # 🚀 MEME 1000X (PAUSED)
        ]
        
        print("\nGroup Status:")
        for i, group_id in enumerate(all_groups):
            group_name = "PRIMARY" if i == 0 else config.target_group.additional_group_names[i-1] if i-1 < len(config.target_group.additional_group_names) else f"Group {group_id}"
            status = config.target_group.group_status.get(group_id, 'UNKNOWN')
            print(f"  - {group_name} ({group_id}) [{status}]")
        
        # Check if all expected groups are present
        missing_groups = [g for g in expected_groups if g not in all_groups]
        if missing_groups:
            print(f"❌ Missing groups: {missing_groups}")
            return False
        
        # Check active vs paused groups
        expected_active = 5  # PRIMARY + 4 active additional groups
        if len(active_groups) == expected_active:
            print(f"✅ Correct number of active groups: {expected_active}")
        else:
            print(f"❌ Expected {expected_active} active groups, got {len(active_groups)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Source groups test failed: {e}")
        return False

def test_new_forwarding_destinations():
    """Test new forwarding destinations configuration."""
    print("\n🧪 Testing New Forwarding Destinations...")
    
    try:
        from config import config
        
        # Test BonkBot (existing)
        print(f"✅ BonkBot: {config.bonkbot.username}")
        
        # Test WINNERS (existing)
        print(f"✅ WINNERS: {config.winners_group.group_name} ({config.winners_group.group_id})")
        
        # Test CLA v2.0 (new)
        print(f"✅ CLA v2.0: {config.cla_v2_group.group_name} ({config.cla_v2_group.group_id})")
        
        # Test Monaco PNL (new)
        print(f"✅ Monaco PNL: {config.monaco_pnl.group_name} ({config.monaco_pnl.group_id})")
        print(f"   Username: {config.monaco_pnl.username}")
        
        # Verify expected chat IDs
        expected_destinations = {
            'WINNERS': -1002439728391,
            'CLA v2.0': -1002659786727,
            'Monaco PNL': -1002666569586
        }
        
        actual_destinations = {
            'WINNERS': config.winners_group.group_id,
            'CLA v2.0': config.cla_v2_group.group_id,
            'Monaco PNL': config.monaco_pnl.group_id
        }
        
        for name, expected_id in expected_destinations.items():
            actual_id = actual_destinations[name]
            if actual_id == expected_id:
                print(f"   ✅ {name} ID correct: {actual_id}")
            else:
                print(f"   ❌ {name} ID mismatch: expected {expected_id}, got {actual_id}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Forwarding destinations test failed: {e}")
        return False

def test_integration_imports():
    """Test that all integration modules can be imported."""
    print("\n🧪 Testing Integration Module Imports...")
    
    try:
        # Test existing integrations
        from src.bonkbot_integration import BonkBotIntegration
        print("✅ BonkBot integration imported")
        
        from src.winners_integration import WinnersIntegration
        print("✅ WINNERS integration imported")
        
        # Test new integrations
        from src.cla_v2_integration import CLAv2Integration
        print("✅ CLA v2.0 integration imported")
        
        from src.monaco_pnl_integration import MonacoPNLIntegration
        print("✅ Monaco PNL integration imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration imports test failed: {e}")
        return False

def test_enhanced_message_parsing():
    """Test enhanced message parsing with link filtering."""
    print("\n🧪 Testing Enhanced Message Parsing...")
    
    try:
        from src.message_parser import MessageParser
        
        parser = MessageParser()
        
        # Test message from new source groups
        manifest_message = """🌹 MANIFEST SIGNAL 🌹
        
New gem found!
Visit https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
Follow @manifest_signals

67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        cas = parser.extract_contract_addresses(manifest_message)
        print(f"✅ MANIFEST format parsing: {len(cas)} CAs extracted")
        
        # Test with multiple links and mentions
        complex_message = """💎 FINDERTRENDING 💎
        
Multiple links test:
https://pump.fun/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
www.solscan.io/token/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
t.me/trending_channel
@follow_us

CA: 9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk"""
        
        cas2 = parser.extract_contract_addresses(complex_message)
        print(f"✅ Complex message parsing: {len(cas2)} CAs extracted (links filtered)")
        
        if len(cas) >= 1 and len(cas2) >= 1:
            print("   ✅ Message parsing working correctly")
            return True
        else:
            print("   ❌ Message parsing not extracting CAs correctly")
            return False
        
    except Exception as e:
        print(f"❌ Message parsing test failed: {e}")
        return False

def test_bot_statistics_structure():
    """Test bot statistics structure includes all new destinations."""
    print("\n🧪 Testing Bot Statistics Structure...")
    
    try:
        from src.bot import CLABot
        from src.database import DatabaseManager
        import tempfile
        
        # Create temporary database for testing
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        db_manager = DatabaseManager()
        db_manager.db_path = temp_db.name
        
        bot = CLABot(db_manager)
        
        # Check statistics structure
        expected_stats = [
            'messages_processed',
            'cas_detected',
            'cas_sent_to_bonkbot',
            'cas_sent_to_winners',
            'cas_sent_to_cla_v2',
            'cas_sent_to_monaco_pnl',
            'pnl_updates_processed',
            'start_time'
        ]
        
        for stat in expected_stats:
            if stat in bot.stats:
                print(f"   ✅ {stat}")
            else:
                print(f"   ❌ Missing stat: {stat}")
                return False
        
        # Cleanup
        os.unlink(temp_db.name)
        
        print("✅ All statistics fields present")
        return True
        
    except Exception as e:
        print(f"❌ Statistics structure test failed: {e}")
        return False

def main():
    """Run all comprehensive enhancement tests."""
    print("🚀 CLA v2.0 Bot - Comprehensive Enhancement Testing\n")
    
    try:
        # Test 1: Cache Period Extension
        if not test_cache_period_extension():
            return False
        
        # Test 2: New Source Groups
        if not test_new_source_groups():
            return False
        
        # Test 3: New Forwarding Destinations
        if not test_new_forwarding_destinations():
            return False
        
        # Test 4: Integration Imports
        if not test_integration_imports():
            return False
        
        # Test 5: Enhanced Message Parsing
        if not test_enhanced_message_parsing():
            return False
        
        # Test 6: Bot Statistics Structure
        if not test_bot_statistics_structure():
            return False
        
        print("\n🎉 All comprehensive enhancement tests passed!")
        print("\n📋 Enhancement Summary:")
        print("✅ Cache Period: Extended to 7 days (168 hours)")
        print("✅ Source Groups: 7 total (5 active, 2 paused)")
        print("   - FREE WHALE SIGNALS [ACTIVE]")
        print("   - Solana Activity Tracker [ACTIVE]")
        print("   - 🌹 MANIFEST [ACTIVE]")
        print("   - 👤 Mark Degens [ACTIVE]")
        print("   - 💎 FINDERTRENDING [ACTIVE]")
        print("   - 🧠 GMGN Featured Signals [PAUSED]")
        print("   - 🚀 MEME 1000X [PAUSED]")
        print("✅ Forwarding Destinations: 4 total")
        print("   - BonkBot (auto-trading)")
        print("   - WINNERS group")
        print("   - CLA v2.0 channel")
        print("   - Monaco PNL channel")
        print("✅ Global Duplicate Prevention: 7-day cache across all sources")
        print("✅ Link Filtering: Enhanced across all message formats")
        print("✅ Statistics Tracking: All destinations included")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Comprehensive enhancement test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
