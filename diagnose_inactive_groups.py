#!/usr/bin/env python3
"""
Diagnose inactive group connectivity issues for FINDERTRENDING and MANIFEST
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_group_configuration():
    """Diagnose group configuration issues."""
    print("🔍 DIAGNOSING GROUP CONFIGURATION...")
    
    try:
        from config import config
        
        # Check target group configuration
        print(f"\n📋 TARGET GROUP CONFIGURATION:")
        print(f"Primary Group: {config.target_group.group_name} ({config.target_group.group_id})")
        print(f"All Groups: {config.target_group.all_group_ids}")
        print(f"Active Groups: {config.target_group.active_group_ids}")
        
        # Check specific groups
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"\n🔍 SPECIFIC GROUP ANALYSIS:")
        print(f"FINDERTRENDING ({findertrending_id}):")
        print(f"  - In all_group_ids: {findertrending_id in config.target_group.all_group_ids}")
        print(f"  - In active_group_ids: {findertrending_id in config.target_group.active_group_ids}")
        print(f"  - Status: {config.target_group.group_status.get(findertrending_id, 'NOT FOUND')}")
        
        print(f"MANIFEST ({manifest_id}):")
        print(f"  - In all_group_ids: {manifest_id in config.target_group.all_group_ids}")
        print(f"  - In active_group_ids: {manifest_id in config.target_group.active_group_ids}")
        print(f"  - Status: {config.target_group.group_status.get(manifest_id, 'NOT FOUND')}")
        
        # Check trending configuration
        print(f"\n📊 TRENDING CONFIGURATION:")
        print(f"High-volume groups: {config.trending.high_volume_groups}")
        print(f"Low-volume groups: {config.trending.low_volume_groups}")
        
        print(f"FINDERTRENDING in low-volume: {findertrending_id in config.trending.low_volume_groups}")
        print(f"MANIFEST in low-volume: {manifest_id in config.trending.low_volume_groups}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_group_manager():
    """Diagnose group manager configuration."""
    print("\n🔍 DIAGNOSING GROUP MANAGER...")
    
    try:
        from src.group_manager import group_manager
        
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"FINDERTRENDING ({findertrending_id}):")
        print(f"  - Is monitored: {group_manager.is_monitored_group(findertrending_id)}")
        print(f"  - Is low-volume: {group_manager.is_low_volume_group(findertrending_id)}")
        print(f"  - Group name: {group_manager.get_group_name(findertrending_id)}")
        print(f"  - Group type: {group_manager.get_group_type(findertrending_id)}")
        
        print(f"MANIFEST ({manifest_id}):")
        print(f"  - Is monitored: {group_manager.is_monitored_group(manifest_id)}")
        print(f"  - Is low-volume: {group_manager.is_low_volume_group(manifest_id)}")
        print(f"  - Group name: {group_manager.get_group_name(manifest_id)}")
        print(f"  - Group type: {group_manager.get_group_type(manifest_id)}")
        
        print(f"\nAll monitored groups: {group_manager.get_all_monitored_groups()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Group manager diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment_variables():
    """Check relevant environment variables."""
    print("\n🔍 CHECKING ENVIRONMENT VARIABLES...")
    
    env_vars = [
        'ADDITIONAL_GROUP_IDS',
        'ADDITIONAL_GROUP_NAMES', 
        'ADDITIONAL_GROUP_STATUS',
        'TRENDING_LOW_VOLUME_GROUPS',
        'TRENDING_HIGH_VOLUME_GROUPS'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'NOT SET')
        print(f"{var}: {value}")
    
    return True

def diagnose_message_handler_setup():
    """Diagnose message handler setup."""
    print("\n🔍 DIAGNOSING MESSAGE HANDLER SETUP...")
    
    try:
        from src.group_manager import group_manager
        
        # Get groups that would be passed to message handler
        monitored_groups = list(group_manager.get_all_monitored_groups())
        print(f"Groups passed to message handler: {monitored_groups}")
        
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"FINDERTRENDING in handler groups: {findertrending_id in monitored_groups}")
        print(f"MANIFEST in handler groups: {manifest_id in monitored_groups}")
        
        return True
        
    except Exception as e:
        print(f"❌ Message handler diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main diagnostic function."""
    print("🚨 INACTIVE GROUP CONNECTIVITY DIAGNOSIS")
    print("=" * 50)
    
    success = True
    
    success &= check_environment_variables()
    success &= diagnose_group_configuration()
    success &= diagnose_group_manager()
    success &= diagnose_message_handler_setup()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ DIAGNOSIS COMPLETED - Check results above")
    else:
        print("❌ DIAGNOSIS FAILED - Check errors above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
