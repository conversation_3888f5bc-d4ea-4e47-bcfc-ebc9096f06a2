"""Database management for CLA v2.0 Bot."""

import aiosqlite
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from pathlib import Path
from contextlib import asynccontextmanager
from loguru import logger

from config import config
from src.error_handler import <PERSON>rror<PERSON>andler

class DatabaseManager:
    """Manages SQLite database operations for the CLA bot with transaction support."""

    def __init__(self):
        self.db_path = config.database.path
        self.connection = None
        self.error_handler = ErrorHandler()

        # Connection pool settings
        self.max_connections = 10
        self.connection_pool = []
        self.pool_lock = asyncio.Lock()

        # Transaction statistics
        self.transaction_stats = {
            'total_transactions': 0,
            'successful_transactions': 0,
            'failed_transactions': 0,
            'rollbacks': 0
        }

        # Query performance tracking
        self.query_stats = {
            'total_queries': 0,
            'slow_queries': 0,
            'batch_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }

        # Query result cache
        self.query_cache = {}
        self.cache_ttl_seconds = 300  # 5 minutes
    
    async def initialize(self):
        """Initialize database connection and create tables."""
        try:
            # Ensure database directory exists
            Path(self.db_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Connect to database
            self.connection = await aiosqlite.connect(self.db_path)
            self.connection.row_factory = aiosqlite.Row
            
            # Create tables
            await self._create_tables()

            # Create indexes
            await self._create_indexes()

            # Optimize database settings for performance
            await self._optimize_database_settings()

            logger.info(f"Database initialized at {self.db_path}")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    @asynccontextmanager
    async def transaction(self):
        """Context manager for database transactions with automatic rollback on error."""
        self.transaction_stats['total_transactions'] += 1

        try:
            await self.connection.execute("BEGIN")
            logger.debug("🔄 Database transaction started")

            yield self.connection

            await self.connection.commit()
            self.transaction_stats['successful_transactions'] += 1
            logger.debug("✅ Database transaction committed")

        except Exception as e:
            await self.connection.rollback()
            self.transaction_stats['failed_transactions'] += 1
            self.transaction_stats['rollbacks'] += 1
            logger.error(f"❌ Database transaction rolled back: {e}")
            raise

    async def execute_with_retry(self, query: str, params: tuple = (),
                               operation_name: str = "database_operation") -> Any:
        """Execute database operation with retry logic and performance monitoring."""
        start_time = time.time()
        self.query_stats['total_queries'] += 1

        async def db_operation():
            async with self.transaction():
                cursor = await self.connection.execute(query, params)
                return cursor

        try:
            result = await self.error_handler.handle_error_with_retry(
                operation=db_operation,
                operation_name=operation_name,
                max_retries=3,
                base_delay=1.0
            )

            # Performance monitoring
            query_time = (time.time() - start_time) * 1000
            if query_time > 100:  # Log slow queries
                self.query_stats['slow_queries'] += 1
                logger.warning(f"⚠️ SLOW QUERY: {operation_name} took {query_time:.1f}ms")
                logger.debug(f"Query: {query[:100]}...")

            return result

        except Exception as e:
            query_time = (time.time() - start_time) * 1000
            logger.error(f"❌ QUERY FAILED: {operation_name} after {query_time:.1f}ms - {e}")
            raise

    async def execute_many_with_retry(self, query: str, params_list: List[tuple],
                                    operation_name: str = "database_batch_operation") -> bool:
        """Execute multiple database operations in a single transaction with retry."""
        async def batch_operation():
            async with self.transaction():
                await self.connection.executemany(query, params_list)
                return True

        return await self.error_handler.handle_error_with_retry(
            operation=batch_operation,
            operation_name=operation_name,
            max_retries=3,
            base_delay=1.0
        )

    async def batch_ca_lookup(self, ca_addresses: List[str]) -> Dict[str, bool]:
        """Batch lookup for multiple CA addresses to solve N+1 query problem."""
        if not ca_addresses:
            return {}

        start_time = time.time()
        self.query_stats['batch_operations'] += 1

        try:
            # Create placeholders for IN clause
            placeholders = ','.join('?' * len(ca_addresses))
            query = f"SELECT address FROM contract_addresses WHERE address IN ({placeholders})"

            async with self.transaction():
                cursor = await self.connection.execute(query, ca_addresses)
                found_addresses = {row['address'] for row in await cursor.fetchall()}

            # Create result dict
            result = {ca: ca in found_addresses for ca in ca_addresses}

            query_time = (time.time() - start_time) * 1000
            if query_time > 100:  # Log slow queries
                self.query_stats['slow_queries'] += 1
                logger.warning(f"⚠️ SLOW BATCH QUERY: {query_time:.1f}ms for {len(ca_addresses)} CAs")

            logger.debug(f"📊 BATCH CA LOOKUP: {len(ca_addresses)} CAs in {query_time:.1f}ms")
            return result

        except Exception as e:
            logger.error(f"Error in batch CA lookup: {e}")
            return {ca: False for ca in ca_addresses}

    async def get_cached_query_result(self, cache_key: str, query_func, ttl_seconds: int = None):
        """Get cached query result or execute query and cache result."""
        if ttl_seconds is None:
            ttl_seconds = self.cache_ttl_seconds

        # Check cache
        if cache_key in self.query_cache:
            cached_result, timestamp = self.query_cache[cache_key]
            if (time.time() - timestamp) < ttl_seconds:
                self.query_stats['cache_hits'] += 1
                logger.debug(f"📊 CACHE HIT: {cache_key}")
                return cached_result
            else:
                # Remove expired entry
                del self.query_cache[cache_key]

        # Cache miss - execute query
        self.query_stats['cache_misses'] += 1
        logger.debug(f"📊 CACHE MISS: {cache_key}")

        result = await query_func()

        # Cache result
        self.query_cache[cache_key] = (result, time.time())

        # Cleanup old cache entries if cache is getting large
        if len(self.query_cache) > 1000:
            await self._cleanup_query_cache()

        return result

    async def _cleanup_query_cache(self):
        """Clean up expired entries from query cache."""
        current_time = time.time()
        expired_keys = []

        for key, (result, timestamp) in self.query_cache.items():
            if (current_time - timestamp) > self.cache_ttl_seconds:
                expired_keys.append(key)

        for key in expired_keys:
            del self.query_cache[key]

        if expired_keys:
            logger.debug(f"🧹 CACHE CLEANUP: Removed {len(expired_keys)} expired entries")
    
    async def _create_tables(self):
        """Create database tables."""
        
        # Contract addresses table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS contract_addresses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ca TEXT UNIQUE NOT NULL,
                first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                source_message_id INTEGER,
                source_group_id INTEGER,
                processed BOOLEAN DEFAULT FALSE,
                sent_to_bonkbot BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        

        
        # Message cache table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS message_cache (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_id INTEGER UNIQUE NOT NULL,
                group_id INTEGER NOT NULL,
                message_text TEXT,
                processed BOOLEAN DEFAULT FALSE,
                ca_extracted TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # CA processing cache table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS ca_cache (
                ca TEXT PRIMARY KEY,
                first_processed TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                hit_count INTEGER DEFAULT 1
            )
        """)
        
        # Bot statistics table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS bot_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                stat_name TEXT UNIQUE NOT NULL,
                stat_value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Trending analysis table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS trending_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ca TEXT NOT NULL,
                group_id INTEGER NOT NULL,
                group_name TEXT,
                first_mention TIMESTAMP,
                trending_qualified TIMESTAMP,
                mention_count INTEGER DEFAULT 1,
                time_to_trend_seconds REAL,
                is_trending BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # CA mentions tracking table
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS ca_mentions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ca TEXT NOT NULL,
                group_id INTEGER NOT NULL,
                group_name TEXT,
                message_id INTEGER,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # TODO: CA forwarding log table (for tracking actual forwarding to destinations)
        # await self.connection.execute("""
        #     CREATE TABLE IF NOT EXISTS ca_forwarding_log (
        #         id INTEGER PRIMARY KEY AUTOINCREMENT,
        #         ca TEXT NOT NULL,
        #         source_group_id INTEGER NOT NULL,
        #         destination TEXT NOT NULL,
        #         forwarded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        #         success BOOLEAN DEFAULT TRUE
        #     )
        # """)

        await self.connection.commit()
    
    async def _create_indexes(self):
        """Create database indexes for performance."""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_ca_first_seen ON contract_addresses(first_seen)",
            "CREATE INDEX IF NOT EXISTS idx_ca_processed ON contract_addresses(processed)",
            "CREATE INDEX IF NOT EXISTS idx_pnl_ca ON pnl_tracking(ca)",
            "CREATE INDEX IF NOT EXISTS idx_pnl_timestamp ON pnl_tracking(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_cache_timestamp ON message_cache(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_ca_cache_last_seen ON ca_cache(last_seen)",
            "CREATE INDEX IF NOT EXISTS idx_trending_ca ON trending_analysis(ca)",
            "CREATE INDEX IF NOT EXISTS idx_trending_timestamp ON trending_analysis(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_mentions_ca_timestamp ON ca_mentions(ca, timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_mentions_group_timestamp ON ca_mentions(group_id, timestamp)"
        ]
        
        for index_sql in indexes:
            await self.connection.execute(index_sql)
        
        await self.connection.commit()

    async def _optimize_database_settings(self):
        """Optimize database settings for better performance."""
        try:
            # Enable WAL mode for better concurrency
            await self.connection.execute("PRAGMA journal_mode=WAL")

            # Optimize cache size (10MB)
            await self.connection.execute("PRAGMA cache_size=10000")

            # Optimize synchronous mode for better performance
            await self.connection.execute("PRAGMA synchronous=NORMAL")

            # Optimize temp store
            await self.connection.execute("PRAGMA temp_store=MEMORY")

            # Optimize page size
            await self.connection.execute("PRAGMA page_size=4096")

            # Enable query planner optimization
            await self.connection.execute("PRAGMA optimize")

            await self.connection.commit()
            logger.info("✅ Database performance optimizations applied")

        except Exception as e:
            logger.error(f"Failed to optimize database settings: {e}")

    async def add_contract_address(self, ca: str, message_id: int, group_id: int) -> bool:
        """Add a new contract address to the database with atomic duplicate prevention."""
        try:
            cursor = await self.connection.execute("""
                INSERT OR IGNORE INTO contract_addresses
                (ca, source_message_id, source_group_id)
                VALUES (?, ?, ?)
            """, (ca, message_id, group_id))

            # Check if the insert was successful (rowcount > 0 means new record)
            rows_affected = cursor.rowcount
            await self.connection.commit()

            return rows_affected > 0  # True if new record was inserted, False if duplicate

        except Exception as e:
            logger.error(f"Failed to add contract address {ca}: {e}")
            return False
    
    async def is_ca_processed(self, ca: str) -> bool:
        """Check if a contract address has been processed recently."""
        try:
            expiry_time = datetime.now() - timedelta(hours=config.cache.ca_expiry_hours)

            cursor = await self.connection.execute("""
                SELECT 1 FROM ca_cache
                WHERE ca = ? AND last_seen > ?
            """, (ca, expiry_time))

            result = await cursor.fetchone()
            return result is not None

        except Exception as e:
            logger.error(f"Failed to check CA processing status: {e}")
            return False

    async def get_recent_cas(self, hours: int = 24) -> List[str]:
        """Get list of recent CAs for cache pre-population (performance optimization)."""
        try:
            expiry_time = datetime.now() - timedelta(hours=hours)

            cursor = await self.connection.execute("""
                SELECT DISTINCT ca FROM contract_addresses
                WHERE first_seen > ?
                ORDER BY first_seen DESC
                LIMIT 10000
            """, (expiry_time,))

            rows = await cursor.fetchall()
            return [row[0] for row in rows]

        except Exception as e:
            logger.error(f"Failed to get recent CAs: {e}")
            return []

    async def was_ca_forwarded_to_destinations(self, ca: str) -> bool:
        """Check if a CA was previously forwarded to any destinations within the duplicate prevention window."""
        try:
            expiry_time = datetime.now() - timedelta(hours=config.cache.ca_expiry_hours)

            # Check if there are any forwarding records for this CA
            cursor = await self.connection.execute("""
                SELECT 1 FROM ca_forwarding_log
                WHERE ca = ? AND forwarded_at > ?
                LIMIT 1
            """, (ca, expiry_time))

            result = await cursor.fetchone()
            return result is not None

        except Exception as e:
            logger.error(f"Failed to check if CA was forwarded: {e}")
            # If we can't check, assume it was forwarded to be safe
            return True

    async def log_ca_forwarding(self, ca: str, source_group_id: int, destination: str, success: bool = True):
        """Log that a CA was forwarded to a destination."""
        try:
            await self.connection.execute("""
                INSERT INTO ca_forwarding_log (ca, source_group_id, destination, success)
                VALUES (?, ?, ?, ?)
            """, (ca, source_group_id, destination, success))
            await self.connection.commit()

        except Exception as e:
            logger.error(f"Failed to log CA forwarding: {e}")
    
    async def mark_ca_processed(self, ca: str):
        """Mark a contract address as processed."""
        try:
            await self.connection.execute("""
                INSERT OR REPLACE INTO ca_cache (ca, last_seen, hit_count)
                VALUES (?, CURRENT_TIMESTAMP, 
                    COALESCE((SELECT hit_count + 1 FROM ca_cache WHERE ca = ?), 1))
            """, (ca, ca))
            
            await self.connection.commit()
            
        except Exception as e:
            logger.error(f"Failed to mark CA as processed: {e}")
    

    
    async def cleanup_old_cache(self):
        """Clean up old cache entries."""
        try:
            expiry_time = datetime.now() - timedelta(hours=config.cache.ca_expiry_hours)
            
            # Clean old CA cache entries
            await self.connection.execute("""
                DELETE FROM ca_cache WHERE last_seen < ?
            """, (expiry_time,))
            
            # Clean old message cache entries
            await self.connection.execute("""
                DELETE FROM message_cache WHERE timestamp < ?
            """, (expiry_time,))
            
            await self.connection.commit()
            logger.debug("Cache cleanup completed")
            
        except Exception as e:
            logger.error(f"Failed to cleanup cache: {e}")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get bot statistics."""
        try:
            stats = {}
            
            # Total CAs processed
            cursor = await self.connection.execute("""
                SELECT COUNT(*) as total FROM contract_addresses
            """)
            result = await cursor.fetchone()
            stats['total_cas'] = result['total'] if result else 0
            
            # CAs processed today
            today = datetime.now().date()
            cursor = await self.connection.execute("""
                SELECT COUNT(*) as today FROM contract_addresses 
                WHERE DATE(first_seen) = ?
            """, (today,))
            result = await cursor.fetchone()
            stats['cas_today'] = result['today'] if result else 0
            
            # Average multiplier
            cursor = await self.connection.execute("""
                SELECT AVG(multiplier) as avg_multiplier FROM pnl_tracking 
                WHERE multiplier IS NOT NULL
            """)
            result = await cursor.fetchone()
            stats['avg_multiplier'] = result['avg_multiplier'] if result else 0
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}

    async def add_ca_mention(self, ca: str, group_id: int, group_name: str, message_id: int):
        """Add a CA mention for trending analysis."""
        try:
            await self.connection.execute("""
                INSERT INTO ca_mentions (ca, group_id, group_name, message_id)
                VALUES (?, ?, ?, ?)
            """, (ca, group_id, group_name, message_id))
            await self.connection.commit()

        except Exception as e:
            logger.error(f"Error adding CA mention: {e}")

    async def add_trending_result(self, ca: str, group_id: int, group_name: str,
                                 first_mention, trending_qualified, mention_count: int,
                                 time_to_trend_seconds: float):
        """Add trending analysis result."""
        try:
            await self.connection.execute("""
                INSERT INTO trending_analysis
                (ca, group_id, group_name, first_mention, trending_qualified,
                 mention_count, time_to_trend_seconds, is_trending)
                VALUES (?, ?, ?, ?, ?, ?, ?, TRUE)
            """, (ca, group_id, group_name, first_mention, trending_qualified,
                  mention_count, time_to_trend_seconds))
            await self.connection.commit()

        except Exception as e:
            logger.error(f"Error adding trending result: {e}")

    async def get_trending_stats(self, hours: int = 24) -> dict:
        """Get trending analysis statistics."""
        try:
            since_time = datetime.now() - timedelta(hours=hours)

            # Total mentions
            cursor = await self.connection.execute("""
                SELECT COUNT(*) as total_mentions FROM ca_mentions
                WHERE timestamp > ?
            """, (since_time,))
            result = await cursor.fetchone()
            total_mentions = result['total_mentions'] if result else 0

            # Trending qualified
            cursor = await self.connection.execute("""
                SELECT COUNT(*) as trending_count FROM trending_analysis
                WHERE timestamp > ?
            """, (since_time,))
            result = await cursor.fetchone()
            trending_count = result['trending_count'] if result else 0

            # Average time to trend
            cursor = await self.connection.execute("""
                SELECT AVG(time_to_trend_seconds) as avg_time_to_trend FROM trending_analysis
                WHERE timestamp > ? AND time_to_trend_seconds IS NOT NULL
            """, (since_time,))
            result = await cursor.fetchone()
            avg_time_to_trend = result['avg_time_to_trend'] if result else 0

            return {
                'total_mentions': total_mentions,
                'trending_qualified': trending_count,
                'avg_time_to_trend_seconds': avg_time_to_trend or 0,
                'trending_rate': (trending_count / total_mentions * 100) if total_mentions > 0 else 0
            }

        except Exception as e:
            logger.error(f"Error getting trending stats: {e}")
            return {}

    async def close(self):
        """Close database connection."""
        if self.connection:
            await self.connection.close()
            logger.info("Database connection closed")
