"""Refactored CLA v2.0 Bot with improved architecture and dependency injection."""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from telethon import events
from loguru import logger

from src.dependency_container import dependency_container
from src.group_manager import group_manager
from src.enhanced_stats_tracker import enhanced_stats
from config import config

class RefactoredCLABot:
    """Refactored CLA v2.0 Bot with improved architecture and single responsibilities."""
    
    def __init__(self):
        """Initialize refactored bot with dependency injection."""
        self.container = dependency_container
        self.running = False
        
        # Component references (will be set after initialization)
        self.message_processor = None
        self.ca_analyzer = None
        self.forwarding_manager = None
        self.telegram_client = None
        
        # Statistics
        self.stats = {
            'start_time': None,
            'uptime_seconds': 0,
            'total_messages': 0,
            'component_errors': 0,
            'restart_count': 0
        }
        
        logger.info("Refactored CLA Bot initialized")
    
    async def initialize(self):
        """Initialize bot with all dependencies."""
        try:
            logger.info("🚀 Initializing Refactored CLA v2.0 Bot...")
            
            # Initialize dependency container
            await self.container.initialize()
            
            # Get component references
            self.message_processor = self.container.get('message_processor')
            self.ca_analyzer = self.container.get('ca_analyzer')
            self.forwarding_manager = self.container.get('forwarding_manager')
            self.telegram_client = self.container.get('telegram_client')
            
            # Verify critical components
            if not all([self.message_processor, self.ca_analyzer, self.forwarding_manager, self.telegram_client]):
                raise RuntimeError("Critical components not initialized")
            
            logger.info("✅ Refactored CLA Bot initialization complete")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize refactored bot: {e}")
            raise
    
    async def start(self):
        """Start the refactored bot."""
        try:
            if self.running:
                logger.warning("Bot is already running")
                return
            
            logger.info("🚀 Starting Refactored CLA v2.0 Bot...")
            self.stats['start_time'] = datetime.now()
            
            # Start Telegram client
            await self.telegram_client.start()
            
            # Set up message handler
            await self._setup_message_handler()
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.running = True
            
            # Log startup information
            await self._log_startup_info()
            
            logger.info("✅ Refactored CLA v2.0 Bot started successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to start refactored bot: {e}")
            raise
    
    async def _setup_message_handler(self):
        """Set up Telegram message handler."""
        try:
            # Get monitored groups
            monitored_groups = list(group_manager.get_all_monitored_groups())
            
            if not monitored_groups:
                raise RuntimeError("No groups configured for monitoring")
            
            # Set up message handler for monitored groups
            @self.telegram_client.client.on(events.NewMessage(chats=monitored_groups))
            async def handle_message(event):
                try:
                    self.stats['total_messages'] += 1
                    await self.message_processor.process_message(event)
                except Exception as e:
                    self.stats['component_errors'] += 1
                    logger.error(f"Error in message handler: {e}")
            
            logger.info(f"✅ Message handler set up for {len(monitored_groups)} groups")
            
        except Exception as e:
            logger.error(f"❌ Failed to setup message handler: {e}")
            raise
    
    async def _start_background_tasks(self):
        """Start background maintenance tasks."""
        try:
            # Start periodic cleanup task
            asyncio.create_task(self._periodic_cleanup_task())
            
            # Start status reporting task
            asyncio.create_task(self._status_reporting_task())
            
            # Start health monitoring task
            asyncio.create_task(self._health_monitoring_task())
            
            logger.info("✅ Background tasks started")
            
        except Exception as e:
            logger.error(f"❌ Failed to start background tasks: {e}")
            raise
    
    async def _periodic_cleanup_task(self):
        """Periodic cleanup of expired data."""
        while self.running:
            try:
                await asyncio.sleep(300)  # 5 minutes
                
                # Cleanup CA analyzer data
                await self.ca_analyzer.cleanup_expired_data()
                
                # Cleanup message processor
                await self.message_processor.cleanup()
                
                logger.debug("🧹 Periodic cleanup completed")
                
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _status_reporting_task(self):
        """Periodic status reporting."""
        while self.running:
            try:
                await asyncio.sleep(3600)  # 1 hour
                
                # Generate and send status report
                if enhanced_stats.should_send_hourly_report():
                    report = enhanced_stats.generate_hourly_report()
                    await self.forwarding_manager.send_status_report(report)
                
                logger.debug("📊 Status report sent")
                
            except Exception as e:
                logger.error(f"Error in status reporting: {e}")
                await asyncio.sleep(300)  # Wait before retrying
    
    async def _health_monitoring_task(self):
        """Monitor component health."""
        while self.running:
            try:
                await asyncio.sleep(600)  # 10 minutes
                
                # Perform health check
                health_status = await self.container.health_check()
                
                # Log unhealthy components
                unhealthy = [name for name, status in health_status.items() if not status]
                if unhealthy:
                    logger.warning(f"⚠️ Unhealthy components: {unhealthy}")
                else:
                    logger.debug("✅ All components healthy")
                
            except Exception as e:
                logger.error(f"Error in health monitoring: {e}")
                await asyncio.sleep(300)  # Wait before retrying
    
    async def _log_startup_info(self):
        """Log startup information."""
        try:
            logger.info("🔍 REFACTORED BOT STARTUP INFO:")
            logger.info(f"   Start time: {self.stats['start_time']}")
            logger.info(f"   Telegram client connected: {self.telegram_client.client.is_connected()}")
            
            # Log monitored groups
            logger.info("🔍 MONITORED GROUPS:")
            for group_id in group_manager.get_all_monitored_groups():
                group_name = group_manager.get_group_name(group_id)
                group_type = group_manager.get_group_type(group_id)
                logger.info(f"   - {group_name} ({group_id}) [{group_type}]")
            
            # Log component status
            component_stats = self.container.get_component_stats()
            logger.info(f"🔍 COMPONENTS INITIALIZED: {len(component_stats)}")
            
            logger.info("🔍 Waiting for messages from monitored groups...")
            
        except Exception as e:
            logger.error(f"Error logging startup info: {e}")
    
    async def stop(self):
        """Stop the refactored bot."""
        try:
            logger.info("🛑 Stopping Refactored CLA v2.0 Bot...")
            
            self.running = False
            
            # Stop Telegram client
            if self.telegram_client:
                await self.telegram_client.stop()
            
            # Cleanup dependencies
            await self.container.cleanup()
            
            # Update stats
            if self.stats['start_time']:
                self.stats['uptime_seconds'] = (datetime.now() - self.stats['start_time']).total_seconds()
            
            logger.info("✅ Refactored CLA v2.0 Bot stopped")
            
        except Exception as e:
            logger.error(f"❌ Error stopping refactored bot: {e}")
    
    async def restart(self):
        """Restart the refactored bot."""
        try:
            logger.info("🔄 Restarting Refactored CLA v2.0 Bot...")
            
            await self.stop()
            await asyncio.sleep(5)  # Wait before restart
            await self.initialize()
            await self.start()
            
            self.stats['restart_count'] += 1
            logger.info("✅ Refactored CLA v2.0 Bot restarted successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to restart refactored bot: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive bot statistics."""
        uptime = 0
        if self.stats['start_time']:
            uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        
        return {
            'bot_stats': {
                **self.stats,
                'uptime_seconds': uptime,
                'running': self.running
            },
            'component_stats': self.container.get_component_stats() if self.container.is_initialized() else {},
            'group_stats': group_manager.get_group_summary(),
            'enhanced_stats': enhanced_stats.get_current_stats()
        }
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status."""
        try:
            health_status = {
                'bot_running': self.running,
                'telegram_connected': self.telegram_client.client.is_connected() if self.telegram_client else False,
                'components': await self.container.health_check() if self.container.is_initialized() else {},
                'uptime_seconds': (datetime.now() - self.stats['start_time']).total_seconds() if self.stats['start_time'] else 0
            }
            
            # Overall health
            all_healthy = (
                health_status['bot_running'] and 
                health_status['telegram_connected'] and 
                all(health_status['components'].values())
            )
            health_status['overall_healthy'] = all_healthy
            
            return health_status
            
        except Exception as e:
            logger.error(f"Error getting health status: {e}")
            return {'error': str(e), 'overall_healthy': False}
    
    async def run_forever(self):
        """Run the bot forever with automatic restart on failure."""
        while True:
            try:
                await self.initialize()
                await self.start()
                
                # Keep running until stopped
                while self.running:
                    await asyncio.sleep(1)
                
                break  # Normal shutdown
                
            except Exception as e:
                logger.error(f"❌ Bot crashed: {e}")
                self.stats['restart_count'] += 1
                
                # Wait before restart
                await asyncio.sleep(30)
                logger.info("🔄 Attempting automatic restart...")
                
                try:
                    await self.stop()
                except:
                    pass  # Ignore cleanup errors during crash recovery
