# 🚀 CLA v2.0 Bot Setup Guide

## ✅ Installation Complete!

Your CLA v2.0 Telegram bot is now installed and ready for configuration.

## 📋 Quick Setup Checklist

### 1. ✅ Dependencies Installed
- All Python packages are installed
- Core functionality tested and working

### 2. 🔑 Get Telegram API Credentials

**Step 1:** Go to https://my.telegram.org/apps
**Step 2:** Log in with your phone number
**Step 3:** Create a new application:
- App title: `CLA v2.0 Bot`
- Short name: `cla-v2-bot`
- Platform: `Desktop`
- Description: `Memecoin signal monitoring bot`

**Step 4:** Copy your credentials:
- `api_id` (numeric)
- `api_hash` (string)

### 3. 📝 Configure Environment

**Step 1:** Copy the example environment file:
```bash
copy .env.example .env
```

**Step 2:** Edit `.env` with your credentials:
```env
# Telegram API Credentials (REQUIRED)
TELEGRAM_API_ID=your_api_id_here
TELEGRAM_API_HASH=your_api_hash_here
TELEGRAM_PHONE=your_phone_number_here

# BonkBot Settings (OPTIONAL - for auto-trading)
BONKBOT_USERNAME=@BonkBot_bot
BONKBOT_CHAT_ID=your_bonkbot_chat_id

# Other settings (use defaults)
CA_CACHE_EXPIRY_HOURS=72
MESSAGE_RATE_LIMIT=30
LOG_LEVEL=INFO
```

### 4. 🤖 Setup BonkBot (Optional)

If you want auto-trading:

**Step 1:** Start a chat with @BonkBot_bot on Telegram
**Step 2:** Get your chat ID:
- Send a message to BonkBot
- Use a bot like @userinfobot to get your chat ID
- Add it to your `.env` file

### 5. 🎯 Join Target Group

Make sure your Telegram account is a member of:
- **Group:** FREE WHALE SIGNALS
- **Chat ID:** -*************

## 🚀 Running the Bot

### Start the Bot
```bash
python main.py
```

### First Run Authentication
1. Enter the verification code sent to your phone
2. If 2FA is enabled, enter your password
3. Bot will save the session for future runs

### Expected Output
```
2025-07-25 22:00:00 | INFO | Starting CLA v2.0 Bot...
2025-07-25 22:00:01 | INFO | Database initialized successfully
2025-07-25 22:00:02 | INFO | Connected as: Your Name (@yourusername)
2025-07-25 22:00:03 | INFO | BonkBot integration initialized: @BonkBot_bot
2025-07-25 22:00:04 | INFO | Bot started successfully. Monitoring for signals...
```

## 📊 Monitoring

### Real-time Logs
The bot will log:
- Messages processed
- Contract addresses detected
- BonkBot forwards
- PNL updates
- Errors and warnings

### Log Files
- **Console:** Real-time colored output
- **Main log:** `./logs/cla_bot.log`
- **Error log:** `./logs/error.log`

### Statistics
Every 100 messages, you'll see:
```
📊 Bot Statistics - Uptime: 1:23:45
   Messages processed: 500
   CAs detected: 12
   CAs sent to BonkBot: 12
   PNL updates: 8
   Total CAs in DB: 45
   CAs today: 12
```

## 🔧 Configuration Options

### Cache Settings
```env
CA_CACHE_EXPIRY_HOURS=72    # How long to remember processed CAs
MAX_CACHE_SIZE=10000        # Maximum cache entries
```

### Rate Limiting
```env
MESSAGE_RATE_LIMIT=30       # Messages per minute
API_RATE_LIMIT=20          # API calls per minute
```

### Logging
```env
LOG_LEVEL=INFO             # DEBUG, INFO, WARNING, ERROR
LOG_FILE=./logs/cla_bot.log
```

## 🛠️ Troubleshooting

### Common Issues

**"Authentication Failed"**
- Check your API credentials in `.env`
- Verify phone number format: `+**********`
- Make sure 2FA password is correct

**"Group Access Denied"**
- Join the FREE WHALE SIGNALS group first
- Make sure your account has message history access
- Check the group ID is correct

**"BonkBot Not Responding"**
- Verify BonkBot username/chat_id in `.env`
- Test with a manual message to BonkBot first
- Make sure BonkBot is online and responding

**"No CAs Detected"**
- Check if messages contain valid Solana addresses
- Verify the message format matches expected patterns
- Look at debug logs for parsing details

### Debug Mode
For detailed debugging, set:
```env
LOG_LEVEL=DEBUG
```

### Test Core Functionality
```bash
python test_core_functionality.py
```

## 📈 Performance Tips

### Optimal Settings
- Keep `CA_CACHE_EXPIRY_HOURS` at 72 for best duplicate detection
- Use `MESSAGE_RATE_LIMIT=30` to avoid Telegram limits
- Set `LOG_LEVEL=INFO` for production use

### Database Maintenance
The bot automatically:
- Cleans old cache entries every hour
- Removes PNL data older than 30 days
- Optimizes database performance

## 🔒 Security

### Best Practices
- Keep your `.env` file secure and private
- Don't share your API credentials
- Use a dedicated Telegram account if possible
- Monitor logs for unusual activity

### Session Security
- Session files are encrypted by Telethon
- Delete session files to force re-authentication
- Sessions expire automatically if unused

## 📞 Support

### Getting Help
1. Check the logs for error details
2. Review this setup guide
3. Test with `python test_core_functionality.py`
4. Check Telegram API status

### Useful Commands
```bash
# Test core functionality
python test_core_functionality.py

# Run message parser tests only
python -m unittest tests.test_message_parser -v

# Check dependencies
pip list | grep -E "(telethon|aiohttp|loguru)"

# View recent logs
tail -f logs/cla_bot.log
```

## 🎉 You're Ready!

Your CLA v2.0 bot is now configured and ready to monitor memecoin signals!

**Next Steps:**
1. Configure your `.env` file with Telegram credentials
2. Run `python main.py`
3. Complete the authentication process
4. Watch the bot monitor for signals automatically

The bot will:
- ✅ Monitor FREE WHALE SIGNALS group 24/7
- ✅ Extract contract addresses from messages
- ✅ Send CAs to BonkBot for auto-trading
- ✅ Track PNL performance data
- ✅ Prevent duplicate processing
- ✅ Handle errors gracefully

Happy trading! 🚀
