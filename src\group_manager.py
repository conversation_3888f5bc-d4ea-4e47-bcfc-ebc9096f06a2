"""Centralized group classification and management for CLA v2.0 Bot."""

from typing import Dict, Set, Optional
from loguru import logger
from config import config

class GroupManager:
    """Centralized group classification and management."""
    
    def __init__(self):
        """Initialize group manager with configuration as single source of truth."""
        
        # Load group classifications from configuration
        self._load_group_classifications()
        
        # Group name mappings for display purposes
        self._initialize_group_names()
        
        logger.info("Group Manager initialized with centralized configuration")
        logger.info(f"High-volume groups: {len(self.high_volume_groups)}")
        logger.info(f"Low-volume groups: {len(self.low_volume_groups)}")
        
        # Validate configuration
        self._validate_configuration()
    
    def _load_group_classifications(self):
        """Load group classifications from configuration."""
        
        # High-volume groups (require trending analysis)
        self.high_volume_groups: Set[int] = set()
        if hasattr(config.trending, 'high_volume_groups') and config.trending.high_volume_groups:
            self.high_volume_groups = set(config.trending.high_volume_groups)
        else:
            # Fallback to default high-volume groups if not configured
            self.high_volume_groups = {
                -1002333406905,  # 🚀 MEME 1000X
                -1002202241417   # 🧠 GMGN Featured Signals
            }
            logger.warning("Using default high-volume groups - configure TRENDING_HIGH_VOLUME_GROUPS")
        
        # Low-volume groups (direct forwarding)
        self.low_volume_groups: Set[int] = set()
        if hasattr(config.trending, 'low_volume_groups') and config.trending.low_volume_groups:
            self.low_volume_groups = set(config.trending.low_volume_groups)
        else:
            # Fallback to default low-volume groups if not configured
            self.low_volume_groups = {
                -1002380594298,  # FREE WHALE SIGNALS
                -1002270988204,  # Solana Activity Tracker
                -1002064145465,  # 🌹 MANIFEST
                -1001763265784,  # 👤 Mark Degens
                -1002139128702   # 💎 FINDERTRENDING
            }
            logger.warning("Using default low-volume groups - configure TRENDING_LOW_VOLUME_GROUPS")
    
    def _initialize_group_names(self):
        """Initialize group name mappings for display purposes."""
        
        # Default group name mappings
        self.group_names: Dict[int, str] = {
            # High-volume groups
            -1002333406905: "🚀 MEME 1000X",
            -1002202241417: "🧠 GMGN Featured Signals",
            
            # Low-volume groups
            -1002380594298: "FREE WHALE SIGNALS",
            -1002270988204: "Solana Activity Tracker",
            -1002064145465: "🌹 MANIFEST",
            -1001763265784: "👤 Mark Degens",
            -1002139128702: "💎 FINDERTRENDING"
        }
        
        # Add any additional groups from configuration
        all_groups = self.high_volume_groups | self.low_volume_groups
        for group_id in all_groups:
            if group_id not in self.group_names:
                self.group_names[group_id] = f"Group {group_id}"
    
    def _validate_configuration(self):
        """Validate group configuration for consistency."""
        
        # Check for overlapping groups
        overlap = self.high_volume_groups & self.low_volume_groups
        if overlap:
            logger.error(f"Groups cannot be both high-volume and low-volume: {overlap}")
            raise ValueError(f"Overlapping group classifications: {overlap}")
        
        # Check for empty classifications
        if not self.high_volume_groups and not self.low_volume_groups:
            logger.error("No groups configured for monitoring")
            raise ValueError("At least one group must be configured")
        
        # Log configuration summary
        logger.info("Group configuration validated successfully")
        for group_id in self.high_volume_groups:
            logger.info(f"  🔥 HIGH-VOLUME: {self.get_group_name(group_id)} ({group_id})")
        for group_id in self.low_volume_groups:
            logger.info(f"  ⚡ LOW-VOLUME: {self.get_group_name(group_id)} ({group_id})")
    
    def is_high_volume_group(self, group_id: int) -> bool:
        """Check if group is classified as high-volume (requires trending analysis)."""
        return group_id in self.high_volume_groups
    
    def is_low_volume_group(self, group_id: int) -> bool:
        """Check if group is classified as low-volume (direct forwarding)."""
        return group_id in self.low_volume_groups
    
    def is_monitored_group(self, group_id: int) -> bool:
        """Check if group is monitored (either high-volume or low-volume)."""
        return group_id in self.high_volume_groups or group_id in self.low_volume_groups
    
    def get_group_name(self, group_id: int) -> str:
        """Get display name for a group."""
        return self.group_names.get(group_id, f"Unknown Group ({group_id})")
    
    def get_group_type(self, group_id: int) -> str:
        """Get group type as string for logging/display."""
        if self.is_high_volume_group(group_id):
            return "HIGH-VOLUME"
        elif self.is_low_volume_group(group_id):
            return "LOW-VOLUME"
        else:
            return "UNMONITORED"
    
    def get_all_monitored_groups(self) -> Set[int]:
        """Get all monitored group IDs."""
        return self.high_volume_groups | self.low_volume_groups
    
    def get_high_volume_groups(self) -> Set[int]:
        """Get high-volume group IDs."""
        return self.high_volume_groups.copy()
    
    def get_low_volume_groups(self) -> Set[int]:
        """Get low-volume group IDs."""
        return self.low_volume_groups.copy()
    
    def get_group_summary(self) -> Dict:
        """Get summary of group classifications."""
        return {
            'high_volume_count': len(self.high_volume_groups),
            'low_volume_count': len(self.low_volume_groups),
            'total_monitored': len(self.get_all_monitored_groups()),
            'high_volume_groups': {
                group_id: self.get_group_name(group_id) 
                for group_id in self.high_volume_groups
            },
            'low_volume_groups': {
                group_id: self.get_group_name(group_id) 
                for group_id in self.low_volume_groups
            }
        }
    
    def reload_configuration(self):
        """Reload group classifications from configuration."""
        logger.info("Reloading group configuration...")
        
        # Store old configuration for comparison
        old_high_volume = self.high_volume_groups.copy()
        old_low_volume = self.low_volume_groups.copy()
        
        # Reload from configuration
        self._load_group_classifications()
        self._initialize_group_names()
        self._validate_configuration()
        
        # Log changes
        added_high = self.high_volume_groups - old_high_volume
        removed_high = old_high_volume - self.high_volume_groups
        added_low = self.low_volume_groups - old_low_volume
        removed_low = old_low_volume - self.low_volume_groups
        
        if added_high or removed_high or added_low or removed_low:
            logger.info("Group configuration changes detected:")
            if added_high:
                logger.info(f"  Added high-volume: {added_high}")
            if removed_high:
                logger.info(f"  Removed high-volume: {removed_high}")
            if added_low:
                logger.info(f"  Added low-volume: {added_low}")
            if removed_low:
                logger.info(f"  Removed low-volume: {removed_low}")
        else:
            logger.info("No group configuration changes detected")

# Global instance - single source of truth for group classifications
group_manager = GroupManager()
