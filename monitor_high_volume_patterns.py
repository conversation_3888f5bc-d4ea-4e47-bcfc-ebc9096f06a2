"""Monitor and analyze high-volume group CA detection patterns."""

import time
import sys
import os
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

def display_header():
    """Display monitoring header."""
    print("\033[2J\033[H")  # Clear screen
    print("🔍 High-Volume Group CA Pattern Monitor")
    print("=" * 70)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Press Ctrl+C to exit")
    print("🧠 GMGN Featured Signals | 🚀 MEME 1000X (5min delay expected)")
    print("=" * 70)

def display_current_analysis():
    """Display current analysis from the high-volume analyzer."""
    try:
        from src.high_volume_ca_analyzer import high_volume_analyzer
        
        # Get comprehensive report
        report = high_volume_analyzer.get_comprehensive_report()
        print(report)
        
        # Show recent detections
        recent_detections = sorted(
            high_volume_analyzer.ca_detections, 
            key=lambda d: d.timestamp, 
            reverse=True
        )[:10]
        
        if recent_detections:
            print("\n📊 Recent High-Volume Detections:")
            print("-" * 50)
            for detection in recent_detections:
                time_str = detection.timestamp.strftime('%H:%M:%S')
                trending_status = "✅ TRENDING" if detection.trending_qualified else "❌ FILTERED"
                group_emoji = "🧠" if detection.group_id == -1002202241417 else "🚀"
                
                print(f"   {time_str} | {group_emoji} {detection.group_name}")
                print(f"      CA: {detection.ca[:30]}...")
                print(f"      Status: {trending_status} | Count: {detection.mention_count}")
                print()
        
        # Show cross-group patterns
        cross_patterns = [p for p in high_volume_analyzer.cross_group_patterns.values() 
                         if p.time_difference is not None]
        
        if cross_patterns:
            print("\n🔄 Cross-Group Patterns:")
            print("-" * 50)
            recent_patterns = sorted(cross_patterns, 
                                   key=lambda p: p.gmgn_detection.timestamp if p.gmgn_detection else datetime.min, 
                                   reverse=True)[:5]
            
            for pattern in recent_patterns:
                if pattern.gmgn_detection and pattern.meme_1000x_detection:
                    gmgn_time = pattern.gmgn_detection.timestamp.strftime('%H:%M:%S')
                    meme_time = pattern.meme_1000x_detection.timestamp.strftime('%H:%M:%S')
                    delay = pattern.time_difference
                    
                    print(f"   CA: {pattern.ca[:30]}...")
                    print(f"   🧠 GMGN: {gmgn_time} → 🚀 MEME: {meme_time} | Delay: {delay:.0f}s")
                    print(f"   Trending: GMGN={pattern.gmgn_detection.trending_qualified} | MEME={pattern.meme_1000x_detection.trending_qualified}")
                    print()
        
    except Exception as e:
        print(f"❌ Error displaying analysis: {e}")

def monitor_real_time():
    """Monitor high-volume patterns in real-time."""
    print("🔍 Starting High-Volume Pattern Monitoring...")
    print("Tracking GMGN Featured Signals and MEME 1000X CA patterns")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            display_header()
            display_current_analysis()
            
            print("\n" + "=" * 70)
            print("🔄 Refreshing in 30 seconds... (Ctrl+C to exit)")
            
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
        
        # Save analysis data before exit
        try:
            from src.high_volume_ca_analyzer import high_volume_analyzer
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filepath = f'data/high_volume_analysis_{timestamp}.json'
            high_volume_analyzer.save_analysis_data(filepath)
            print(f"📊 Analysis data saved to {filepath}")
        except Exception as e:
            print(f"⚠️ Error saving analysis data: {e}")
            
    except Exception as e:
        print(f"❌ Monitoring error: {e}")

def generate_analysis_report():
    """Generate and display analysis report."""
    try:
        from src.high_volume_ca_analyzer import high_volume_analyzer
        
        print("🔍 High-Volume Group Analysis Report")
        print("=" * 70)
        
        report = high_volume_analyzer.get_comprehensive_report()
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f'data/high_volume_report_{timestamp}.txt'
        
        os.makedirs('data', exist_ok=True)
        with open(report_file, 'w') as f:
            f.write(report)
        
        print(f"\n📄 Report saved to {report_file}")
        
        # Save analysis data
        data_file = f'data/high_volume_analysis_{timestamp}.json'
        high_volume_analyzer.save_analysis_data(data_file)
        print(f"📊 Analysis data saved to {data_file}")
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")

def analyze_trending_effectiveness():
    """Analyze trending filter effectiveness."""
    try:
        from src.high_volume_ca_analyzer import high_volume_analyzer
        
        print("🎯 Trending Filter Effectiveness Analysis")
        print("=" * 70)
        
        effectiveness_report = high_volume_analyzer.get_trending_effectiveness_report()
        print(effectiveness_report)
        
        # Detailed analysis of filtered CAs
        filtered_cas = [analysis for analysis in high_volume_analyzer.trending_analyses.values() 
                       if analysis.filtered_as_noise and not analysis.qualified_for_trending]
        
        if filtered_cas:
            print(f"\n📋 Detailed Analysis of Filtered CAs ({len(filtered_cas)} total):")
            print("-" * 50)
            
            # Sort by cross-group appearances and total mentions
            sorted_filtered = sorted(filtered_cas, 
                                   key=lambda a: (a.cross_group_appearances, a.total_mentions), 
                                   reverse=True)
            
            for i, analysis in enumerate(sorted_filtered[:10], 1):
                print(f"{i:2d}. {analysis.ca[:25]}...")
                print(f"    Mentions: {analysis.total_mentions} | Cross-group: {analysis.cross_group_appearances}")
                print(f"    First: {analysis.first_mention.strftime('%H:%M:%S')} | Last: {analysis.last_mention.strftime('%H:%M:%S')}")
                
                # Check if this appeared in both groups
                if analysis.ca in high_volume_analyzer.cross_group_patterns:
                    pattern = high_volume_analyzer.cross_group_patterns[analysis.ca]
                    if pattern.time_difference is not None:
                        print(f"    ⚠️ POTENTIAL RUNNER: Appeared in both groups with {pattern.time_difference:.0f}s delay")
                print()
        
        # Recommendations
        print("\n💡 Filter Optimization Recommendations:")
        print("-" * 50)
        
        cross_group_filtered = len([a for a in filtered_cas if a.cross_group_appearances > 1])
        total_detections = len(high_volume_analyzer.trending_analyses)
        
        if total_detections > 0:
            filter_rate = (len(filtered_cas) / total_detections) * 100
            cross_group_rate = (cross_group_filtered / total_detections) * 100 if total_detections > 0 else 0
            
            print(f"Current filter rate: {filter_rate:.1f}% of CAs filtered as noise")
            print(f"Cross-group filtered: {cross_group_rate:.1f}% (potential missed opportunities)")
            
            if cross_group_rate > 10:  # More than 10% cross-group filtered
                print("⚠️ RECOMMENDATION: Consider reducing threshold from 6 to 4-5 mentions")
                print("   High cross-group filter rate suggests threshold may be too strict")
            elif cross_group_rate < 2:  # Less than 2% cross-group filtered
                print("✅ RECOMMENDATION: Current 6-mention threshold appears optimal")
                print("   Low cross-group filter rate indicates good balance")
            else:
                print("📊 RECOMMENDATION: Monitor for more data before adjusting threshold")
                print("   Current performance within acceptable range")
        
    except Exception as e:
        print(f"❌ Error analyzing effectiveness: {e}")

def main():
    """Main monitoring function."""
    if len(sys.argv) > 1:
        if sys.argv[1] == 'report':
            generate_analysis_report()
        elif sys.argv[1] == 'effectiveness':
            analyze_trending_effectiveness()
        elif sys.argv[1] == 'monitor':
            monitor_real_time()
        else:
            print("❌ Unknown command. Use: report, effectiveness, or monitor")
    else:
        print("🔍 High-Volume Group CA Pattern Analyzer")
        print("\nOptions:")
        print("  python monitor_high_volume_patterns.py monitor        - Real-time monitoring")
        print("  python monitor_high_volume_patterns.py report         - Generate analysis report")
        print("  python monitor_high_volume_patterns.py effectiveness  - Analyze filter effectiveness")
        print()
        
        choice = input("Select option (monitor/report/effectiveness): ").lower()
        if choice == 'monitor':
            monitor_real_time()
        elif choice == 'report':
            generate_analysis_report()
        elif choice == 'effectiveness':
            analyze_trending_effectiveness()
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
