"""Real-time monitoring script for MEME 1000X integration."""

import time
import re
from datetime import datetime

def monitor_meme_1000x_activity():
    """Monitor MEME 1000X activity in real-time."""
    print("🔍 Real-Time MEME 1000X Activity Monitor")
    print("=" * 60)
    print("Monitoring for:")
    print("🚀 MEME 1000X messages received")
    print("🔥 High-volume group processing")
    print("📊 Trending analysis application")
    print("🛡️ Anti-pump protection activation")
    print("📤 Forwarding decisions")
    print("🔄 Global duplicate prevention")
    print("=" * 60)
    
    # Patterns to watch for
    patterns = {
        'meme_1000x_message': r'🚀 MEME 1000X.*MESSAGE',
        'high_volume_processing': r'🔥 HIGH-VOLUME.*PROCESSING.*-1002333406905',
        'trending_analysis': r'🔥 HIGH-VOLUME TRENDING.*MEME 1000X',
        'trending_result': r'🔥 HIGH-VOLUME RESULT.*-1002333406905',
        'pump_protection': r'🚫 PUMP SCHEME BLOCKED.*-1002333406905',
        'trending_qualified': r'🔥 HIGH-VOLUME QUALIFIED.*MEME 1000X',
        'forwarding': r'Sent.*CAs to.*MEME 1000X',
        'duplicate_prevention': r'DUPLICATE.*already in cache',
        'noise_filtered': r'🔥 HIGH-VOLUME NOISE FILTERED'
    }
    
    print(f"Started monitoring at {datetime.now().strftime('%H:%M:%S')}")
    print("Waiting for MEME 1000X activity...\n")
    
    try:
        # Read the log file in real-time
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            # Go to end of file
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if not line:
                    time.sleep(0.1)
                    continue
                
                line = line.strip()
                timestamp = datetime.now().strftime('%H:%M:%S')
                
                # Check for MEME 1000X specific activity
                if '-1002333406905' in line or 'MEME 1000X' in line:
                    print(f"[{timestamp}] 🚀 MEME 1000X ACTIVITY:")
                    print(f"    {line}")
                    
                    # Analyze the type of activity
                    for activity_type, pattern in patterns.items():
                        if re.search(pattern, line, re.IGNORECASE):
                            print(f"    📊 Activity Type: {activity_type.replace('_', ' ').title()}")
                            break
                    print()
                
                # Check for high-volume group processing
                elif '🔥 HIGH-VOLUME' in line:
                    print(f"[{timestamp}] 🔥 HIGH-VOLUME PROCESSING:")
                    print(f"    {line}")
                    print()
                
                # Check for trending analysis
                elif 'TRENDING' in line and ('GMGN' in line or 'MEME' in line):
                    print(f"[{timestamp}] 📊 TRENDING ANALYSIS:")
                    print(f"    {line}")
                    print()
                
                # Check for anti-pump protection
                elif '🚫 PUMP SCHEME BLOCKED' in line:
                    print(f"[{timestamp}] 🛡️ ANTI-PUMP PROTECTION:")
                    print(f"    {line}")
                    print()
                
                # Check for forwarding activity
                elif 'Sent' in line and 'CAs to' in line:
                    print(f"[{timestamp}] 📤 FORWARDING:")
                    print(f"    {line}")
                    print()
                
                # Check for duplicate prevention
                elif 'DUPLICATE' in line and 'already in cache' in line:
                    print(f"[{timestamp}] 🔄 DUPLICATE PREVENTION:")
                    print(f"    {line}")
                    print()
                
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoring stopped at {datetime.now().strftime('%H:%M:%S')}")
    except FileNotFoundError:
        print("❌ Log file not found. Make sure the bot is running and logging to logs/cla_bot.log")
    except Exception as e:
        print(f"❌ Error monitoring logs: {e}")

def show_current_status():
    """Show current bot status and MEME 1000X configuration."""
    print("📊 Current Bot Status - MEME 1000X Integration")
    print("=" * 60)
    
    try:
        # Read recent log entries
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
            recent_lines = lines[-50:]  # Last 50 lines
            
            # Look for initialization info
            for line in recent_lines:
                if 'High-volume groups' in line:
                    print(f"✅ {line.strip()}")
                elif 'Low-volume groups' in line:
                    print(f"✅ {line.strip()}")
                elif 'MEME 1000X (-1002333406905) [ACTIVE]' in line:
                    print(f"✅ MEME 1000X monitoring: ACTIVE")
                elif 'Successfully accessed group -1002333406905' in line:
                    print(f"✅ MEME 1000X access: VERIFIED")
                elif 'Selective protection: enabled=True' in line:
                    print(f"✅ Selective protection: ENABLED")
            
            print("\n📋 Expected MEME 1000X Behavior:")
            print("🔥 High-volume group requiring trending analysis")
            print("🛡️ Anti-pump protection: 6 mentions in 8 minutes")
            print("📊 Enhanced filters: 120s spread, 3.0/min velocity, 3min organic growth")
            print("📤 Forwarding: Only trending CAs to BonkBot, CLA v2.0, Monaco PNL")
            print("❌ WINNERS: Globally excluded")
            print("🔄 Global duplicate prevention: 7-day cache across all groups")
            
    except FileNotFoundError:
        print("❌ Log file not found. Bot may not be running.")
    except Exception as e:
        print(f"❌ Error reading status: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'status':
        show_current_status()
    else:
        print("🚀 MEME 1000X Integration Monitor")
        print("\nOptions:")
        print("  python monitor_meme_1000x.py        - Real-time activity monitoring")
        print("  python monitor_meme_1000x.py status - Show current status")
        print()
        
        choice = input("Start real-time monitoring? (y/n): ").lower()
        if choice in ['y', 'yes']:
            monitor_meme_1000x_activity()
        else:
            show_current_status()
