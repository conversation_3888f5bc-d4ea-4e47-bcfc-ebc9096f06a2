"""High-volume group CA detection pattern analyzer for trending filter evaluation."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
from loguru import logger

@dataclass
class CADetection:
    """Single CA detection event."""
    ca: str
    group_id: int
    group_name: str
    timestamp: datetime
    message_id: int
    message_text: str
    trending_qualified: bool
    mention_count: int
    time_to_trend: Optional[float] = None  # seconds

@dataclass
class CrossGroupPattern:
    """Pattern of CA appearances across high-volume groups."""
    ca: str
    gmgn_detection: Optional[CADetection] = None
    meme_1000x_detection: Optional[CADetection] = None
    time_difference: Optional[float] = None  # seconds between detections
    both_qualified: bool = False
    either_qualified: bool = False

@dataclass
class TrendingAnalysis:
    """Analysis of trending filter effectiveness."""
    ca: str
    total_mentions: int
    first_mention: datetime
    last_mention: datetime
    qualified_for_trending: bool
    filtered_as_noise: bool
    cross_group_appearances: int
    potential_runner: bool = False  # To be determined by external analysis

class HighVolumeCAAnalyzer:
    """Analyzer for high-volume group CA detection patterns."""
    
    def __init__(self):
        # High-volume group IDs
        self.gmgn_group_id = -1002202241417  # 🧠 GMGN Featured Signals
        self.meme_1000x_group_id = -1002333406905  # 🚀 MEME 1000X
        
        # Detection tracking
        self.ca_detections: List[CADetection] = []
        self.cross_group_patterns: Dict[str, CrossGroupPattern] = {}
        self.trending_analyses: Dict[str, TrendingAnalysis] = {}
        
        # Timing analysis
        self.meme_1000x_delay = 300  # 5 minutes expected delay
        self.trending_window = 480  # 8 minutes trending window
        
        # Statistics
        self.stats = {
            'total_gmgn_detections': 0,
            'total_meme_1000x_detections': 0,
            'trending_qualified': 0,
            'noise_filtered': 0,
            'cross_group_cas': 0,
            'potential_runners_missed': 0
        }
        
        logger.info("High-volume CA analyzer initialized")
    
    def record_ca_detection(self, ca: str, group_id: int, group_name: str, 
                          message_id: int, message_text: str, trending_qualified: bool, 
                          mention_count: int, time_to_trend: Optional[float] = None):
        """Record a CA detection from high-volume groups."""
        
        # Only track high-volume groups
        if group_id not in [self.gmgn_group_id, self.meme_1000x_group_id]:
            return
        
        detection = CADetection(
            ca=ca,
            group_id=group_id,
            group_name=group_name,
            timestamp=datetime.now(),
            message_id=message_id,
            message_text=message_text[:200],  # Truncate for storage
            trending_qualified=trending_qualified,
            mention_count=mention_count,
            time_to_trend=time_to_trend
        )
        
        self.ca_detections.append(detection)
        
        # Update statistics
        if group_id == self.gmgn_group_id:
            self.stats['total_gmgn_detections'] += 1
        elif group_id == self.meme_1000x_group_id:
            self.stats['total_meme_1000x_detections'] += 1
        
        if trending_qualified:
            self.stats['trending_qualified'] += 1
        else:
            self.stats['noise_filtered'] += 1
        
        # Analyze cross-group patterns
        self._analyze_cross_group_pattern(detection)
        
        # Update trending analysis
        self._update_trending_analysis(detection)
        
        logger.info(f"📊 CA DETECTION RECORDED: {ca} from {group_name} | Trending: {trending_qualified} | Count: {mention_count}")
    
    def _analyze_cross_group_pattern(self, detection: CADetection):
        """Analyze cross-group appearance patterns."""
        ca = detection.ca
        
        if ca not in self.cross_group_patterns:
            self.cross_group_patterns[ca] = CrossGroupPattern(ca=ca)
        
        pattern = self.cross_group_patterns[ca]
        
        # Record detection in appropriate group
        if detection.group_id == self.gmgn_group_id:
            pattern.gmgn_detection = detection
        elif detection.group_id == self.meme_1000x_group_id:
            pattern.meme_1000x_detection = detection
        
        # Calculate timing relationships if both groups have detected this CA
        if pattern.gmgn_detection and pattern.meme_1000x_detection:
            time_diff = (pattern.meme_1000x_detection.timestamp - pattern.gmgn_detection.timestamp).total_seconds()
            pattern.time_difference = time_diff
            
            # Update qualification status
            pattern.both_qualified = (pattern.gmgn_detection.trending_qualified and 
                                    pattern.meme_1000x_detection.trending_qualified)
            pattern.either_qualified = (pattern.gmgn_detection.trending_qualified or 
                                      pattern.meme_1000x_detection.trending_qualified)
            
            # This is a cross-group CA
            if ca not in [p.ca for p in self.cross_group_patterns.values() if p.time_difference is not None]:
                self.stats['cross_group_cas'] += 1
            
            logger.info(f"🔄 CROSS-GROUP PATTERN: {ca} | Time diff: {time_diff:.1f}s | Both qualified: {pattern.both_qualified}")
    
    def _update_trending_analysis(self, detection: CADetection):
        """Update trending analysis for a CA."""
        ca = detection.ca
        
        if ca not in self.trending_analyses:
            self.trending_analyses[ca] = TrendingAnalysis(
                ca=ca,
                total_mentions=0,
                first_mention=detection.timestamp,
                last_mention=detection.timestamp,
                qualified_for_trending=False,
                filtered_as_noise=False,
                cross_group_appearances=0
            )
        
        analysis = self.trending_analyses[ca]
        analysis.total_mentions += 1
        analysis.last_mention = detection.timestamp
        
        if detection.trending_qualified:
            analysis.qualified_for_trending = True
        else:
            analysis.filtered_as_noise = True
        
        # Count cross-group appearances
        if ca in self.cross_group_patterns:
            pattern = self.cross_group_patterns[ca]
            if pattern.gmgn_detection and pattern.meme_1000x_detection:
                analysis.cross_group_appearances = 2
            else:
                analysis.cross_group_appearances = 1
    
    def get_timing_analysis_report(self) -> str:
        """Generate timing analysis report."""
        report = []
        report.append("⏰ High-Volume Group Timing Analysis")
        report.append("=" * 50)
        
        # Cross-group timing patterns
        cross_group_cas = [p for p in self.cross_group_patterns.values() if p.time_difference is not None]
        
        if cross_group_cas:
            report.append(f"\n🔄 Cross-Group CA Patterns ({len(cross_group_cas)} CAs):")
            
            timing_diffs = [p.time_difference for p in cross_group_cas]
            avg_delay = sum(timing_diffs) / len(timing_diffs)
            
            report.append(f"   Average MEME 1000X delay: {avg_delay:.1f} seconds")
            report.append(f"   Expected delay: {self.meme_1000x_delay} seconds")
            report.append(f"   Delay variance: {avg_delay - self.meme_1000x_delay:.1f} seconds")
            
            # Analyze timing patterns
            within_expected = sum(1 for t in timing_diffs if 240 <= t <= 360)  # 4-6 minutes
            report.append(f"   Within expected range (4-6min): {within_expected}/{len(timing_diffs)}")
            
            # Show recent examples
            report.append(f"\n   Recent Cross-Group Examples:")
            recent_patterns = sorted(cross_group_cas, key=lambda p: p.gmgn_detection.timestamp, reverse=True)[:5]
            
            for pattern in recent_patterns:
                gmgn_time = pattern.gmgn_detection.timestamp.strftime('%H:%M:%S')
                meme_time = pattern.meme_1000x_detection.timestamp.strftime('%H:%M:%S')
                delay = pattern.time_difference
                
                report.append(f"      {pattern.ca[:20]}... | GMGN: {gmgn_time} → MEME: {meme_time} | Delay: {delay:.0f}s")
        else:
            report.append("\n🔄 No cross-group patterns detected yet")
        
        return "\n".join(report)
    
    def get_trending_effectiveness_report(self) -> str:
        """Generate trending filter effectiveness report."""
        report = []
        report.append("🎯 Trending Filter Effectiveness Analysis")
        report.append("=" * 50)
        
        # Overall statistics
        total_detections = self.stats['total_gmgn_detections'] + self.stats['total_meme_1000x_detections']
        if total_detections > 0:
            trending_rate = (self.stats['trending_qualified'] / total_detections) * 100
            noise_rate = (self.stats['noise_filtered'] / total_detections) * 100
            
            report.append(f"\n📊 Overall Filter Performance:")
            report.append(f"   Total CA detections: {total_detections}")
            report.append(f"   Trending qualified: {self.stats['trending_qualified']} ({trending_rate:.1f}%)")
            report.append(f"   Filtered as noise: {self.stats['noise_filtered']} ({noise_rate:.1f}%)")
            report.append(f"   Cross-group CAs: {self.stats['cross_group_cas']}")
        
        # Group-specific analysis
        report.append(f"\n📈 Per-Group Analysis:")
        report.append(f"   🧠 GMGN Featured Signals: {self.stats['total_gmgn_detections']} detections")
        report.append(f"   🚀 MEME 1000X: {self.stats['total_meme_1000x_detections']} detections")
        
        # Analyze potential missed opportunities
        filtered_cas = [analysis for analysis in self.trending_analyses.values() 
                       if analysis.filtered_as_noise and not analysis.qualified_for_trending]
        
        if filtered_cas:
            report.append(f"\n⚠️ Potential Missed Opportunities ({len(filtered_cas)} CAs):")
            report.append("   CAs filtered as noise that appeared in multiple groups:")
            
            cross_group_filtered = [ca for ca in filtered_cas if ca.cross_group_appearances > 1]
            for analysis in cross_group_filtered[:5]:  # Show top 5
                report.append(f"      {analysis.ca[:20]}... | Mentions: {analysis.total_mentions} | Cross-group: {analysis.cross_group_appearances}")
        
        return "\n".join(report)
    
    def get_comprehensive_report(self) -> str:
        """Generate comprehensive analysis report."""
        report = []
        report.append("🔍 High-Volume Group CA Analysis Report")
        report.append("=" * 60)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Add timing analysis
        report.append(self.get_timing_analysis_report())
        report.append("")
        
        # Add trending effectiveness analysis
        report.append(self.get_trending_effectiveness_report())
        report.append("")
        
        # Recommendations
        report.append("💡 Recommendations:")
        
        # Analyze if threshold should be adjusted
        cross_group_filtered = len([a for a in self.trending_analyses.values() 
                                  if a.filtered_as_noise and a.cross_group_appearances > 1])
        
        if cross_group_filtered > 0:
            report.append(f"   ⚠️ Consider lowering threshold: {cross_group_filtered} cross-group CAs were filtered")
            report.append("   📊 Suggested: Reduce from 6 mentions to 4-5 mentions")
        else:
            report.append("   ✅ Current 6-mention threshold appears optimal")
        
        # Timing recommendations
        cross_group_cas = [p for p in self.cross_group_patterns.values() if p.time_difference is not None]
        if cross_group_cas:
            avg_delay = sum(p.time_difference for p in cross_group_cas) / len(cross_group_cas)
            if abs(avg_delay - self.meme_1000x_delay) > 60:  # More than 1 minute variance
                report.append(f"   ⏰ Update expected MEME 1000X delay: {avg_delay:.0f}s (currently {self.meme_1000x_delay}s)")
        
        return "\n".join(report)
    
    def save_analysis_data(self, filepath: str):
        """Save analysis data to JSON file."""
        try:
            data = {
                'timestamp': datetime.now().isoformat(),
                'stats': self.stats,
                'detections': [asdict(d) for d in self.ca_detections],
                'cross_group_patterns': {ca: asdict(pattern) for ca, pattern in self.cross_group_patterns.items()},
                'trending_analyses': {ca: asdict(analysis) for ca, analysis in self.trending_analyses.items()}
            }
            
            # Convert datetime objects to strings
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                return obj
            
            data = convert_datetime(data)
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
            
            logger.info(f"Analysis data saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving analysis data: {e}")

# Global instance
high_volume_analyzer = HighVolumeCAAnalyzer()
