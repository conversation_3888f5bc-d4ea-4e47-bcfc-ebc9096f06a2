import sys
import os
import asyncio
import logging
from pathlib import Path

# Fix Windows console encoding issues
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up basic logging with ASCII-safe format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('startup.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def verify_fixes():
    """Verify that all our recent fixes are in place"""
    logger.info("[VERIFY] Checking recent fixes...")

    # Check if WINNERS is reactivated
    try:
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_content = f.read()
                # Check if WINNERS exclusion line exists and is not commented
                lines = env_content.split('\n')
                winners_excluded = False
                for line in lines:
                    line_stripped = line.strip()
                    # Check if line excludes WINNERS and is NOT commented out
                    if (line_stripped.startswith('TRENDING_EXCLUDE_DESTINATIONS=') and
                        'WINNERS' in line_stripped and
                        not line_stripped.startswith('#')):
                        winners_excluded = True
                        break

                if winners_excluded:
                    logger.warning("[WARN] WINNERS still excluded in .env")
                    return False
                else:
                    logger.info("[OK] WINNERS reactivation confirmed - exclusion line is commented out")
        else:
            logger.info("[OK] No .env file found - using default config")
    except Exception as e:
        logger.error(f"[ERROR] Could not check .env file: {e}")
        return False

    # Check if enhanced stats tracker has our fixes
    try:
        with open('src/enhanced_stats_tracker.py', 'r') as f:
            stats_content = f.read()
            if '_forward_to_winners_parallel' in stats_content:
                logger.info("[OK] Enhanced stats tracker fixes confirmed")
            else:
                logger.warning("[WARN] Enhanced stats tracker missing fixes")
                return False
    except Exception as e:
        logger.error(f"[ERROR] Could not check enhanced_stats_tracker.py: {e}")
        return False

    logger.info("[OK] All fixes verified successfully")
    return True

async def main():
    try:
        logger.info("[START] Starting CLA Bot with enhanced fixes...")

        # Verify fixes are in place
        if not verify_fixes():
            logger.error("[ERROR] Fix verification failed - please check the code")
            return False

        # Import and run the main bot
        from main import main as bot_main
        await bot_main()

    except Exception as e:
        logger.error(f"[ERROR] Failed to start bot: {e}")
        logger.exception("Full error details:")
        return False

    return True

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("[STOP] Bot stopped by user")
    except Exception as e:
        logger.error(f"[CRITICAL] Critical error: {e}")
        logger.exception("Full error details:")
