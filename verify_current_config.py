#!/usr/bin/env python3
"""
Verify the current bot configuration for FINDERTRENDING and MANIFEST
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_configuration():
    """Verify the current configuration."""
    print("🔍 VERIFYING CURRENT BOT CONFIGURATION...")
    
    try:
        from config import config
        
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"\n📋 CONFIGURATION SUMMARY:")
        print(f"Total groups: {len(config.target_group.all_group_ids)}")
        print(f"Active groups: {len(config.target_group.active_group_ids)}")
        print(f"All group IDs: {config.target_group.all_group_ids}")
        print(f"Active group IDs: {config.target_group.active_group_ids}")
        
        print(f"\n🔍 SPECIFIC GROUP VERIFICATION:")
        
        # Check FINDERTRENDING
        print(f"FINDERTRENDING ({findertrending_id}):")
        print(f"  ✅ In all_group_ids: {findertrending_id in config.target_group.all_group_ids}")
        print(f"  ✅ In active_group_ids: {findertrending_id in config.target_group.active_group_ids}")
        print(f"  ✅ Status: {config.target_group.group_status.get(findertrending_id, 'NOT FOUND')}")
        
        # Check MANIFEST
        print(f"MANIFEST ({manifest_id}):")
        print(f"  ✅ In all_group_ids: {manifest_id in config.target_group.all_group_ids}")
        print(f"  ✅ In active_group_ids: {manifest_id in config.target_group.active_group_ids}")
        print(f"  ✅ Status: {config.target_group.group_status.get(manifest_id, 'NOT FOUND')}")
        
        # Check if they would be included in message handler
        print(f"\n📡 MESSAGE HANDLER VERIFICATION:")
        print(f"Groups that will receive message handler:")
        for i, group_id in enumerate([config.target_group.group_id] + config.target_group.additional_group_ids):
            if group_id in config.target_group.active_group_ids:
                if group_id == config.target_group.group_id:
                    group_name = config.target_group.group_name
                else:
                    idx = config.target_group.additional_group_ids.index(group_id)
                    group_name = config.target_group.additional_group_names[idx] if idx < len(config.target_group.additional_group_names) else f"Group {group_id}"
                print(f"  ✅ {group_name} ({group_id})")
        
        # Verify trending configuration
        print(f"\n📊 TRENDING CONFIGURATION:")
        print(f"High-volume groups: {config.trending.high_volume_groups}")
        print(f"Low-volume groups: {config.trending.low_volume_groups}")
        print(f"FINDERTRENDING in low-volume: {findertrending_id in config.trending.low_volume_groups}")
        print(f"MANIFEST in low-volume: {manifest_id in config.trending.low_volume_groups}")
        
        # Check if both groups are properly configured
        both_active = (findertrending_id in config.target_group.active_group_ids and 
                      manifest_id in config.target_group.active_group_ids)
        both_low_volume = (findertrending_id in config.trending.low_volume_groups and 
                          manifest_id in config.trending.low_volume_groups)
        
        print(f"\n🎯 FINAL VERIFICATION:")
        print(f"Both groups in active_group_ids: {'✅ YES' if both_active else '❌ NO'}")
        print(f"Both groups in low_volume_groups: {'✅ YES' if both_low_volume else '❌ NO'}")
        
        if both_active and both_low_volume:
            print(f"\n✅ CONFIGURATION IS CORRECT!")
            print(f"Both FINDERTRENDING and MANIFEST should receive messages.")
            print(f"If they're not showing activity, the groups themselves may be inactive.")
        else:
            print(f"\n❌ CONFIGURATION ISSUE DETECTED!")
            
        return both_active and both_low_volume
        
    except Exception as e:
        print(f"❌ Configuration verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main verification function."""
    print("🚨 CURRENT BOT CONFIGURATION VERIFICATION")
    print("=" * 60)
    
    success = verify_configuration()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ CONFIGURATION VERIFICATION PASSED")
        print("If groups are still inactive, they may not be sending messages.")
    else:
        print("❌ CONFIGURATION VERIFICATION FAILED")
        print("Configuration issues need to be fixed.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
