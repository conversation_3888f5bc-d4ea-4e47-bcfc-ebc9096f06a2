#!/usr/bin/env python3
"""
Diagnose startup issues for CLA Bot
"""
import os
import sys
import traceback

def main():
    print("=" * 60)
    print("CLA Bot Startup Diagnosis")
    print("=" * 60)
    
    try:
        print("1. Checking Python environment...")
        print(f"Python version: {sys.version}")
        print(f"Current directory: {os.getcwd()}")
        
        print("\n2. Checking required files...")
        required_files = ['main.py', 'config.py', '.env']
        for file in required_files:
            if os.path.exists(file):
                print(f"✅ {file} exists")
            else:
                print(f"❌ {file} missing")
        
        print("\n3. Checking imports...")
        try:
            import config
            print("✅ config module imported")
        except Exception as e:
            print(f"❌ config import failed: {e}")
            traceback.print_exc()
            return
        
        try:
            from src.database import initialize as db_init
            print("✅ database module imported")
        except Exception as e:
            print(f"❌ database import failed: {e}")
            traceback.print_exc()
            return
        
        print("\n4. Testing database initialization...")
        try:
            db_init()
            print("✅ Database initialized successfully")
        except Exception as e:
            print(f"❌ Database initialization failed: {e}")
            traceback.print_exc()
            return
        
        print("\n5. Testing bot import...")
        try:
            from src.bot import CLABot
            print("✅ Bot class imported successfully")
        except Exception as e:
            print(f"❌ Bot import failed: {e}")
            traceback.print_exc()
            return
        
        print("\n6. Testing configuration...")
        try:
            excluded = getattr(config.trending, 'exclude_destinations', [])
            print(f"✅ Trending exclusions: {excluded}")
            
            winners_excluded = 'WINNERS' in [dest.upper() for dest in excluded]
            if winners_excluded:
                print("❌ WINNERS is excluded in configuration!")
            else:
                print("✅ WINNERS is not excluded")
                
        except Exception as e:
            print(f"❌ Configuration check failed: {e}")
            traceback.print_exc()
        
        print("\n7. All basic checks completed!")
        print("If all checks passed, the issue might be with Telegram authentication.")
        
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
