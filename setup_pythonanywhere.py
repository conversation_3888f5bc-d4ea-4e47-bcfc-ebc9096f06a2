#!/usr/bin/env python3
"""
PythonAnywhere Setup Script for CLA v2 Telegram Bot
Automates the deployment setup process on PythonAnywhere hosting
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def print_step(step_num, description):
    """Print formatted step information."""
    print(f"\n{'='*60}")
    print(f"STEP {step_num}: {description}")
    print('='*60)

def run_command(command, description=""):
    """Run a shell command and return success status."""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description or 'Command'} successful")
            if result.stdout:
                print(f"Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description or 'Command'} failed")
            if result.stderr:
                print(f"Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return False

def check_python_version():
    """Check Python version compatibility."""
    print_step(1, "CHECKING PYTHON VERSION")
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("✅ Python version is compatible (3.8+)")
        return True
    else:
        print("❌ Python version is not compatible. Need Python 3.8+")
        return False

def create_directories():
    """Create required directories."""
    print_step(2, "CREATING REQUIRED DIRECTORIES")
    
    directories = ['data', 'logs']
    success = True
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            os.chmod(directory, 0o755)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Failed to create directory {directory}: {e}")
            success = False
    
    return success

def install_dependencies():
    """Install Python dependencies."""
    print_step(3, "INSTALLING DEPENDENCIES")
    
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    # Try different pip commands for PythonAnywhere
    pip_commands = [
        "pip3.9 install --user -r requirements.txt",
        "pip3 install --user -r requirements.txt", 
        "python3.9 -m pip install --user -r requirements.txt",
        "python3 -m pip install --user -r requirements.txt"
    ]
    
    for cmd in pip_commands:
        print(f"Trying: {cmd}")
        if run_command(cmd, "Dependency installation"):
            return True
    
    print("❌ All pip installation attempts failed")
    return False

def verify_imports():
    """Verify that core modules can be imported."""
    print_step(4, "VERIFYING MODULE IMPORTS")
    
    modules_to_test = [
        'telethon',
        'aiosqlite', 
        'loguru',
        'python-dotenv',
        'asyncio-throttle'
    ]
    
    success = True
    for module in modules_to_test:
        try:
            __import__(module.replace('-', '_'))
            print(f"✅ {module} imported successfully")
        except ImportError as e:
            print(f"❌ Failed to import {module}: {e}")
            success = False
    
    # Test project modules
    sys.path.append('.')
    project_modules = [
        'src.bot',
        'src.ca_detector', 
        'src.message_parser',
        'src.database'
    ]
    
    for module in project_modules:
        try:
            __import__(module)
            print(f"✅ {module} imported successfully")
        except Exception as e:
            print(f"❌ Failed to import {module}: {e}")
            success = False
    
    return success

def check_environment_file():
    """Check if .env file exists and has required variables."""
    print_step(5, "CHECKING ENVIRONMENT CONFIGURATION")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("Please create .env file with required environment variables")
        print("See PYTHONANYWHERE_DEPLOYMENT_GUIDE.md for template")
        return False
    
    required_vars = [
        'TELEGRAM_API_ID',
        'TELEGRAM_API_HASH', 
        'TELEGRAM_PHONE',
        'TARGET_GROUP_ID',
        'BONKBOT_USER_ID',
        'CLA_V2_GROUP_ID',
        'MONACO_PNL_GROUP_ID'
    ]
    
    with open('.env', 'r') as f:
        env_content = f.read()
    
    missing_vars = []
    for var in required_vars:
        if f"{var}=" not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("✅ All required environment variables found")
        return True

async def test_database_connectivity():
    """Test database connectivity."""
    print_step(6, "TESTING DATABASE CONNECTIVITY")
    
    try:
        from src.database import DatabaseManager
        
        db = DatabaseManager()
        await db.connect()
        print("✅ Database connection successful")
        
        # Test table creation
        await db._create_tables()
        print("✅ Database tables created/verified")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def check_session_file():
    """Check if Telegram session file exists."""
    print_step(7, "CHECKING TELEGRAM SESSION")
    
    session_files = [f for f in os.listdir('.') if f.startswith('cla_bot_session.session')]
    
    if session_files:
        print(f"✅ Telegram session file found: {session_files[0]}")
        return True
    else:
        print("❌ Telegram session file not found")
        print("Run: python3.9 authenticate_telegram.py")
        return False

def run_verification_tests():
    """Run the verification test suites."""
    print_step(8, "RUNNING VERIFICATION TESTS")
    
    test_files = [
        'test_winners_disabled_and_indentation_fix.py',
        'test_optimized_ca_extraction.py'
    ]
    
    success = True
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\nRunning {test_file}...")
            if run_command(f"python3.9 {test_file}", f"{test_file} execution"):
                print(f"✅ {test_file} passed")
            else:
                print(f"❌ {test_file} failed")
                success = False
        else:
            print(f"⚠️ {test_file} not found - skipping")
    
    return success

def create_startup_script():
    """Create startup script for PythonAnywhere."""
    print_step(9, "CREATING STARTUP SCRIPT")
    
    startup_script = """#!/bin/bash
# CLA v2 Bot Startup Script for PythonAnywhere
cd /home/<USER>/clav2
python3.9 main.py
"""
    
    try:
        with open('start_bot.sh', 'w') as f:
            f.write(startup_script)
        
        os.chmod('start_bot.sh', 0o755)
        print("✅ Startup script created: start_bot.sh")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create startup script: {e}")
        return False

def create_health_check_script():
    """Create health check script."""
    print_step(10, "CREATING HEALTH CHECK SCRIPT")
    
    health_script = """#!/usr/bin/env python3.9
import subprocess
import sqlite3
import os
import time

def health_check():
    print(f"Health Check - {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Check if bot is running
    result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True)
    if result.returncode == 0:
        print("✅ Bot process is running")
    else:
        print("❌ Bot process is not running")
        return False
    
    # Check database
    try:
        conn = sqlite3.connect('data/cla_bot.db')
        cursor = conn.execute('SELECT COUNT(*) FROM contract_addresses')
        count = cursor.fetchone()[0]
        conn.close()
        print(f"✅ Database accessible ({count} CAs)")
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False
    
    print("✅ All health checks passed")
    return True

if __name__ == "__main__":
    health_check()
"""
    
    try:
        with open('health_check.py', 'w') as f:
            f.write(health_script)
        
        os.chmod('health_check.py', 0o755)
        print("✅ Health check script created: health_check.py")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create health check script: {e}")
        return False

async def main():
    """Main setup function."""
    print("🚀 CLA v2 TELEGRAM BOT - PYTHONANYWHERE SETUP")
    print("=" * 60)
    print("This script will prepare your PythonAnywhere environment for the CLA v2 bot")
    print("=" * 60)
    
    # Run all setup steps
    steps = [
        ("Python Version Check", check_python_version),
        ("Directory Creation", create_directories),
        ("Dependency Installation", install_dependencies),
        ("Module Import Verification", verify_imports),
        ("Environment Configuration Check", check_environment_file),
        ("Database Connectivity Test", test_database_connectivity),
        ("Telegram Session Check", check_session_file),
        ("Verification Tests", run_verification_tests),
        ("Startup Script Creation", create_startup_script),
        ("Health Check Script Creation", create_health_check_script)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        try:
            if asyncio.iscoroutinefunction(step_func):
                result = await step_func()
            else:
                result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
            results.append((step_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("SETUP SUMMARY")
    print("="*60)
    
    passed = 0
    for step_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{step_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} steps completed successfully")
    
    if passed == len(results):
        print("\n🎉 SETUP COMPLETE!")
        print("✅ Your PythonAnywhere environment is ready for CLA v2 bot")
        print("\nNext steps:")
        print("1. If session file missing: python3.9 authenticate_telegram.py")
        print("2. Start bot: python3.9 main.py")
        print("3. Monitor: tail -f logs/cla_bot.log")
    else:
        print(f"\n❌ SETUP INCOMPLETE - {len(results) - passed} issues need to be resolved")
        print("Please fix the failed steps and run setup again")
    
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
