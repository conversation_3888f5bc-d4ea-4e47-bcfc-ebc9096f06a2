#!/usr/bin/env python3
"""
Simple CA Validation Test
Tests the enhanced validation for the specific problematic CA
"""

import sys
sys.path.append('.')

def test_ca_validation():
    """Test the enhanced CA validation."""
    print("🧪 SIMPLE CA VALIDATION TEST")
    print("=" * 50)
    
    # Test the specific problematic CA
    test_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
    
    print(f"Testing CA: {test_ca}")
    print(f"Length: {len(test_ca)} characters")
    
    # Test base58 validation first
    try:
        import base58
        decoded = base58.b58decode(test_ca)
        print(f"✅ Base58 decode successful: {len(decoded)} bytes")
        
        if len(decoded) == 32:
            print("✅ Valid 32-byte format")
        else:
            print(f"⚠️ Non-standard length: {len(decoded)} bytes")
        
        if decoded != b'\\x00' * 32:
            print("✅ Not all zeros")
        else:
            print("❌ All zeros (invalid)")
            
    except Exception as e:
        print(f"❌ Base58 decode failed: {e}")
        return False
    
    # Test enhanced validation
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        result = parser._validate_solana_ca(test_ca)
        print(f"Enhanced validation result: {'✅ PASS' if result else '❌ FAIL'}")
        
        if result:
            print("🎉 SUCCESS: CA now passes enhanced validation!")
            return True
        else:
            print("❌ FAILURE: CA still fails validation")
            return False
            
    except Exception as e:
        print(f"❌ Validation test error: {e}")
        return False

def test_message_extraction():
    """Test message extraction with the problematic CA."""
    print("\\n🧪 MESSAGE EXTRACTION TEST")
    print("=" * 50)
    
    # Test message from FREE WHALE SIGNALS
    test_message = "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)"
    expected_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
    
    print(f"Test message: {test_message}")
    print(f"Expected CA: {expected_ca}")
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        extracted_cas = parser.extract_contract_addresses(test_message)
        print(f"Extracted CAs: {extracted_cas}")
        
        if expected_ca in extracted_cas:
            print("🎉 SUCCESS: CA extracted from message!")
            return True
        else:
            print("❌ FAILURE: CA not extracted from message")
            return False
            
    except Exception as e:
        print(f"❌ Extraction test error: {e}")
        return False

def main():
    """Run simple validation tests."""
    print("🚀 ENHANCED CA VALIDATION - SIMPLE TEST")
    print("=" * 60)
    print("Testing the specific CA: 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups")
    print("=" * 60)
    
    # Run tests
    validation_success = test_ca_validation()
    extraction_success = test_message_extraction()
    
    print("\\n🎯 RESULTS")
    print("=" * 50)
    print(f"CA Validation: {'✅ PASS' if validation_success else '❌ FAIL'}")
    print(f"Message Extraction: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    
    if validation_success and extraction_success:
        print("\\n🎉 ALL TESTS PASSED!")
        print("✅ The problematic CA should now be processed correctly")
        print("✅ FREE WHALE SIGNALS messages should forward CAs")
    else:
        print("\\n⚠️ TESTS FAILED")
        print("The enhanced validation needs further adjustment")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
