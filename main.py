"""
CLA v2.0 - Telegram Memecoin Signal Monitoring and Auto-Trading Bot
Main entry point for the application.
"""

import os
import asyncio
import signal
import sys
from pathlib import Path

from loguru import logger
from config import config
from src.bot import CLABot
from src.database import DatabaseManager
from src.logger_setup import setup_logging
from src.production_logger import setup_production_logging
from src.health_check import start_health_check_server, stop_health_check_server
from src.backup_manager import start_backup_system

class CLABotRunner:
    """Main bot runner class."""
    
    def __init__(self):
        self.bot = None
        self.db_manager = None
        self.running = False
    
    async def initialize(self):
        """Initialize bot components."""
        try:
            # Setup logging (use simple logging for development)
            environment = os.getenv('ENVIRONMENT', 'development')
            if environment == 'production':
                setup_production_logging()
            else:
                setup_logging()  # Use simple logging for development

            logger.info("🚀 Starting CLA v2.0 Bot...")
            logger.info(f"📊 Environment: {environment}")

            # Validate configuration first
            logger.info("🔍 Validating production configuration...")
            from src.config_validator import validate_production_config
            validate_production_config()
            logger.info("✅ Configuration validation completed")

            # Initialize database
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            logger.info("✅ Database initialized successfully")
            
            # Initialize Phase 1 Slow Cook Tracking
            logger.info("🐌 Initializing Phase 1 Slow Cook Pattern Analysis...")
            try:
                from src.slow_cook_analytics import SlowCookAnalytics
                slow_cook_analytics = SlowCookAnalytics()
                logger.info("   ✅ Slow cook analytics initialized")
                logger.info("   📊 Phase 1 data collection: ACTIVE")
                logger.info("   🔍 Pattern tracking: Multi-mention filtering, cross-group detection")
                logger.info("   ⏱️ Time window analysis: 24-hour pattern memory")
            except Exception as e:
                logger.warning(f"   ⚠️ Slow cook analytics initialization failed: {e}")

            # Start backup system
            logger.info("📦 Starting backup system...")
            await start_backup_system()
            logger.info("✅ Backup system started")

            # Initialize bot
            self.bot = CLABot(self.db_manager)
            await self.bot.initialize()
            logger.info("✅ Bot initialized successfully")

            # Start health check server
            logger.info("🏥 Starting health check server...")
            await start_health_check_server(self.bot)
            logger.info("✅ Health check server started")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize bot: {e}")
            await self.cleanup()
            return False
    
    async def start(self):
        """Start the bot."""
        if not await self.initialize():
            logger.error("Bot initialization failed. Exiting.")
            return
        
        # Setup signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}. Shutting down...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            self.running = True
            logger.info("Bot started successfully. Monitoring for signals...")
            
            # Start bot
            await self.bot.start()
            
            # Keep running until signal received
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Bot encountered an error: {e}")
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """Cleanup resources gracefully."""
        logger.info("🧹 Cleaning up resources...")

        try:
            # Stop health check server
            logger.info("🏥 Stopping health check server...")
            await stop_health_check_server()

            # Stop bot
            if self.bot:
                logger.info("🤖 Stopping bot...")
                await self.bot.stop()

            # Close database connections
            if self.db_manager:
                logger.info("💾 Closing database connections...")
                await self.db_manager.close()

            logger.info("✅ Cleanup completed successfully")

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")

async def main():
    """Main entry point."""
    runner = CLABotRunner()
    await runner.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
