"""Unit tests for CAAnalyzer component."""

import pytest
import asyncio
from unittest.mock import <PERSON>ck, AsyncMock, patch
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.ca_analyzer import <PERSON>Analy<PERSON>, TrendingPatternDetector

class TestCAAnalyzer:
    """Test cases for CAAnalyzer."""
    
    @pytest.fixture
    def mock_ca_detector(self):
        """Mock CA detector."""
        detector = Mock()
        detector.process_message = AsyncMock(return_value=['test_ca_123', 'test_ca_456'])
        detector.db_manager = Mock()
        detector.db_manager.add_trending_result = AsyncMock()
        return detector
    
    @pytest.fixture
    def mock_trending_analyzer(self):
        """Mock trending analyzer."""
        analyzer = Mock()
        analyzer.analyze_ca_trending = AsyncMock()
        analyzer.cleanup_expired_data = AsyncMock()
        return analyzer
    
    @pytest.fixture
    def mock_high_volume_analyzer(self):
        """Mock high volume analyzer."""
        analyzer = Mock()
        analyzer.record_ca_detection = AsyncMock()
        return analyzer
    
    @pytest.fixture
    def mock_enhanced_stats(self):
        """Mock enhanced stats tracker."""
        return Mock()
    
    @pytest.fixture
    def mock_rescue_tracker(self):
        """Mock rescue tracker."""
        tracker = Mock()
        tracker.check_for_rescue = AsyncMock(return_value=False)
        tracker.add_filtered_ca = AsyncMock()
        tracker.cleanup_expired_entries = AsyncMock()
        return tracker
    
    @pytest.fixture
    def ca_analyzer(self, mock_ca_detector, mock_trending_analyzer, mock_high_volume_analyzer,
                   mock_enhanced_stats, mock_rescue_tracker):
        """Create CAAnalyzer instance with mocked dependencies."""
        return CAAnalyzer(
            ca_detector=mock_ca_detector,
            trending_analyzer=mock_trending_analyzer,
            high_volume_analyzer=mock_high_volume_analyzer,
            enhanced_stats_tracker=mock_enhanced_stats,
            ca_rescue_tracker=mock_rescue_tracker
        )
    
    @pytest.mark.asyncio
    async def test_analyze_message_high_volume(self, ca_analyzer, mock_trending_analyzer):
        """Test message analysis for high-volume group."""
        # Mock trending result
        trending_result = Mock()
        trending_result.is_trending = True
        trending_result.mention_count = 6
        trending_result.first_mention = datetime.now()
        trending_result.latest_mention = datetime.now()
        trending_result.time_to_trend = None
        mock_trending_analyzer.analyze_ca_trending.return_value = trending_result
        
        result = await ca_analyzer.analyze_message(
            message_text="Test message",
            message_id=12345,
            chat_id=-1002333406905,  # MEME 1000X
            group_name="🚀 MEME 1000X",
            group_type="HIGH-VOLUME"
        )
        
        assert 'new_cas' in result
        assert 'trending_cas' in result
        assert 'rescue_cas' in result
        assert len(result['new_cas']) == 2
        assert len(result['trending_cas']) == 2  # Both CAs trending
        assert result['analysis_time_ms'] > 0
    
    @pytest.mark.asyncio
    async def test_analyze_message_low_volume(self, ca_analyzer, mock_rescue_tracker):
        """Test message analysis for low-volume group."""
        mock_rescue_tracker.check_for_rescue.return_value = True
        
        result = await ca_analyzer.analyze_message(
            message_text="Test message",
            message_id=12345,
            chat_id=-1002380594298,  # FREE WHALE SIGNALS
            group_name="FREE WHALE SIGNALS",
            group_type="LOW-VOLUME"
        )
        
        assert 'rescue_cas' in result
        assert len(result['rescue_cas']) == 2  # Both CAs rescued
        assert result['rescue_check_time_ms'] > 0
    
    @pytest.mark.asyncio
    async def test_analyze_message_no_cas(self, ca_analyzer, mock_ca_detector):
        """Test message analysis with no CAs detected."""
        mock_ca_detector.process_message.return_value = []
        
        result = await ca_analyzer.analyze_message(
            message_text="No CAs here",
            message_id=12345,
            chat_id=-1002380594298,
            group_name="FREE WHALE SIGNALS",
            group_type="LOW-VOLUME"
        )
        
        assert len(result['new_cas']) == 0
        assert len(result['trending_cas']) == 0
        assert len(result['rescue_cas']) == 0
    
    @pytest.mark.asyncio
    async def test_analyze_trending_patterns_trending(self, ca_analyzer, mock_trending_analyzer):
        """Test trending pattern analysis for trending CA."""
        trending_result = Mock()
        trending_result.is_trending = True
        trending_result.mention_count = 6
        trending_result.first_mention = datetime.now()
        trending_result.latest_mention = datetime.now()
        trending_result.time_to_trend = None
        mock_trending_analyzer.analyze_ca_trending.return_value = trending_result
        
        trending_cas = await ca_analyzer._analyze_trending_patterns(
            new_cas=['test_ca_123'],
            chat_id=-1002333406905,
            group_name="🚀 MEME 1000X",
            group_type="HIGH-VOLUME",
            message_text="Test message"
        )
        
        assert len(trending_cas) == 1
        assert trending_cas[0] == 'test_ca_123'
    
    @pytest.mark.asyncio
    async def test_analyze_trending_patterns_noise(self, ca_analyzer, mock_trending_analyzer, mock_rescue_tracker):
        """Test trending pattern analysis for noise CA."""
        trending_result = Mock()
        trending_result.is_trending = False
        trending_result.mention_count = 2
        trending_result.first_mention = datetime.now()
        trending_result.latest_mention = datetime.now()
        trending_result.time_to_trend = None
        mock_trending_analyzer.analyze_ca_trending.return_value = trending_result
        
        trending_cas = await ca_analyzer._analyze_trending_patterns(
            new_cas=['test_ca_123'],
            chat_id=-1002333406905,
            group_name="🚀 MEME 1000X",
            group_type="HIGH-VOLUME",
            message_text="Test message"
        )
        
        assert len(trending_cas) == 0
        mock_rescue_tracker.add_filtered_ca.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_check_rescue_opportunities(self, ca_analyzer, mock_rescue_tracker):
        """Test rescue opportunity checking."""
        mock_rescue_tracker.check_for_rescue.side_effect = [True, False]
        
        rescue_cas = await ca_analyzer._check_rescue_opportunities(
            new_cas=['test_ca_123', 'test_ca_456'],
            chat_id=-1002380594298,
            group_name="FREE WHALE SIGNALS",
            message_text="Test message"
        )
        
        assert len(rescue_cas) == 1
        assert rescue_cas[0] == 'test_ca_123'
    
    @pytest.mark.asyncio
    async def test_cleanup_expired_data(self, ca_analyzer, mock_trending_analyzer, mock_rescue_tracker):
        """Test cleanup of expired data."""
        await ca_analyzer.cleanup_expired_data()
        
        mock_trending_analyzer.cleanup_expired_data.assert_called_once()
        mock_rescue_tracker.cleanup_expired_entries.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, ca_analyzer, mock_ca_detector):
        """Test error handling in analysis."""
        mock_ca_detector.process_message.side_effect = Exception("Test error")
        
        result = await ca_analyzer.analyze_message(
            message_text="Test message",
            message_id=12345,
            chat_id=-1002380594298,
            group_name="FREE WHALE SIGNALS",
            group_type="LOW-VOLUME"
        )
        
        assert 'error' in result
        assert result['error'] == "Test error"
        assert ca_analyzer.stats['analysis_errors'] == 1
    
    def test_get_stats(self, ca_analyzer):
        """Test statistics retrieval."""
        stats = ca_analyzer.get_stats()
        
        assert isinstance(stats, dict)
        assert 'messages_analyzed' in stats
        assert 'cas_detected' in stats
        assert 'trending_cas_found' in stats

class TestTrendingPatternDetector:
    """Test cases for TrendingPatternDetector."""
    
    @pytest.fixture
    def detector(self):
        """Create TrendingPatternDetector instance."""
        return TrendingPatternDetector()
    
    @pytest.mark.asyncio
    async def test_detect_trending_pattern(self, detector):
        """Test detection of trending pattern."""
        # Mock mentions
        mentions = []
        for i in range(6):
            mention = Mock()
            mention.timestamp = datetime.now()
            mentions.append(mention)
        
        result = await detector.detect_pattern('test_ca_123', mentions)
        
        assert result['is_trending'] is True
        assert result['mention_count'] == 6
        assert result['pattern_type'] == 'trending'
    
    @pytest.mark.asyncio
    async def test_detect_noise_pattern(self, detector):
        """Test detection of noise pattern."""
        # Mock mentions (insufficient for trending)
        mentions = []
        for i in range(2):
            mention = Mock()
            mention.timestamp = datetime.now()
            mentions.append(mention)
        
        result = await detector.detect_pattern('test_ca_123', mentions)
        
        assert result['is_trending'] is False
        assert result['mention_count'] == 2
        assert result['pattern_type'] == 'noise'
    
    @pytest.mark.asyncio
    async def test_detect_pattern_error(self, detector):
        """Test error handling in pattern detection."""
        # Pass invalid mentions to trigger error
        result = await detector.detect_pattern('test_ca_123', None)
        
        assert result['is_trending'] is False
        assert 'error' in result
    
    def test_get_stats(self, detector):
        """Test statistics retrieval."""
        stats = detector.get_stats()
        
        assert isinstance(stats, dict)
        assert 'patterns_analyzed' in stats
        assert 'trending_detected' in stats
        assert 'noise_filtered' in stats

if __name__ == "__main__":
    pytest.main([__file__])
