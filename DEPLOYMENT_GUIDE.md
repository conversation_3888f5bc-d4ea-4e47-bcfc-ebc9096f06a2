# CLA v2 Deployment Guide

**⚠️ PRIVATE REPOSITORY - INTERNAL USE ONLY**

This guide provides step-by-step deployment instructions for the CLA v2 Telegram bot system.

## 🚀 Quick Deployment

### Prerequisites

- Python 3.8+ installed
- Git installed
- Telegram account with API access
- Access to monitored Telegram groups
- Windows/Linux/macOS environment

### 1. Repository Setup

```bash
# Clone the repository
git clone https://github.com/bigdrako1/clav2.git
cd clav2

# Create virtual environment (recommended)
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
# Use your preferred text editor
nano .env  # or vim .env or code .env
```

**Required Environment Variables**:
```bash
# Telegram API Configuration
TELEGRAM_API_ID=********
TELEGRAM_API_HASH=431f5f680d1975acfdd9050089661772
TELEGRAM_PHONE=+************
TELEGRAM_SESSION_NAME=cla_bot_session

# Group Configuration
TARGET_GROUP_ID=-*************
TARGET_GROUP_NAME=FREE WHALE SIGNALS

# Additional Groups (comma-separated)
ADDITIONAL_GROUP_IDS=-1002270988204,-1002064145465,-1001763265784,-1002139128702,-1002202241417,-1002333406905,-1002356333152
ADDITIONAL_GROUP_NAMES=Solana Activity Tracker,🌹 MANIFEST,👤 Mark Degens,💎 FINDERTRENDING,🧠 GMGN Featured Signals,🚀 MEME 1000X,BUGSIE
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE

# Destination Configuration
BONKBOT_USER_ID=5312785865
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391

# Database Configuration
DATABASE_PATH=./data/cla_bot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=3600

# Cache Configuration
CA_CACHE_EXPIRY_HOURS=168
MESSAGE_CACHE_SIZE=10000
DUPLICATE_PREVENTION_HOURS=24
```

### 3. Telegram Authentication

```bash
# Run authentication script
python authenticate_telegram.py

# Follow prompts:
# 1. Verification code will be sent to your Telegram app
# 2. Enter the code when prompted
# 3. Session file will be created automatically
```

### 4. Database Initialization

```bash
# Create data directory
mkdir -p data

# Initialize database (automatic on first run)
python -c "
import asyncio
from src.database import DatabaseManager

async def init_db():
    db = DatabaseManager()
    await db.connect()
    print('Database initialized successfully')
    await db.close()

asyncio.run(init_db())
"
```

### 5. Verification Tests

```bash
# Test Telegram connection
python test_telegram_connection.py

# Test group access
python test_group_access_simple.py

# Test CA detection
python test_ca_detection.py

# Test Phase 1 components
python test_slow_cook_phase1.py
```

### 6. Start the Bot

```bash
# Start main bot
python main.py

# Monitor logs in separate terminal
tail -f logs/cla_bot.log
```

## 🔧 Advanced Deployment

### Production Environment Setup

#### 1. System Service Configuration (Linux)

Create systemd service file:
```bash
sudo nano /etc/systemd/system/clav2-bot.service
```

Service file content:
```ini
[Unit]
Description=CLA v2 Telegram Bot
After=network.target

[Service]
Type=simple
User=clav2
WorkingDirectory=/home/<USER>/clav2
Environment=PATH=/home/<USER>/clav2/venv/bin
ExecStart=/home/<USER>/clav2/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable clav2-bot
sudo systemctl start clav2-bot
sudo systemctl status clav2-bot
```

#### 2. Process Management (Alternative)

Using PM2 for process management:
```bash
# Install PM2
npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'clav2-bot',
    script: 'python',
    args: 'main.py',
    cwd: '/path/to/clav2',
    interpreter: '/path/to/clav2/venv/bin/python',
    restart_delay: 10000,
    max_restarts: 10,
    min_uptime: '10s'
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Docker Deployment

#### Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create data directory
RUN mkdir -p data logs

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Run the bot
CMD ["python", "main.py"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  clav2-bot:
    build: .
    container_name: clav2-bot
    restart: unless-stopped
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
    environment:
      - PYTHONUNBUFFERED=1
    networks:
      - clav2-network

networks:
  clav2-network:
    driver: bridge
```

Deploy with Docker:
```bash
# Build and start
docker-compose up -d

# View logs
docker-compose logs -f clav2-bot

# Stop
docker-compose down
```

## 🔍 Monitoring and Maintenance

### Log Management

#### Log Rotation Configuration
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/clav2-bot
```

Logrotate configuration:
```
/path/to/clav2/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 clav2 clav2
    postrotate
        systemctl reload clav2-bot
    endscript
}
```

#### Real-time Monitoring
```bash
# Monitor all logs
tail -f logs/*.log

# Monitor specific components
tail -f logs/cla_bot.log | grep -E "📊|⚠️|❌"

# Monitor performance
tail -f logs/cla_bot.log | grep "PIPELINE TIMING"

# Monitor errors
tail -f logs/error.log
```

### Health Checks

#### System Health Script
```bash
#!/bin/bash
# health_check.sh

# Check if bot process is running
if pgrep -f "python main.py" > /dev/null; then
    echo "✅ Bot process is running"
else
    echo "❌ Bot process is not running"
    exit 1
fi

# Check database connectivity
python -c "
import asyncio
from src.database import DatabaseManager

async def check_db():
    try:
        db = DatabaseManager()
        await db.connect()
        await db.close()
        print('✅ Database connection successful')
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
        exit(1)

asyncio.run(check_db())
"

# Check log file size
LOG_SIZE=$(du -m logs/cla_bot.log | cut -f1)
if [ $LOG_SIZE -gt 100 ]; then
    echo "⚠️ Log file is large (${LOG_SIZE}MB)"
fi

echo "✅ Health check completed"
```

#### Automated Health Monitoring
```bash
# Add to crontab for regular health checks
crontab -e

# Add this line for 5-minute health checks
*/5 * * * * /path/to/clav2/health_check.sh >> /var/log/clav2-health.log 2>&1
```

### Performance Monitoring

#### Performance Metrics Script
```python
#!/usr/bin/env python3
# monitor_performance.py

import asyncio
import time
from src.database import DatabaseManager
from src.enhanced_stats_tracker import enhanced_stats

async def monitor_performance():
    db = DatabaseManager()
    await db.connect()
    
    while True:
        # Get current statistics
        stats = enhanced_stats.get_current_stats()
        
        # Database performance
        start_time = time.time()
        cursor = await db.connection.execute("SELECT COUNT(*) FROM contract_addresses")
        await cursor.fetchone()
        db_response_time = (time.time() - start_time) * 1000
        
        print(f"📊 Performance Metrics:")
        print(f"   Messages processed: {stats.get('messages_processed', 0)}")
        print(f"   CAs detected: {stats.get('cas_detected', 0)}")
        print(f"   Database response: {db_response_time:.2f}ms")
        print(f"   Memory usage: {stats.get('memory_usage_mb', 0):.1f}MB")
        print("-" * 50)
        
        await asyncio.sleep(60)  # Monitor every minute

if __name__ == "__main__":
    asyncio.run(monitor_performance())
```

### Backup and Recovery

#### Database Backup Script
```bash
#!/bin/bash
# backup_database.sh

BACKUP_DIR="/path/to/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_PATH="./data/cla_bot.db"

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
sqlite3 $DB_PATH ".backup $BACKUP_DIR/cla_bot_backup_$DATE.db"

# Compress backup
gzip "$BACKUP_DIR/cla_bot_backup_$DATE.db"

# Keep only last 30 days of backups
find $BACKUP_DIR -name "cla_bot_backup_*.db.gz" -mtime +30 -delete

echo "✅ Database backup completed: cla_bot_backup_$DATE.db.gz"
```

#### Automated Backup
```bash
# Add to crontab for daily backups
crontab -e

# Add this line for daily 2 AM backups
0 2 * * * /path/to/clav2/backup_database.sh >> /var/log/clav2-backup.log 2>&1
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Authentication Errors
```bash
# Remove session file and re-authenticate
rm cla_bot_session.session*
python authenticate_telegram.py
```

#### 2. Database Lock Errors
```bash
# Check for running processes
ps aux | grep python

# Kill hanging processes
pkill -f "python main.py"

# Remove database lock files
rm data/cla_bot.db-wal data/cla_bot.db-shm
```

#### 3. Network Connectivity Issues
```bash
# Test Telegram connectivity
python -c "
import asyncio
from telethon import TelegramClient

async def test_connection():
    client = TelegramClient('test_session', API_ID, API_HASH)
    await client.start()
    print('✅ Telegram connection successful')
    await client.disconnect()

asyncio.run(test_connection())
"
```

#### 4. Memory Issues
```bash
# Monitor memory usage
ps aux | grep python | awk '{print $6/1024 " MB"}'

# Check cache sizes
python -c "
from src.bounded_cache import BoundedCache
# Check cache statistics
"
```

### Emergency Recovery

#### Quick Recovery Steps
1. Stop the bot: `systemctl stop clav2-bot`
2. Backup current state: `cp -r data data_backup_$(date +%s)`
3. Check logs: `tail -100 logs/error.log`
4. Restore from backup if needed: `cp backup/cla_bot_backup_latest.db data/cla_bot.db`
5. Restart bot: `systemctl start clav2-bot`

This deployment guide provides comprehensive instructions for deploying, monitoring, and maintaining the CLA v2 Telegram bot system in both development and production environments.
