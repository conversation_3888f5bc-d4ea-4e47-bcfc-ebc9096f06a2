"""Simple test to verify basic message reception."""

import asyncio
import sys
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_simple_message_reception():
    """Test basic message reception with minimal setup."""
    print("🔍 Testing Simple Message Reception...")
    
    try:
        from telethon import TelegramClient, events
        from config import config
        
        # Create simple client
        client = TelegramClient(
            config.telegram.session_name,
            config.telegram.api_id,
            config.telegram.api_hash
        )
        
        print("📱 Connecting to Telegram...")
        await client.start()
        print("✅ Connected to Telegram")
        
        # Get user info
        me = await client.get_me()
        print(f"👤 Logged in as: {me.first_name} (@{me.username})")
        
        # Test specific groups
        test_groups = [
            -1002270988204,  # Solana Activity Tracker
            -1002202241417,  # GMGN Featured Signals
            -1002380594298   # FREE WHALE SIGNALS
        ]
        
        print(f"\n🔍 Testing access to {len(test_groups)} groups:")
        
        accessible_groups = []
        for group_id in test_groups:
            try:
                entity = await client.get_entity(group_id)
                print(f"✅ Can access: {entity.title} ({group_id})")
                accessible_groups.append(group_id)
            except Exception as e:
                print(f"❌ Cannot access group {group_id}: {e}")
        
        if not accessible_groups:
            print("❌ No accessible groups found!")
            return
        
        print(f"\n🚀 Setting up message handler for {len(accessible_groups)} accessible groups...")
        
        message_count = 0
        
        @client.on(events.NewMessage(chats=accessible_groups))
        async def simple_handler(event):
            nonlocal message_count
            message_count += 1
            
            chat_id = event.chat_id
            message_text = event.message.text or ""
            
            print(f"📨 MESSAGE #{message_count}: Chat={chat_id} | ID={event.message.id}")
            print(f"   Text: {message_text[:100]}...")
            
            # Check for contract addresses
            if len(message_text) > 40 and any(c.isalnum() for c in message_text):
                # Simple CA detection
                words = message_text.split()
                for word in words:
                    if len(word) >= 40 and word.isalnum():
                        print(f"   🎯 POTENTIAL CA: {word}")
        
        print(f"✅ Message handler registered for groups: {accessible_groups}")
        print(f"⏰ Started monitoring at {datetime.now()}")
        print("Waiting for messages... (Press Ctrl+C to stop)")
        
        # Wait for messages
        try:
            await asyncio.sleep(300)  # 5 minutes
        except KeyboardInterrupt:
            print("\n⏹️ Stopped by user")
        
        print(f"\n📊 Results:")
        print(f"Total messages received: {message_count}")
        
        await client.disconnect()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run simple message reception test."""
    print("🚨 SIMPLE MESSAGE RECEPTION TEST\n")
    
    try:
        asyncio.run(test_simple_message_reception())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
