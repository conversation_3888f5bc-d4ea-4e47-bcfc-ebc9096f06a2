#!/usr/bin/env python3
"""
Simple validation script for CLA v2.0 Telegram Bot
Validates basic functionality before deployment
"""

def test_configuration():
    """Test configuration loading"""
    print("🔍 Testing configuration...")
    try:
        from config import config
        print("✅ Configuration loaded successfully")
        print(f"📊 High-volume groups: {len(config.trending.high_volume_groups)}")
        print(f"📊 Low-volume groups: {len(config.trending.low_volume_groups)}")
        print(f"📊 Active groups: {len(config.target_group.active_group_ids)}")

        # Check critical settings
        if not config.telegram.api_id or not config.telegram.api_hash:
            print("❌ Missing Telegram API credentials")
            return False

        if not config.telegram.phone:
            print("❌ Missing Telegram phone number")
            return False

        print("✅ Critical configuration values present")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_imports():
    """Test critical imports"""
    print("\n🔍 Testing imports...")
    try:
        # Test core imports
        from src.bot import CLABot
        from src.message_processor import MessageProcessor
        from src.ca_detector import CADetector
        from src.trending_analyzer import TrendingAnalyzer
        from src.forwarding_manager import ForwardingManager
        from src.database import DatabaseManager
        print("✅ All critical imports successful")
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_environment():
    """Test environment setup"""
    print("\n🔍 Testing environment...")
    try:
        import os
        from pathlib import Path

        # Check data directory
        data_dir = Path("./data")
        if not data_dir.exists():
            data_dir.mkdir(parents=True, exist_ok=True)
            print("✅ Created data directory")
        else:
            print("✅ Data directory exists")

        # Check logs directory
        logs_dir = Path("./logs")
        if not logs_dir.exists():
            logs_dir.mkdir(parents=True, exist_ok=True)
            print("✅ Created logs directory")
        else:
            print("✅ Logs directory exists")

        # Check .env file
        if Path(".env").exists():
            print("✅ Environment file exists")
        else:
            print("❌ Environment file missing")
            return False

        return True
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

def main():
    """Run validation tests"""
    print("🚀 CLA v2.0 Bot - Pre-Deployment Validation")
    print("=" * 50)

    tests = [
        ("Environment Setup", test_environment),
        ("Configuration", test_configuration),
        ("Critical Imports", test_imports),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False

    print("\n" + "=" * 50)
    print("📊 VALIDATION RESULTS")
    print("=" * 50)

    passed = 0
    total = len(tests)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 Basic validation passed! Bot is ready for deployment testing.")
        return True
    else:
        print("⚠️ Some validation tests failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)