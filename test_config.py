"""Test configuration loading."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

print("Environment variables loaded:")
print(f"TELEGRAM_API_ID: {os.getenv('TELEGRAM_API_ID')}")
print(f"TELEGRAM_API_HASH: {os.getenv('TELEGRAM_API_HASH')}")
print(f"TELEGRAM_PHONE: {os.getenv('TELEGRAM_PHONE')}")

print("\nAll environment variables:")
for key, value in os.environ.items():
    if 'TELEGRAM' in key.upper():
        print(f"{key}: {value}")

# Test pydantic-settings directly
try:
    from pydantic import Field
    from pydantic_settings import BaseSettings
    
    class TestTelegramConfig(BaseSettings):
        api_id: int = Field(..., env="TELEGRAM_API_ID")
        api_hash: str = Field(..., env="TELEGRAM_API_HASH")
        phone: str = Field(..., env="TELEGRAM_PHONE")
        
        class Config:
            env_file = ".env"
            env_file_encoding = "utf-8"
            extra = "ignore"
    
    print("\nTesting pydantic-settings:")
    config = TestTelegramConfig()
    print(f"API ID: {config.api_id}")
    print(f"API Hash: {config.api_hash}")
    print(f"Phone: {config.phone}")
    print("✅ Configuration loaded successfully!")
    
except Exception as e:
    print(f"❌ Configuration failed: {e}")
    print(f"Error type: {type(e)}")
