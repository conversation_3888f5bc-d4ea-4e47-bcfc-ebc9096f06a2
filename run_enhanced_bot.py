#!/usr/bin/env python3
"""
Enhanced CLA Bot Runner - Bypasses terminal issues
"""
import os
import sys
import asyncio
import logging
from pathlib import Path

# Ensure we're in the right directory
os.chdir(Path(__file__).parent)

# Set up logging to file only to avoid terminal encoding issues
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
    handlers=[
        logging.FileHandler('enhanced_bot_startup.log', encoding='utf-8'),
        logging.FileHandler('logs/cla_bot.log', encoding='utf-8', mode='a')
    ]
)

logger = logging.getLogger(__name__)

def print_status(message):
    """Print status to both console and log"""
    print(message)
    logger.info(message)

async def main():
    try:
        print_status("=" * 60)
        print_status("CLA Bot Enhanced Version Starting...")
        print_status("=" * 60)
        
        print_status("Verifying WINNERS reactivation...")
        
        # Check .env file
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_content = f.read()
                if '# TRENDING_EXCLUDE_DESTINATIONS=WINNERS' in env_content:
                    print_status("[OK] WINNERS exclusion is commented out - REACTIVATED")
                else:
                    print_status("[WARN] WINNERS status unclear in .env")
        
        print_status("Importing enhanced bot modules...")
        
        # Import the main bot
        from main import main as bot_main
        
        print_status("Starting enhanced CLA bot...")
        print_status("Features enabled:")
        print_status("- WINNERS group reactivated")
        print_status("- Enhanced CA detection debugging")
        print_status("- Mark Degens monitoring active")
        print_status("- Improved status reporting")
        
        # Start the bot
        await bot_main()
        
    except KeyboardInterrupt:
        print_status("Bot stopped by user (Ctrl+C)")
    except Exception as e:
        print_status(f"Error starting bot: {e}")
        logger.exception("Full error details:")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)
