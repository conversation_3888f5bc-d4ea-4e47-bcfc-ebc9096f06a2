"""Test anti-pump-and-dump protection in trending analyzer."""

import asyncio
import sys
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

async def test_pump_and_dump_detection():
    """Test pump-and-dump detection scenarios."""
    print("🧪 Testing Anti-Pump-and-Dump Protection...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer, CAMention
        
        analyzer = TrendingAnalyzer()
        test_ca = "TestPumpCA123456789"
        gmgn_group_id = -1002202241417
        
        print(f"\n📋 Configuration:")
        print(f"   Time Window: {analyzer.time_window_minutes} minutes")
        print(f"   Min Mentions: {analyzer.min_mentions}")
        print(f"   Min Time Spread: {analyzer.min_time_spread_seconds} seconds")
        print(f"   Max Velocity: {analyzer.max_velocity_mentions_per_minute} mentions/minute")
        print(f"   Min Organic Growth: {analyzer.min_organic_growth_minutes} minutes")
        
        # Test 1: Pump Scheme - Too Fast (simultaneous mentions)
        print(f"\n🚨 Test 1: Pump Scheme - Simultaneous Mentions")
        
        base_time = datetime.now()
        pump_mentions = []
        
        # Create 6 mentions within 1 second (classic pump pattern)
        for i in range(6):
            mention = CAMention(
                ca=test_ca,
                timestamp=base_time + timedelta(milliseconds=i*100),  # 100ms apart
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=1000 + i
            )
            pump_mentions.append(mention)
        
        is_pump, reason = await analyzer._detect_pump_and_dump_pattern(pump_mentions)
        print(f"   Result: {'🚫 BLOCKED' if is_pump else '✅ ALLOWED'} - {reason}")
        
        if not is_pump:
            print(f"   ❌ FAILED: Should have detected pump scheme")
            return False
        
        # Test 2: Organic Growth - Proper Time Distribution
        print(f"\n✅ Test 2: Organic Growth - Proper Time Distribution")
        
        organic_mentions = []
        
        # Create 6 mentions spread over 4 minutes (organic pattern)
        for i in range(6):
            mention = CAMention(
                ca=test_ca + "_organic",
                timestamp=base_time + timedelta(minutes=i*0.7),  # ~42 seconds apart
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=2000 + i
            )
            organic_mentions.append(mention)
        
        is_pump2, reason2 = await analyzer._detect_pump_and_dump_pattern(organic_mentions)
        print(f"   Result: {'🚫 BLOCKED' if is_pump2 else '✅ ALLOWED'} - {reason2}")
        
        if is_pump2:
            print(f"   ❌ FAILED: Should have allowed organic growth")
            return False
        
        # Test 3: High Velocity Pump
        print(f"\n🚨 Test 3: High Velocity Pump")
        
        velocity_mentions = []
        
        # Create 6 mentions in 1 minute (too high velocity)
        for i in range(6):
            mention = CAMention(
                ca=test_ca + "_velocity",
                timestamp=base_time + timedelta(seconds=i*10),  # 10 seconds apart
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=3000 + i
            )
            velocity_mentions.append(mention)
        
        is_pump3, reason3 = await analyzer._detect_pump_and_dump_pattern(velocity_mentions)
        print(f"   Result: {'🚫 BLOCKED' if is_pump3 else '✅ ALLOWED'} - {reason3}")
        
        if not is_pump3:
            print(f"   ❌ FAILED: Should have detected high velocity pump")
            return False
        
        # Test 4: Insufficient Growth Time
        print(f"\n🚨 Test 4: Insufficient Growth Time")
        
        short_mentions = []
        
        # Create 6 mentions in 2 minutes (below 3-minute minimum)
        for i in range(6):
            mention = CAMention(
                ca=test_ca + "_short",
                timestamp=base_time + timedelta(seconds=i*20),  # 20 seconds apart
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=4000 + i
            )
            short_mentions.append(mention)
        
        is_pump4, reason4 = await analyzer._detect_pump_and_dump_pattern(short_mentions)
        print(f"   Result: {'🚫 BLOCKED' if is_pump4 else '✅ ALLOWED'} - {reason4}")
        
        if not is_pump4:
            print(f"   ❌ FAILED: Should have detected insufficient growth time")
            return False
        
        # Test 5: Clustered Mentions (Front-loaded)
        print(f"\n🚨 Test 5: Clustered Mentions (Front-loaded)")
        
        clustered_mentions = []
        
        # Create 6 mentions with 4 in first quarter, 2 spread later
        times = [
            base_time,
            base_time + timedelta(seconds=30),
            base_time + timedelta(seconds=60),
            base_time + timedelta(seconds=90),  # 4 mentions in first 90 seconds
            base_time + timedelta(minutes=4),   # 2 mentions later
            base_time + timedelta(minutes=5)
        ]
        
        for i, timestamp in enumerate(times):
            mention = CAMention(
                ca=test_ca + "_clustered",
                timestamp=timestamp,
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=5000 + i
            )
            clustered_mentions.append(mention)
        
        is_pump5, reason5 = await analyzer._detect_pump_and_dump_pattern(clustered_mentions)
        print(f"   Result: {'🚫 BLOCKED' if is_pump5 else '✅ ALLOWED'} - {reason5}")
        
        if not is_pump5:
            print(f"   ❌ FAILED: Should have detected clustered mentions")
            return False
        
        print(f"\n🎉 All anti-pump-and-dump tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Anti-pump protection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_trending_with_protection():
    """Test full trending analysis with protection."""
    print(f"\n🧪 Testing Full Trending Analysis with Protection...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        test_ca = "FullTestCA123456789"
        gmgn_group_id = -1002202241417
        
        # Test legitimate trending (should pass)
        print(f"\n✅ Testing Legitimate Trending:")
        
        # Simulate 6 mentions spread over 4 minutes by manually creating mentions
        base_time = datetime.now()

        # Create mentions with proper time distribution
        mention_times = [
            base_time,
            base_time + timedelta(minutes=0.5),
            base_time + timedelta(minutes=1.2),
            base_time + timedelta(minutes=2.0),
            base_time + timedelta(minutes=3.1),
            base_time + timedelta(minutes=4.0)
        ]

        # Add mentions to analyzer manually with proper timestamps
        for i, timestamp in enumerate(mention_times):
            # Create mention with specific timestamp
            from src.trending_analyzer import CAMention
            mention = CAMention(
                ca=test_ca,
                timestamp=timestamp,
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=6000 + i
            )

            # Add to analyzer's tracking
            analyzer.ca_mentions[test_ca].append(mention)
            analyzer.stats['total_mentions'] += 1

            # Analyze trending status
            result = await analyzer._analyze_trending_status(test_ca)
            print(f"   Mention {i+1}: Trending={result.is_trending}, Count={result.mention_count}")

            if result.is_trending:
                break
        
        # Check final result
        if result.is_trending:
            print(f"   ✅ Legitimate trending detected successfully")
        else:
            print(f"   ❌ Legitimate trending not detected")
            return False
        
        # Test pump scheme (should be blocked)
        print(f"\n🚫 Testing Pump Scheme (should be blocked):")
        
        pump_ca = "PumpSchemeCA123456789"
        
        # Simulate pump scheme with rapid mentions
        pump_base_time = datetime.now()

        # Create pump mentions (all within 1 second)
        pump_times = [
            pump_base_time + timedelta(milliseconds=i*100) for i in range(6)
        ]

        # Add pump mentions to analyzer
        for i, timestamp in enumerate(pump_times):
            from src.trending_analyzer import CAMention
            mention = CAMention(
                ca=pump_ca,
                timestamp=timestamp,
                group_id=gmgn_group_id,
                group_name="GMGN",
                message_id=7000 + i
            )

            # Add to analyzer's tracking
            analyzer.ca_mentions[pump_ca].append(mention)
            analyzer.stats['total_mentions'] += 1

            # Analyze trending status
            result = await analyzer._analyze_trending_status(pump_ca)
            print(f"   Mention {i+1}: Trending={result.is_trending}, Count={result.mention_count}")

            if i >= 5:  # Check final result after 6 mentions
                break
        
        # Check final result
        if not result.is_trending:
            print(f"   ✅ Pump scheme correctly blocked")
        else:
            print(f"   ❌ Pump scheme not blocked - SECURITY ISSUE!")
            return False
        
        print(f"\n🎉 Full trending analysis with protection working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Full trending test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all anti-pump-and-dump protection tests."""
    print("🚀 CLA v2.0 Bot - Anti-Pump-and-Dump Protection Testing\n")
    
    try:
        # Test 1: Pump detection patterns
        if not await test_pump_and_dump_detection():
            return False
        
        # Test 2: Full trending analysis with protection
        if not await test_trending_with_protection():
            return False
        
        print(f"\n🎉 All anti-pump-and-dump protection tests passed!")
        print(f"\n📋 Enhanced Protection Summary:")
        print(f"✅ Time window increased: 5min → 8min")
        print(f"✅ Mention threshold increased: 3 → 6 mentions")
        print(f"✅ Minimum time spread: 120 seconds")
        print(f"✅ Maximum velocity: 3.0 mentions/minute")
        print(f"✅ Minimum organic growth: 3 minutes")
        print(f"✅ Time distribution analysis: Active")
        print(f"✅ Pump detection: Active")
        
        print(f"\n🎯 Protection Against:")
        print(f"🚫 Simultaneous message pumps")
        print(f"🚫 High-velocity artificial trending")
        print(f"🚫 Insufficient organic growth time")
        print(f"🚫 Front-loaded clustered mentions")
        print(f"🚫 Coordinated pump-and-dump schemes")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Anti-pump protection test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
