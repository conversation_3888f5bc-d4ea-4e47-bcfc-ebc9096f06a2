#!/usr/bin/env python3
"""
Performance Test for CA Detection Optimization
Tests the speed improvements in CA detection and duplicate checking
"""

import asyncio
import time
import sys
from datetime import datetime

# Add project root to path
sys.path.append('.')

async def test_ca_detection_performance():
    """Test CA detection performance with the new optimizations."""
    print("🚀 TESTING CA DETECTION PERFORMANCE OPTIMIZATION")
    print("=" * 60)
    
    try:
        from src.database import DatabaseManager
        from src.ca_detector import CADetector
        
        # Initialize components
        print("Initializing database and CA detector...")
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        ca_detector = CADetector(db_manager)
        
        # Test messages (similar to what you're seeing in logs)
        test_messages = [
            {
                "text": "🚀 NEW TOKEN ALERT: B4PUF4nfKBFQtn69MMX6NS7gSqUoSFyTB9W5EtLVjups is trending!",
                "group_id": -1002202241417,  # GMGN
                "group_name": "GMGN Featured Signals"
            },
            {
                "text": "🔥 HOT SIGNAL: ASDTSLmTnXUB5ZmJu416mPpdvq1ydm13z3E7hY7Sbonk - Don't miss this!",
                "group_id": -1002333406905,  # MEME 1000X
                "group_name": "MEME 1000X"
            },
            {
                "text": "New discovery: J4T9r9xG2jdLsEMraS6yxwxFrUzQgQKVRsqfdYP1bonk",
                "group_id": -1002333406905,  # MEME 1000X
                "group_name": "MEME 1000X"
            },
            {
                "text": "Check this out: 97nFJcGuR1r5AKNqceMuNxbmLXZMWJZ2umNARYfHbonk",
                "group_id": -1002333406905,  # MEME 1000X
                "group_name": "MEME 1000X"
            },
            {
                "text": "Another one: Ar5w4Td92P7eLgZSe5A9powcMnwbHUCgthwnaGEYbonk",
                "group_id": -1002333406905,  # MEME 1000X
                "group_name": "MEME 1000X"
            }
        ]
        
        print(f"Testing with {len(test_messages)} messages...")
        print()
        
        # Test 1: First run (cache initialization)
        print("🧪 TEST 1: First run (with cache initialization)")
        print("-" * 40)
        
        total_time_first = 0
        for i, msg in enumerate(test_messages):
            start_time = time.perf_counter()
            
            result = await ca_detector.process_message(
                msg["text"], 
                1000 + i, 
                msg["group_id"]
            )
            
            end_time = time.perf_counter()
            processing_time = (end_time - start_time) * 1000
            total_time_first += processing_time
            
            print(f"Message {i+1}: {processing_time:.1f}ms | CAs found: {len(result)} | {result}")
        
        print(f"Total time (first run): {total_time_first:.1f}ms")
        print(f"Average per message: {total_time_first/len(test_messages):.1f}ms")
        print()
        
        # Test 2: Second run (cache should be hot)
        print("🧪 TEST 2: Second run (hot cache - duplicates)")
        print("-" * 40)
        
        total_time_second = 0
        for i, msg in enumerate(test_messages):
            start_time = time.perf_counter()
            
            result = await ca_detector.process_message(
                msg["text"], 
                2000 + i, 
                msg["group_id"]
            )
            
            end_time = time.perf_counter()
            processing_time = (end_time - start_time) * 1000
            total_time_second += processing_time
            
            print(f"Message {i+1}: {processing_time:.1f}ms | CAs found: {len(result)} | {result}")
        
        print(f"Total time (second run): {total_time_second:.1f}ms")
        print(f"Average per message: {total_time_second/len(test_messages):.1f}ms")
        print()
        
        # Test 3: Rapid-fire test (simulating high-volume scenario)
        print("🧪 TEST 3: Rapid-fire test (100 messages)")
        print("-" * 40)
        
        rapid_fire_start = time.perf_counter()
        rapid_fire_results = []
        
        for i in range(100):
            # Cycle through test messages
            msg = test_messages[i % len(test_messages)]
            
            start_time = time.perf_counter()
            result = await ca_detector.process_message(
                msg["text"], 
                3000 + i, 
                msg["group_id"]
            )
            end_time = time.perf_counter()
            
            processing_time = (end_time - start_time) * 1000
            rapid_fire_results.append(processing_time)
        
        rapid_fire_total = time.perf_counter() - rapid_fire_start
        
        # Calculate statistics
        avg_time = sum(rapid_fire_results) / len(rapid_fire_results)
        max_time = max(rapid_fire_results)
        min_time = min(rapid_fire_results)
        
        # Count slow messages (>50ms)
        slow_messages = [t for t in rapid_fire_results if t > 50]
        
        print(f"Total time: {rapid_fire_total*1000:.1f}ms")
        print(f"Average per message: {avg_time:.1f}ms")
        print(f"Min time: {min_time:.1f}ms")
        print(f"Max time: {max_time:.1f}ms")
        print(f"Slow messages (>50ms): {len(slow_messages)}/100 ({len(slow_messages)}%)")
        print()
        
        # Performance analysis
        print("📊 PERFORMANCE ANALYSIS")
        print("=" * 60)
        
        if avg_time < 10:
            print("✅ EXCELLENT: Average processing time under 10ms")
        elif avg_time < 50:
            print("✅ GOOD: Average processing time under 50ms")
        elif avg_time < 100:
            print("⚠️ ACCEPTABLE: Average processing time under 100ms")
        else:
            print("❌ SLOW: Average processing time over 100ms")
        
        if len(slow_messages) < 5:
            print("✅ EXCELLENT: Less than 5% slow messages")
        elif len(slow_messages) < 20:
            print("✅ GOOD: Less than 20% slow messages")
        else:
            print("❌ NEEDS IMPROVEMENT: Too many slow messages")
        
        # Compare with previous performance
        print()
        print("🔄 COMPARISON WITH PREVIOUS PERFORMANCE")
        print("-" * 40)
        print("Previous performance (from your logs):")
        print("  - Average time: 400-700ms per message")
        print("  - Duplicate check time: 400-700ms")
        print("  - Extraction time: 0.1-0.2ms")
        print()
        print(f"New performance:")
        print(f"  - Average time: {avg_time:.1f}ms per message")
        print(f"  - Improvement: {((500 - avg_time) / 500 * 100):.1f}% faster")
        
        if avg_time < 50:
            improvement_factor = 500 / avg_time
            print(f"  - Speed increase: {improvement_factor:.1f}x faster!")
        
        await db_manager.close()
        
        print()
        print("🎉 PERFORMANCE TEST COMPLETED")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_cache_initialization():
    """Test cache initialization performance."""
    print("\n🧪 TESTING CACHE INITIALIZATION")
    print("=" * 60)
    
    try:
        from src.database import DatabaseManager
        from src.ca_detector import CADetector
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Test cache initialization
        start_time = time.perf_counter()
        recent_cas = await db_manager.get_recent_cas(hours=24)
        end_time = time.perf_counter()
        
        init_time = (end_time - start_time) * 1000
        
        print(f"Cache initialization time: {init_time:.1f}ms")
        print(f"Recent CAs loaded: {len(recent_cas)}")
        
        if init_time < 100:
            print("✅ EXCELLENT: Cache initialization under 100ms")
        elif init_time < 500:
            print("✅ GOOD: Cache initialization under 500ms")
        else:
            print("⚠️ SLOW: Cache initialization over 500ms")
        
        await db_manager.close()
        
    except Exception as e:
        print(f"❌ Cache test failed: {e}")

async def main():
    """Run all performance tests."""
    await test_cache_initialization()
    await test_ca_detection_performance()

if __name__ == "__main__":
    asyncio.run(main())
