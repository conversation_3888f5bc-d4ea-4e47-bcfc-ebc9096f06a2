"""Monitor and analyze the CA rescue mechanism performance."""

import time
import sys
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

def display_header():
    """Display monitoring header."""
    print("\033[2J\033[H")  # Clear screen
    print("🛡️ CA Rescue Mechanism Monitor")
    print("=" * 70)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Press Ctrl+C to exit")
    print("🔥 High-Volume → ⚡ Low-Volume Rescue Tracking")
    print("=" * 70)

def display_rescue_status():
    """Display current rescue mechanism status."""
    try:
        from src.ca_rescue_tracker import ca_rescue_tracker
        
        # Get current statistics
        stats = ca_rescue_tracker.get_rescue_statistics()
        pending_rescues = ca_rescue_tracker.get_pending_rescues()
        recent_rescues = ca_rescue_tracker.get_recent_rescues(24)
        
        # Display statistics
        print("📊 Rescue Mechanism Statistics:")
        print(f"   Total CAs filtered from high-volume: {stats['total_filtered']}")
        print(f"   Added to rescue-eligible cache: {stats['rescue_eligible_added']}")
        print(f"   Rescue attempts: {stats['rescue_attempts']}")
        print(f"   Successful rescues: {stats['successful_rescues']}")
        print(f"   Duplicate rescues prevented: {stats['duplicate_rescue_prevented']}")
        print(f"   Success rate: {stats['rescue_success_rate']:.1f}%")
        print()
        
        # Current status
        print("📋 Current Status:")
        print(f"   Rescue-eligible CAs: {stats['rescue_eligible_count']}")
        print(f"   Total rescued CAs: {stats['rescued_count']}")
        print(f"   Expired entries cleaned: {stats['expired_entries_cleaned']}")
        print()
        
        # Pending rescues
        if pending_rescues:
            print(f"⏳ Pending Rescues ({len(pending_rescues)}):")
            for pending in pending_rescues[:5]:  # Show top 5
                time_waiting = (datetime.now() - pending.filtered_timestamp).total_seconds() / 60
                print(f"   {pending.ca[:25]}...")
                print(f"      From: {pending.original_group_name}")
                print(f"      Waiting: {time_waiting:.1f} minutes")
                print(f"      Original mentions: {pending.mention_count}")
                print()
        else:
            print("⏳ Pending Rescues: None currently eligible")
            print()
        
        # Recent rescues
        if recent_rescues:
            print(f"🚀 Recent Rescues (Last 24 hours): {len(recent_rescues)}")
            for rescue in recent_rescues[:3]:  # Show top 3
                time_delay = (rescue.rescue_timestamp - rescue.filtered_timestamp).total_seconds() / 60
                print(f"   {rescue.ca[:25]}...")
                print(f"      Original: {rescue.original_group_name}")
                print(f"      Rescued by: {rescue.rescue_group_name}")
                print(f"      Delay: {time_delay:.1f} minutes")
                print(f"      Original mentions: {rescue.mention_count}")
                print()
        else:
            print("🚀 Recent Rescues: None in last 24 hours")
            print()
        
        # Group breakdown
        print("🏷️ Group Classifications:")
        print("   🔥 High-Volume (Trending Required):")
        for group_id, group_name in ca_rescue_tracker.high_volume_groups.items():
            print(f"      {group_name} ({group_id})")
        
        print("   ⚡ Low-Volume (Direct Forwarding):")
        for group_id, group_name in ca_rescue_tracker.low_volume_groups.items():
            print(f"      {group_name} ({group_id})")
        
    except Exception as e:
        print(f"❌ Error displaying rescue status: {e}")

def monitor_rescue_logs():
    """Monitor rescue mechanism activity from logs."""
    print("\n🔍 Recent Rescue Activity from Logs:")
    print("-" * 50)
    
    try:
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Look for rescue-related log entries
        rescue_lines = []
        for line in lines[-500:]:  # Check last 500 lines
            if any(keyword in line for keyword in [
                'RESCUE ELIGIBLE', 'RESCUE TRIGGERED', 'RESCUE EXECUTED', 
                'RESCUE PREVENTED', 'RESCUE BLOCKED', 'RESCUE FORWARDING'
            ]):
                rescue_lines.append(line.strip())
        
        if rescue_lines:
            print(f"Found {len(rescue_lines)} rescue-related log entries:")
            for line in rescue_lines[-10:]:  # Show last 10
                timestamp_match = line.split(' | ')
                if len(timestamp_match) >= 3:
                    timestamp = timestamp_match[0]
                    message = ' | '.join(timestamp_match[2:])
                    print(f"   {timestamp}: {message}")
        else:
            print("No recent rescue activity found in logs")
    
    except Exception as e:
        print(f"❌ Error reading rescue logs: {e}")

def analyze_rescue_effectiveness():
    """Analyze rescue mechanism effectiveness."""
    print("\n💡 Rescue Mechanism Analysis:")
    print("-" * 50)
    
    try:
        from src.ca_rescue_tracker import ca_rescue_tracker
        
        stats = ca_rescue_tracker.get_rescue_statistics()
        pending = ca_rescue_tracker.get_pending_rescues()
        recent = ca_rescue_tracker.get_recent_rescues(24)
        
        # Effectiveness analysis
        if stats['rescue_attempts'] > 0:
            success_rate = stats['rescue_success_rate']
            if success_rate >= 90:
                effectiveness = "🟢 EXCELLENT"
            elif success_rate >= 70:
                effectiveness = "🟡 GOOD"
            else:
                effectiveness = "🔴 NEEDS IMPROVEMENT"
            
            print(f"Rescue Success Rate: {success_rate:.1f}% - {effectiveness}")
        else:
            print("Rescue Success Rate: No attempts yet")
        
        # Timing analysis
        if recent:
            delays = []
            for rescue in recent:
                if rescue.rescue_timestamp and rescue.filtered_timestamp:
                    delay = (rescue.rescue_timestamp - rescue.filtered_timestamp).total_seconds() / 60
                    delays.append(delay)
            
            if delays:
                avg_delay = sum(delays) / len(delays)
                min_delay = min(delays)
                max_delay = max(delays)
                
                print(f"Rescue Timing Analysis:")
                print(f"   Average delay: {avg_delay:.1f} minutes")
                print(f"   Fastest rescue: {min_delay:.1f} minutes")
                print(f"   Slowest rescue: {max_delay:.1f} minutes")
        
        # Recommendations
        print(f"\nRecommendations:")
        
        if stats['rescue_eligible_count'] > 10:
            print("⚠️ High number of pending rescues - monitor low-volume group activity")
        
        if stats['duplicate_rescue_prevented'] > stats['successful_rescues']:
            print("✅ Duplicate prevention working well")
        
        if stats['successful_rescues'] == 0 and stats['rescue_eligible_added'] > 0:
            print("📊 No rescues yet - monitor for low-volume group CA appearances")
        
    except Exception as e:
        print(f"❌ Error analyzing rescue effectiveness: {e}")

def monitor_real_time():
    """Monitor rescue mechanism in real-time."""
    print("🛡️ Starting Rescue Mechanism Monitoring...")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            display_header()
            display_rescue_status()
            monitor_rescue_logs()
            analyze_rescue_effectiveness()
            
            print("\n" + "=" * 70)
            print("🔄 Refreshing in 30 seconds... (Ctrl+C to exit)")
            
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
        
        # Generate final report
        try:
            from src.ca_rescue_tracker import ca_rescue_tracker
            report = ca_rescue_tracker.generate_rescue_report()
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'data/rescue_report_{timestamp}.txt'
            
            with open(report_file, 'w') as f:
                f.write(report)
            
            print(f"📄 Final rescue report saved to {report_file}")
            
        except Exception as e:
            print(f"⚠️ Error saving final report: {e}")
            
    except Exception as e:
        print(f"❌ Monitoring error: {e}")

def show_current_status():
    """Show current rescue mechanism status."""
    display_header()
    display_rescue_status()
    monitor_rescue_logs()
    analyze_rescue_effectiveness()

def main():
    """Main monitoring function."""
    if len(sys.argv) > 1 and sys.argv[1] == 'status':
        show_current_status()
    else:
        print("🛡️ CA Rescue Mechanism Monitor")
        print("\nOptions:")
        print("  python monitor_rescue_mechanism.py        - Real-time monitoring")
        print("  python monitor_rescue_mechanism.py status - Show current status")
        print()
        
        choice = input("Start real-time monitoring? (y/n): ").lower()
        if choice in ['y', 'yes']:
            monitor_real_time()
        else:
            show_current_status()

if __name__ == "__main__":
    main()
