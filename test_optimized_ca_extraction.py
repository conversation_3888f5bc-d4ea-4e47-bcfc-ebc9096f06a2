#!/usr/bin/env python3
"""
Test Optimized CA Extraction Logic
Tests the improved CA extraction with standalone priority and smart deduplication
"""

import sys
import os

# Add project root to path
sys.path.append('.')

from src.message_parser import MessageParser
from src.logger_setup import setup_logging

# Setup logging
logger = setup_logging()

def test_ca_extraction_optimization():
    """Test the optimized CA extraction logic."""
    print("=" * 80)
    print("🔧 TESTING OPTIMIZED CA EXTRACTION LOGIC")
    print("=" * 80)
    
    parser = MessageParser()
    
    # Test Case 1: Message with both standalone CA and URL containing same CA
    test_case_1 = """
🚀 NEW TOKEN ALERT 🚀

Check this out: https://gmgn.ai/sol/token/7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn

7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn

Don't miss this opportunity!
"""
    
    print("\n📋 Test Case 1: Standalone CA + URL with same CA")
    print("Expected: Should extract only 1 CA (standalone priority)")
    print("Message preview:", test_case_1[:100] + "...")
    
    cas_1 = parser.extract_contract_addresses(test_case_1)
    print(f"✅ Result: {len(cas_1)} CA(s) extracted")
    for i, ca in enumerate(cas_1, 1):
        print(f"   CA {i}: {ca}")
    
    # Test Case 2: Message with only URL CA (no standalone)
    test_case_2 = """
🔥 HOT SIGNAL 🔥

New listing: https://pump.fun/9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

Check it out now!
"""
    
    print("\n📋 Test Case 2: Only URL CA (no standalone)")
    print("Expected: Should extract 1 CA from URL")
    print("Message preview:", test_case_2[:100] + "...")
    
    cas_2 = parser.extract_contract_addresses(test_case_2)
    print(f"✅ Result: {len(cas_2)} CA(s) extracted")
    for i, ca in enumerate(cas_2, 1):
        print(f"   CA {i}: {ca}")
    
    # Test Case 3: Message with multiple standalone CAs
    test_case_3 = """
🎯 MULTIPLE SIGNALS 🎯

First token:
7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn

Second token:
9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM

Both are trending!
"""
    
    print("\n📋 Test Case 3: Multiple standalone CAs")
    print("Expected: Should extract 2 CAs")
    print("Message preview:", test_case_3[:100] + "...")
    
    cas_3 = parser.extract_contract_addresses(test_case_3)
    print(f"✅ Result: {len(cas_3)} CA(s) extracted")
    for i, ca in enumerate(cas_3, 1):
        print(f"   CA {i}: {ca}")
    
    # Test Case 4: Message with standalone CA + different URL CA
    test_case_4 = """
🚀 DOUBLE ALERT 🚀

Primary signal:
7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn

Also check: https://dexscreener.com/solana/9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
"""
    
    print("\n📋 Test Case 4: Standalone CA + different URL CA")
    print("Expected: Should extract only 1 CA (standalone priority, URL ignored)")
    print("Message preview:", test_case_4[:100] + "...")
    
    cas_4 = parser.extract_contract_addresses(test_case_4)
    print(f"✅ Result: {len(cas_4)} CA(s) extracted")
    for i, ca in enumerate(cas_4, 1):
        print(f"   CA {i}: {ca}")
    
    # Test Case 5: GMGN markdown link format
    test_case_5 = """
🧠 GMGN FEATURED SIGNAL 🧠

[7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn](https://gmgn.ai/sol/token/7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn)

Trending now!
"""
    
    print("\n📋 Test Case 5: GMGN markdown link (no standalone)")
    print("Expected: Should extract 1 CA from URL")
    print("Message preview:", test_case_5[:100] + "...")
    
    cas_5 = parser.extract_contract_addresses(test_case_5)
    print(f"✅ Result: {len(cas_5)} CA(s) extracted")
    for i, ca in enumerate(cas_5, 1):
        print(f"   CA {i}: {ca}")
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 OPTIMIZATION TEST SUMMARY")
    print("=" * 80)
    print(f"Test 1 (Standalone + Same URL): {len(cas_1)} CA - {'✅ PASS' if len(cas_1) == 1 else '❌ FAIL'}")
    print(f"Test 2 (URL Only): {len(cas_2)} CA - {'✅ PASS' if len(cas_2) == 1 else '❌ FAIL'}")
    print(f"Test 3 (Multiple Standalone): {len(cas_3)} CAs - {'✅ PASS' if len(cas_3) == 2 else '❌ FAIL'}")
    print(f"Test 4 (Standalone + Different URL): {len(cas_4)} CA - {'✅ PASS' if len(cas_4) == 1 else '❌ FAIL'}")
    print(f"Test 5 (GMGN Markdown): {len(cas_5)} CA - {'✅ PASS' if len(cas_5) == 1 else '❌ FAIL'}")
    
    # Check if all tests passed
    all_passed = (
        len(cas_1) == 1 and  # Standalone priority
        len(cas_2) == 1 and  # URL extraction when no standalone
        len(cas_3) == 2 and  # Multiple standalone
        len(cas_4) == 1 and  # Standalone priority over different URL
        len(cas_5) == 1      # URL extraction from markdown
    )
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED! CA extraction optimization is working correctly.")
        print("✅ Standalone CAs have priority over URL CAs")
        print("✅ Duplicate CAs are properly prevented")
        print("✅ URL extraction works when no standalone CAs are found")
    else:
        print("\n❌ SOME TESTS FAILED! Please review the CA extraction logic.")
    
    print("=" * 80)

def test_message_formatting_optimization():
    """Test the optimized message formatting."""
    print("\n🔧 TESTING OPTIMIZED MESSAGE FORMATTING")
    print("=" * 80)
    
    # Import integration modules
    from src.cla_v2_integration import CLAv2Integration
    from src.bonkbot_integration import BonkBotIntegration
    from src.monaco_pnl_integration import MonacoPNLIntegration
    from src.winners_integration import WinnersIntegration
    
    # Test CA
    test_ca = "7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn"
    test_message = "🚀 NEW TOKEN ALERT 🚀\nCheck this out!"
    test_group = "🧠 GMGN Featured Signals"
    
    print(f"\n📋 Testing Message Formatting for CA: {test_ca[:20]}...")
    
    # Test each integration's message formatting
    integrations = [
        ("CLA v2.0", CLAv2Integration),
        ("BonkBot", BonkBotIntegration),
        ("Monaco PNL", MonacoPNLIntegration),
        ("WINNERS", WinnersIntegration)
    ]
    
    for name, integration_class in integrations:
        try:
            # Create instance (without actual initialization)
            integration = integration_class(None)  # Pass None for telegram_client

            # Test message formatting (handle different method signatures)
            if name == "BonkBot":
                # BonkBot has different signature
                formatted_message = integration._format_ca_message(test_ca, test_message)
            else:
                # Other integrations have 3 parameters
                formatted_message = integration._format_ca_message(test_ca, test_message, test_group)

            print(f"\n{name} Integration:")
            print(f"   Formatted message: '{formatted_message}'")
            print(f"   Length: {len(formatted_message)} characters")
            print(f"   Concise: {'✅ YES' if len(formatted_message) <= 50 else '❌ NO'}")

        except Exception as e:
            print(f"\n{name} Integration: ❌ Error testing - {e}")
    
    print("\n✅ Message formatting optimization complete!")
    print("All integrations now use concise CA-only format for maximum efficiency.")

if __name__ == "__main__":
    test_ca_extraction_optimization()
    test_message_formatting_optimization()
