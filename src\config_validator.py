"""Configuration validation for CLA v2.0 Bot production deployment."""

import os
import re
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

class ConfigurationError(Exception):
    """Raised when configuration validation fails."""
    pass

class ConfigValidator:
    """Validates configuration on startup to catch errors early."""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
    def validate_all(self) -> bool:
        """Validate all configuration sections."""
        logger.info("🔍 Starting configuration validation...")
        
        try:
            self._validate_telegram_config()
            self._validate_group_config()
            self._validate_integration_config()
            self._validate_database_config()
            self._validate_performance_config()
            self._validate_security_config()
            self._validate_paths_and_permissions()
            
            if self.errors:
                error_msg = f"Configuration validation failed with {len(self.errors)} errors:\n" + "\n".join(f"  - {error}" for error in self.errors)
                logger.error(error_msg)
                raise ConfigurationError(error_msg)
            
            if self.warnings:
                warning_msg = f"Configuration validation completed with {len(self.warnings)} warnings:\n" + "\n".join(f"  - {warning}" for warning in self.warnings)
                logger.warning(warning_msg)
            
            logger.info("✅ Configuration validation passed successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration validation failed: {e}")
            raise
    
    def _validate_telegram_config(self):
        """Validate Telegram API configuration."""
        logger.debug("Validating Telegram configuration...")
        
        # Required fields
        api_id = os.getenv('TELEGRAM_API_ID')
        api_hash = os.getenv('TELEGRAM_API_HASH')
        phone = os.getenv('TELEGRAM_PHONE')
        
        if not api_id:
            self.errors.append("TELEGRAM_API_ID is required")
        elif not api_id.isdigit():
            self.errors.append("TELEGRAM_API_ID must be a valid integer")
        
        if not api_hash:
            self.errors.append("TELEGRAM_API_HASH is required")
        elif len(api_hash) != 32:
            self.errors.append("TELEGRAM_API_HASH must be 32 characters long")
        
        if not phone:
            self.errors.append("TELEGRAM_PHONE is required")
        elif not re.match(r'^\+?[1-9]\d{1,14}$', phone):
            self.errors.append("TELEGRAM_PHONE must be a valid phone number")
        
        session_name = os.getenv('TELEGRAM_SESSION_NAME', 'cla_bot_production')
        if not re.match(r'^[a-zA-Z0-9_-]+$', session_name):
            self.errors.append("TELEGRAM_SESSION_NAME contains invalid characters")
    
    def _validate_group_config(self):
        """Validate group configuration."""
        logger.debug("Validating group configuration...")
        
        # Primary group
        target_group_id = os.getenv('TARGET_GROUP_ID')
        if not target_group_id:
            self.errors.append("TARGET_GROUP_ID is required")
        else:
            try:
                group_id = int(target_group_id)
                if group_id >= 0:
                    self.errors.append("TARGET_GROUP_ID must be negative (Telegram group ID)")
            except ValueError:
                self.errors.append("TARGET_GROUP_ID must be a valid integer")
        
        # Additional groups
        additional_ids = os.getenv('ADDITIONAL_GROUP_IDS', '')
        additional_names = os.getenv('ADDITIONAL_GROUP_NAMES', '')
        additional_status = os.getenv('ADDITIONAL_GROUP_STATUS', '')
        
        if additional_ids:
            try:
                ids = [int(id.strip()) for id in additional_ids.split(',') if id.strip()]
                names = [name.strip() for name in additional_names.split(',') if name.strip()]
                statuses = [status.strip().upper() for status in additional_status.split(',') if status.strip()]
                
                # Check if all arrays have same length
                if len(names) != len(ids):
                    self.warnings.append(f"Group names count ({len(names)}) doesn't match IDs count ({len(ids)})")
                
                if len(statuses) != len(ids):
                    self.warnings.append(f"Group status count ({len(statuses)}) doesn't match IDs count ({len(ids)})")
                
                # Validate group IDs are negative
                for group_id in ids:
                    if group_id >= 0:
                        self.errors.append(f"Group ID {group_id} must be negative (Telegram group ID)")
                
                # Validate status values
                valid_statuses = {'ACTIVE', 'PAUSED', 'DISABLED'}
                for status in statuses:
                    if status not in valid_statuses:
                        self.errors.append(f"Invalid group status: {status}. Must be one of {valid_statuses}")
                        
            except ValueError as e:
                self.errors.append(f"Invalid additional group IDs format: {e}")
    
    def _validate_integration_config(self):
        """Validate integration endpoints."""
        logger.debug("Validating integration configuration...")
        
        # BonkBot
        bonkbot_username = os.getenv('BONKBOT_USERNAME')
        if bonkbot_username and not bonkbot_username.startswith('@'):
            self.warnings.append("BONKBOT_USERNAME should start with @")
        
        bonkbot_chat_id = os.getenv('BONKBOT_CHAT_ID')
        if bonkbot_chat_id:
            try:
                int(bonkbot_chat_id)
            except ValueError:
                self.errors.append("BONKBOT_CHAT_ID must be a valid integer")
        
        # CLA v2.0
        cla_v2_group_id = os.getenv('CLA_V2_GROUP_ID')
        if cla_v2_group_id:
            try:
                group_id = int(cla_v2_group_id)
                if group_id >= 0:
                    self.errors.append("CLA_V2_GROUP_ID must be negative (Telegram group ID)")
            except ValueError:
                self.errors.append("CLA_V2_GROUP_ID must be a valid integer")
        
        # Monaco PNL
        monaco_group_id = os.getenv('MONACO_PNL_GROUP_ID')
        if monaco_group_id:
            try:
                group_id = int(monaco_group_id)
                if group_id >= 0:
                    self.errors.append("MONACO_PNL_GROUP_ID must be negative (Telegram group ID)")
            except ValueError:
                self.errors.append("MONACO_PNL_GROUP_ID must be a valid integer")
    
    def _validate_database_config(self):
        """Validate database configuration."""
        logger.debug("Validating database configuration...")
        
        db_path = os.getenv('DATABASE_PATH', './data/cla_bot.db')
        db_dir = Path(db_path).parent
        
        # Check if directory exists or can be created
        try:
            db_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            self.errors.append(f"Cannot create database directory: {db_dir}")
        except Exception as e:
            self.errors.append(f"Database path validation failed: {e}")
        
        # Validate cache settings
        cache_expiry = os.getenv('CA_CACHE_EXPIRY_HOURS', '168')
        try:
            hours = int(cache_expiry)
            if hours < 1 or hours > 720:  # 1 hour to 30 days
                self.warnings.append(f"CA_CACHE_EXPIRY_HOURS ({hours}) outside recommended range (1-720)")
        except ValueError:
            self.errors.append("CA_CACHE_EXPIRY_HOURS must be a valid integer")
        
        max_cache_size = os.getenv('MAX_CACHE_SIZE', '50000')
        try:
            size = int(max_cache_size)
            if size < 1000:
                self.warnings.append(f"MAX_CACHE_SIZE ({size}) is very small, may impact performance")
        except ValueError:
            self.errors.append("MAX_CACHE_SIZE must be a valid integer")
    
    def _validate_performance_config(self):
        """Validate performance-related configuration."""
        logger.debug("Validating performance configuration...")
        
        # Trending analysis
        trending_enabled = os.getenv('TRENDING_ENABLED', 'true').lower()
        if trending_enabled not in ['true', 'false']:
            self.errors.append("TRENDING_ENABLED must be 'true' or 'false'")
        
        if trending_enabled == 'true':
            # Validate trending parameters
            try:
                min_mentions = int(os.getenv('TRENDING_MIN_MENTIONS', '6'))
                if min_mentions < 1:
                    self.errors.append("TRENDING_MIN_MENTIONS must be >= 1")
                elif min_mentions > 20:
                    self.warnings.append(f"TRENDING_MIN_MENTIONS ({min_mentions}) is very high")
                
                time_window = int(os.getenv('TRENDING_TIME_WINDOW_MINUTES', '8'))
                if time_window < 1:
                    self.errors.append("TRENDING_TIME_WINDOW_MINUTES must be >= 1")
                elif time_window > 60:
                    self.warnings.append(f"TRENDING_TIME_WINDOW_MINUTES ({time_window}) is very long")
                    
            except ValueError as e:
                self.errors.append(f"Invalid trending configuration: {e}")
        
        # Database connection pool
        try:
            pool_size = int(os.getenv('DATABASE_CONNECTION_POOL_SIZE', '20'))
            if pool_size < 5:
                self.warnings.append(f"DATABASE_CONNECTION_POOL_SIZE ({pool_size}) is small")
            elif pool_size > 100:
                self.warnings.append(f"DATABASE_CONNECTION_POOL_SIZE ({pool_size}) is very large")
        except ValueError:
            self.errors.append("DATABASE_CONNECTION_POOL_SIZE must be a valid integer")
    
    def _validate_security_config(self):
        """Validate security configuration."""
        logger.debug("Validating security configuration...")
        
        # Message length limit
        try:
            max_length = int(os.getenv('MAX_MESSAGE_LENGTH', '10000'))
            if max_length < 1000:
                self.warnings.append(f"MAX_MESSAGE_LENGTH ({max_length}) is very small")
            elif max_length > 100000:
                self.warnings.append(f"MAX_MESSAGE_LENGTH ({max_length}) is very large")
        except ValueError:
            self.errors.append("MAX_MESSAGE_LENGTH must be a valid integer")
        
        # Resource limits
        try:
            max_memory = int(os.getenv('MAX_MEMORY_MB', '2048'))
            if max_memory < 512:
                self.warnings.append(f"MAX_MEMORY_MB ({max_memory}) may be insufficient")
        except ValueError:
            self.errors.append("MAX_MEMORY_MB must be a valid integer")
    
    def _validate_paths_and_permissions(self):
        """Validate file paths and permissions."""
        logger.debug("Validating paths and permissions...")
        
        # Working directory
        working_dir = os.getenv('WORKING_DIRECTORY', '/opt/cla-bot')
        if not Path(working_dir).exists():
            self.warnings.append(f"Working directory does not exist: {working_dir}")
        
        # Log directory
        log_path = os.getenv('LOG_FILE_PATH', '/opt/cla-bot/logs/cla_bot.log')
        log_dir = Path(log_path).parent
        try:
            log_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            self.errors.append(f"Cannot create log directory: {log_dir}")
        
        # Backup directory
        backup_path = os.getenv('BACKUP_PATH', '/opt/cla-bot/backups')
        backup_dir = Path(backup_path)
        try:
            backup_dir.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            self.errors.append(f"Cannot create backup directory: {backup_dir}")

def validate_production_config() -> bool:
    """Main function to validate production configuration."""
    validator = ConfigValidator()
    return validator.validate_all()

if __name__ == "__main__":
    # Standalone validation script
    try:
        validate_production_config()
        print("✅ Configuration validation passed")
    except ConfigurationError as e:
        print(f"❌ Configuration validation failed: {e}")
        exit(1)
    except Exception as e:
        print(f"❌ Unexpected error during validation: {e}")
        exit(1)
