"""Contract Address detection and validation for CLA v2.0 Bot."""

import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Optional, Set, Dict
import base58
from loguru import logger

from src.database import DatabaseManager
from src.message_parser import MessageParser
from config import config

class CADetector:
    """Handles contract address detection, validation, and duplicate prevention."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.parser = MessageParser()
        self.processed_cache: Set[str] = set()
        self.last_cleanup = datetime.now()
        self.cache_initialized = False

        # Async locks for CA processing to prevent race conditions
        self.ca_processing_locks: Dict[str, asyncio.Lock] = {}
        self.lock_cleanup_threshold = 1000  # Clean up locks after this many
    
    async def _get_ca_lock(self, ca: str) -> asyncio.Lock:
        """Get or create an async lock for a specific CA to prevent race conditions."""
        if ca not in self.ca_processing_locks:
            self.ca_processing_locks[ca] = asyncio.Lock()

            # Cleanup old locks periodically
            if len(self.ca_processing_locks) > self.lock_cleanup_threshold:
                await self._cleanup_old_locks()

        return self.ca_processing_locks[ca]

    async def _cleanup_old_locks(self):
        """Clean up old CA processing locks to prevent memory leaks."""
        try:
            # Keep only the most recent locks (simple cleanup)
            if len(self.ca_processing_locks) > self.lock_cleanup_threshold:
                # Remove half of the locks (oldest ones)
                keys_to_remove = list(self.ca_processing_locks.keys())[:self.lock_cleanup_threshold // 2]
                for key in keys_to_remove:
                    if key in self.ca_processing_locks:
                        del self.ca_processing_locks[key]
                logger.debug(f"Cleaned up {len(keys_to_remove)} CA processing locks")
        except Exception as e:
            logger.error(f"Error cleaning up CA locks: {e}")

    async def _initialize_cache_if_needed(self):
        """Initialize cache with recent CAs to avoid database calls during processing."""
        if self.cache_initialized:
            return

        try:
            # Pre-populate cache with CAs from the last 24 hours for maximum performance
            recent_cas = await self.db_manager.get_recent_cas(hours=24)
            self.processed_cache.update(recent_cas)
            self.cache_initialized = True
            logger.info(f"🚀 PERFORMANCE: Cache pre-populated with {len(recent_cas)} recent CAs")
        except Exception as e:
            logger.warning(f"Failed to pre-populate cache: {e}")
            self.cache_initialized = True  # Don't retry on every message

    async def process_message(self, message_text: str, message_id: int, group_id: int) -> List[str]:
        """Process a message and extract new contract addresses with global duplicate prevention."""
        start_time = time.perf_counter()
        try:
            # Initialize cache on first use for maximum performance
            await self._initialize_cache_if_needed()

            # Enhanced debug logging for GMGN (reduced verbosity for performance)
            if group_id == -1002202241417:  # GMGN Featured Signals
                logger.info(f"🧠 GMGN CA DETECTOR: Processing message {message_id} with text: {message_text[:200]}...")

            # Extract contract addresses from message
            extraction_start = time.perf_counter()
            extracted_cas = self.parser.extract_contract_addresses(message_text)
            extraction_time = (time.perf_counter() - extraction_start) * 1000

            # Enhanced debug logging for GMGN extraction
            if group_id == -1002202241417:  # GMGN Featured Signals
                logger.info(f"🧠 GMGN EXTRACTION: Found {len(extracted_cas)} CAs in message: {extracted_cas}")

            if not extracted_cas:
                if group_id == -1002202241417:  # GMGN Featured Signals
                    logger.info(f"🧠 GMGN NO CAS: No contract addresses found in message")
                else:
                    logger.debug("No contract addresses found in message")
                return []

            # Optimized duplicate filtering with batch processing
            duplicate_check_start = time.perf_counter()
            new_cas = await self._process_cas_batch(extracted_cas, message_id, group_id)
            duplicate_check_time = (time.perf_counter() - duplicate_check_start) * 1000

            # Performance logging for optimization
            total_time = (time.perf_counter() - start_time) * 1000
            if total_time > 50:  # Log slow CA detection
                logger.warning(f"⚠️ SLOW CA DETECTION: {total_time:.1f}ms (extract: {extraction_time:.1f}ms, dup: {duplicate_check_time:.1f}ms) for {len(extracted_cas)} CAs")

            # Record duplicate filtering in enhanced stats
            # TODO: Add enhanced stats tracking for duplicate filtering

            # Periodic cleanup
            await self._periodic_cleanup()

            return new_cas

        except Exception as e:
            logger.error(f"Error processing message for CAs: {e}")
            return []

    async def _process_cas_batch(self, extracted_cas: List[str], message_id: int, group_id: int) -> List[str]:
        """Ultra-fast batch processing of CAs with optimized cache-first approach."""
        new_cas = []

        # PERFORMANCE OPTIMIZATION: Cache-first approach with minimal database calls
        for ca in extracted_cas:
            # Step 1: Quick cache check (no locks, no database calls)
            if ca in self.processed_cache:
                # Log duplicate for GMGN only
                if group_id == -1002202241417:
                    logger.info(f"🧠 GMGN DUPLICATE: {ca} already in cache")
                continue

            # Step 2: Get lock for this specific CA (minimal lock scope)
            ca_lock = await self._get_ca_lock(ca)

            async with ca_lock:
                # Step 3: Double-check cache after acquiring lock (race condition protection)
                if ca in self.processed_cache:
                    if group_id == -1002202241417:
                        logger.info(f"🧠 GMGN DUPLICATE: {ca} already in cache (race condition)")
                    continue

                # Step 4: Try to add to database directly (let database handle duplicates)
                db_inserted = await self.db_manager.add_contract_address(ca, message_id, group_id)

                if db_inserted:
                    # New CA successfully added
                    self.processed_cache.add(ca)
                    new_cas.append(ca)

                    # Enhanced debug logging for GMGN
                    if group_id == -1002202241417:  # GMGN Featured Signals
                        logger.info(f"🧠 GMGN NEW CA: {ca} added to cache and database")
                    else:
                        logger.debug(f"New CA detected: {ca}")
                else:
                    # CA was already in database (duplicate)
                    self.processed_cache.add(ca)
                    if group_id == -1002202241417:
                        logger.info(f"🧠 GMGN DUPLICATE: {ca} already in database")
                    else:
                        logger.debug(f"CA already in database: {ca}")

        # Periodic cleanup (less frequent for performance)
        if len(new_cas) > 0:
            await self._periodic_cleanup()

        return new_cas
    
    async def _is_new_ca_global(self, ca: str, source_group_id: int = None) -> bool:
        """DEPRECATED: Legacy method for backward compatibility.

        Performance optimization: This method is no longer used in the main flow.
        Database duplicate checking is now handled directly in _process_cas_batch
        for maximum performance.
        """
        # Simple cache check only (no database calls for performance)
        return ca not in self.processed_cache

    async def _is_low_volume_group(self, group_id: int) -> bool:
        """Check if a group is classified as low-volume for direct forwarding."""
        try:
            # Import here to avoid circular imports
            from config import config

            # Check if group is in low-volume groups list
            if hasattr(config.trending, 'low_volume_groups') and config.trending.low_volume_groups:
                return group_id in config.trending.low_volume_groups

            # Fallback to default low-volume groups
            default_low_volume_groups = {
                -1002380594298,  # FREE WHALE SIGNALS
                -1002270988204,  # Solana Activity Tracker
                -1002064145465,  # 🌹 MANIFEST
                -1001763265784,  # 👤 Mark Degens
                -1002139128702   # 💎 FINDERTRENDING
            }
            return group_id in default_low_volume_groups

        except Exception as e:
            logger.error(f"Error checking if group is low-volume: {e}")
            return False

    async def _is_new_ca(self, ca: str) -> bool:
        """Legacy method - redirects to global check for backward compatibility."""
        return await self._is_new_ca_global(ca)
    
    async def validate_ca_format(self, ca: str) -> bool:
        """Validate contract address format."""
        try:
            # Check length
            if len(ca) != 44:
                logger.debug(f"Invalid CA length: {len(ca)}")
                return False
            
            # Check base58 encoding
            try:
                decoded = base58.b58decode(ca)
            except Exception:
                logger.debug(f"Invalid base58 encoding: {ca}")
                return False
            
            # Check decoded length (should be 32 bytes)
            if len(decoded) != 32:
                logger.debug(f"Invalid decoded length: {len(decoded)}")
                return False
            
            # Check if it's not all zeros
            if decoded == b'\x00' * 32:
                logger.debug("CA is all zeros")
                return False
            
            # Check for common invalid patterns
            if self._is_invalid_pattern(ca):
                logger.debug(f"Invalid CA pattern: {ca}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating CA format: {e}")
            return False
    
    def _is_invalid_pattern(self, ca: str) -> bool:
        """Check for common invalid CA patterns."""
        # Check for repeated characters (likely invalid)
        if len(set(ca)) < 10:  # Too few unique characters
            return True
        
        # Check for common test addresses or invalid patterns
        invalid_patterns = [
            '1' * 44,  # All ones
            'A' * 44,  # All A's
            'test',    # Contains 'test'
            'fake',    # Contains 'fake'
        ]
        
        ca_lower = ca.lower()
        for pattern in invalid_patterns:
            if pattern.lower() in ca_lower:
                return True
        
        return False
    
    async def get_ca_stats(self, ca: str) -> dict:
        """Get statistics for a specific CA."""
        try:
            # This could be extended to fetch on-chain data
            # For now, return basic info from database
            return {
                'ca': ca,
                'valid_format': await self.validate_ca_format(ca),
                'processed': await self.db_manager.is_ca_processed(ca)
            }
            
        except Exception as e:
            logger.error(f"Error getting CA stats: {e}")
            return {}
    
    async def _periodic_cleanup(self):
        """Perform periodic cleanup of caches."""
        try:
            now = datetime.now()
            
            # Cleanup every hour
            if now - self.last_cleanup > timedelta(hours=1):
                logger.debug("Performing periodic cache cleanup")
                
                # Clear local cache (it will be rebuilt from database as needed)
                self.processed_cache.clear()
                
                # Cleanup database cache
                await self.db_manager.cleanup_old_cache()
                
                self.last_cleanup = now
                logger.debug("Cache cleanup completed")
                
        except Exception as e:
            logger.error(f"Error during periodic cleanup: {e}")
    
    async def force_cleanup(self):
        """Force immediate cleanup of all caches."""
        try:
            logger.info("Forcing cache cleanup")
            
            # Clear local cache
            self.processed_cache.clear()
            
            # Cleanup database cache
            await self.db_manager.cleanup_old_cache()
            
            self.last_cleanup = datetime.now()
            logger.info("Force cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during force cleanup: {e}")
    
    async def add_ca_to_whitelist(self, ca: str):
        """Add CA to whitelist (always process even if seen before)."""
        try:
            # Remove from caches to allow reprocessing
            self.processed_cache.discard(ca)
            
            # Could implement a whitelist table in database
            logger.info(f"CA added to whitelist: {ca}")
            
        except Exception as e:
            logger.error(f"Error adding CA to whitelist: {e}")
    
    async def get_recent_cas(self, hours: int = 24) -> List[dict]:
        """Get recently processed CAs."""
        try:
            # This would query the database for recent CAs
            # Implementation depends on specific requirements
            logger.debug(f"Getting CAs from last {hours} hours")
            return []
            
        except Exception as e:
            logger.error(f"Error getting recent CAs: {e}")
            return []
    
    def get_cache_size(self) -> int:
        """Get current cache size."""
        return len(self.processed_cache)
    
    async def is_ca_in_cache(self, ca: str) -> bool:
        """Check if CA is in any cache."""
        return (ca in self.processed_cache or 
                await self.db_manager.is_ca_processed(ca))
