"""Simple test to verify message reception from GMGN and other groups."""

import asyncio
import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_message_reception():
    """Test basic message reception from all groups."""
    print("🔍 Testing Message Reception from All Groups...")
    
    try:
        from src.telegram_client import TelegramClientManager
        from config import config
        from telethon import events
        
        # Initialize Telegram client
        client_manager = TelegramClientManager()
        await client_manager.initialize()
        
        print(f"✅ Telegram client initialized")
        
        # Get active group IDs
        active_groups = config.target_group.active_group_ids
        print(f"📡 Monitoring {len(active_groups)} active groups:")
        for group_id in active_groups:
            group_name = "PRIMARY" if group_id == config.target_group.group_id else "UNKNOWN"
            for i, gid in enumerate(config.target_group.additional_group_ids):
                if gid == group_id:
                    group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {gid}"
                    break
            print(f"  - {group_name} ({group_id})")
        
        # Message counter
        message_count = 0
        gmgn_message_count = 0
        
        # Simple message handler
        @client_manager.client.on(events.NewMessage(chats=active_groups))
        async def handle_test_message(event):
            nonlocal message_count, gmgn_message_count
            
            message_count += 1
            chat_id = event.chat_id
            message_text = event.message.text or ""
            
            # Get group name
            group_name = "UNKNOWN"
            if chat_id == config.target_group.group_id:
                group_name = config.target_group.group_name
            else:
                for i, gid in enumerate(config.target_group.additional_group_ids):
                    if gid == chat_id:
                        group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {gid}"
                        break
            
            # Special handling for GMGN
            if chat_id == -1002202241417:  # GMGN Featured Signals
                gmgn_message_count += 1
                print(f"🧠 GMGN MESSAGE #{gmgn_message_count}: ID={event.message.id} | Text={message_text[:100]}...")
            else:
                print(f"📨 MESSAGE #{message_count}: {group_name} | ID={event.message.id} | Text={message_text[:50]}...")
        
        print(f"\n🚀 Message reception test started at {datetime.now()}")
        print("Waiting for messages from any monitored group...")
        print("Press Ctrl+C to stop the test\n")
        
        # Run for a specified time or until interrupted
        try:
            await asyncio.sleep(300)  # Wait 5 minutes
        except KeyboardInterrupt:
            print("\n⏹️ Test stopped by user")
        
        print(f"\n📊 Test Results:")
        print(f"Total messages received: {message_count}")
        print(f"GMGN messages received: {gmgn_message_count}")
        
        if message_count == 0:
            print("❌ NO MESSAGES RECEIVED - This indicates a connection or permission issue")
        elif gmgn_message_count == 0:
            print("⚠️ No GMGN messages received - GMGN may be quiet or there's a specific issue")
        else:
            print("✅ Message reception working correctly")
        
        await client_manager.disconnect()
        
    except Exception as e:
        print(f"❌ Message reception test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run message reception test."""
    print("🚨 CRITICAL TEST: Message Reception from GMGN and All Groups\n")
    
    try:
        asyncio.run(test_message_reception())
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    main()
