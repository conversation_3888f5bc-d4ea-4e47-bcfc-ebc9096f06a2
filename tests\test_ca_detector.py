"""Tests for CA detection and validation."""

import unittest
import asyncio
import tempfile
import os
from src.ca_detector import CADetector
from src.database import DatabaseManager

# Set test environment variables to avoid config validation errors
os.environ.setdefault('TELEGRAM_API_ID', '12345')
os.environ.setdefault('TELEGRAM_API_HASH', 'test_hash')
os.environ.setdefault('TELEGRAM_PHONE', '+1234567890')

class TestCADetector(unittest.TestCase):
    """Test cases for CADetector class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create temporary database
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        # Create database manager with temp database
        self.db_manager = DatabaseManager()
        self.db_manager.db_path = self.temp_db.name
        
        # Create CA detector
        self.ca_detector = CADetector(self.db_manager)
        
        # Initialize database
        asyncio.run(self.db_manager.initialize())
    
    def tearDown(self):
        """Clean up test fixtures."""
        # Close database and remove temp file
        asyncio.run(self.db_manager.close())
        os.unlink(self.temp_db.name)
    
    def test_validate_ca_format_valid(self):
        """Test validation of valid CA format."""
        valid_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
        
        result = asyncio.run(self.ca_detector.validate_ca_format(valid_ca))
        self.assertTrue(result)
    
    def test_validate_ca_format_invalid_length(self):
        """Test validation rejects invalid length."""
        # Too short
        short_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjy"
        
        # Too long
        long_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk123"
        
        result1 = asyncio.run(self.ca_detector.validate_ca_format(short_ca))
        result2 = asyncio.run(self.ca_detector.validate_ca_format(long_ca))
        
        self.assertFalse(result1)
        self.assertFalse(result2)
    
    def test_validate_ca_format_invalid_characters(self):
        """Test validation rejects invalid base58 characters."""
        # Contains invalid character '0'
        invalid_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjyb0nk"
        
        result = asyncio.run(self.ca_detector.validate_ca_format(invalid_ca))
        self.assertFalse(result)
    
    def test_validate_ca_format_invalid_patterns(self):
        """Test validation rejects common invalid patterns."""
        # All ones
        ones_ca = "1" * 44
        
        # All A's
        as_ca = "A" * 44
        
        result1 = asyncio.run(self.ca_detector.validate_ca_format(ones_ca))
        result2 = asyncio.run(self.ca_detector.validate_ca_format(as_ca))
        
        self.assertFalse(result1)
        self.assertFalse(result2)
    
    def test_process_message_new_ca(self):
        """Test processing message with new CA."""
        message = """🔥 NEW SIGNAL 🔥
        
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        result = asyncio.run(self.ca_detector.process_message(message, 123, -1001234567))
        
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")
    
    def test_process_message_duplicate_ca(self):
        """Test processing message with duplicate CA."""
        message = """🔥 NEW SIGNAL 🔥
        
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        # Process first time
        result1 = asyncio.run(self.ca_detector.process_message(message, 123, -1001234567))
        
        # Process second time (should be duplicate)
        result2 = asyncio.run(self.ca_detector.process_message(message, 124, -1001234567))
        
        self.assertEqual(len(result1), 1)
        self.assertEqual(len(result2), 0)  # Should be empty due to duplicate
    
    def test_process_message_no_ca(self):
        """Test processing message with no CA."""
        message = """Just a regular message
        
Nothing special here."""
        
        result = asyncio.run(self.ca_detector.process_message(message, 123, -1001234567))
        
        self.assertEqual(len(result), 0)
    
    def test_process_message_invalid_ca(self):
        """Test processing message with invalid CA."""
        message = """🔥 FAKE SIGNAL 🔥
        
invalidcontractaddress123456789"""
        
        result = asyncio.run(self.ca_detector.process_message(message, 123, -1001234567))
        
        self.assertEqual(len(result), 0)
    
    def test_cache_functionality(self):
        """Test that cache prevents duplicate processing."""
        ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
        
        # First check - should be new
        is_new1 = asyncio.run(self.ca_detector._is_new_ca(ca))
        
        # Mark as processed
        asyncio.run(self.db_manager.mark_ca_processed(ca))
        
        # Second check - should not be new
        is_new2 = asyncio.run(self.ca_detector._is_new_ca(ca))
        
        self.assertTrue(is_new1)
        self.assertFalse(is_new2)
    
    def test_get_cache_size(self):
        """Test cache size tracking."""
        initial_size = self.ca_detector.get_cache_size()
        
        # Add some CAs to local cache
        self.ca_detector.processed_cache.add("test1")
        self.ca_detector.processed_cache.add("test2")
        
        new_size = self.ca_detector.get_cache_size()
        
        self.assertEqual(new_size, initial_size + 2)
    
    def test_force_cleanup(self):
        """Test force cleanup functionality."""
        # Add some items to cache
        self.ca_detector.processed_cache.add("test1")
        self.ca_detector.processed_cache.add("test2")
        
        initial_size = self.ca_detector.get_cache_size()
        self.assertGreater(initial_size, 0)
        
        # Force cleanup
        asyncio.run(self.ca_detector.force_cleanup())
        
        # Cache should be cleared
        final_size = self.ca_detector.get_cache_size()
        self.assertEqual(final_size, 0)

if __name__ == '__main__':
    unittest.main()
