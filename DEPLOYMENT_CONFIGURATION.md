# CLA v2 Telegram Bot - Deployment Configuration

## Working Directory Path

**Correct Working Directory**: `C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2`

### Path Verification
- ✅ **Verified Working**: This path has been tested and confirmed working for bot startup
- ✅ **All Dependencies**: All required files and modules are accessible from this path
- ✅ **Database Access**: Database files are correctly located relative to this path
- ✅ **Configuration Files**: All config files are properly accessible

### Startup Commands

#### Successful Bot Launch
```bash
# Change to correct working directory
cd "C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2"

# Launch bot
python main.py
```

#### Alternative Launch Methods
```bash
# Using launch-process tool with correct cwd
launch-process:
  command: python main.py
  cwd: C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2
  wait: false
```

### Common Path Issues and Solutions

#### ❌ **INCORRECT PATHS** (Do not use):
- `/c/Users/<USER>/Desktop/NEW BOTS 4 27/CLA v2` (Unix-style path on Windows)
- `C:/Users/<USER>/Desktop/NEW BOTS 4 27/CLA v2` (Forward slashes may cause issues)
- Relative paths without proper working directory

#### ✅ **CORRECT PATH FORMAT**:
- `C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2` (Windows backslash format)

### Startup Script

Create a batch file for easy startup:

**File**: `start_bot.bat`
```batch
@echo off
cd /d "C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2"
echo Starting CLA v2 Telegram Bot...
python main.py
pause
```

### Directory Structure Verification

The working directory should contain:
```
C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2\
├── main.py                    # Main bot entry point
├── src/                       # Source code directory
│   ├── bot.py                # Main bot class
│   ├── message_parser.py     # CA extraction logic
│   ├── ca_detector.py        # CA detection and validation
│   ├── database.py           # Database management
│   └── ...                   # Other modules
├── data/                     # Database and data files
├── logs/                     # Log files
├── config/                   # Configuration files
└── requirements.txt          # Python dependencies
```

### Environment Setup

Ensure the Python environment is properly configured:
```bash
# Activate virtual environment (if using)
.venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import src.bot; print('✅ Bot modules accessible')"
```

### Troubleshooting

#### Path-Related Launch Failures
1. **Error**: "Starting directory (cwd) does not exist"
   - **Solution**: Use the exact path `C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2`

2. **Error**: "Module not found"
   - **Solution**: Ensure working directory is set correctly before running Python

3. **Error**: "Database file not found"
   - **Solution**: Verify the `data/` directory exists in the working directory

#### Verification Commands
```bash
# Test path accessibility
cd "C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2"
dir

# Test Python module imports
python -c "import sys; sys.path.append('.'); from src.message_parser import MessageParser; print('✅ Imports working')"

# Test bot startup (dry run)
python -c "from src.bot import CLABot; print('✅ Bot class accessible')"
```

## Deployment Checklist

Before starting the bot:
- [ ] ✅ Working directory path verified: `C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2`
- [ ] ✅ Python environment activated
- [ ] ✅ Dependencies installed
- [ ] ✅ Configuration files present
- [ ] ✅ Database directory accessible
- [ ] ✅ Log directory writable
- [ ] ✅ Network connectivity for Telegram API

## Performance Monitoring

After successful startup, monitor:
- [ ] ✅ All 8 groups connected and active
- [ ] ✅ 3 forwarding destinations operational (BonkBot, CLA v2.0, Monaco PNL)
- [ ] ✅ CA extraction working for all message formats
- [ ] ✅ Processing times under performance targets
- [ ] ✅ No error messages in logs

---

**Last Updated**: 2025-07-31  
**Verified Working**: ✅ Confirmed functional  
**Next Review**: Monitor for any path-related issues during regular operation
