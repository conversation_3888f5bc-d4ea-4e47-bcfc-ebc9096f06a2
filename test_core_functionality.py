"""Test core functionality without config dependencies."""

import os
import sys

# Add src to path
sys.path.insert(0, 'src')

from src.message_parser import MessageParser

def test_message_parsing():
    """Test message parsing functionality."""
    print("🧪 Testing Message Parsing...")
    
    parser = MessageParser()
    
    # Test 1: Extract CA from standalone message
    message1 = """🔥 NEW SIGNAL ALERT 🔥
    
Check this out!

67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
    
    cas1 = parser.extract_contract_addresses(message1)
    print(f"✅ Standalone CA extraction: {len(cas1)} CAs found")
    if cas1:
        print(f"   CA: {cas1[0]}")
    
    # Test 2: Extract CA from URL
    message2 = """🚨 PUMP ALERT 🚨
    
https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk

Get in now!"""
    
    cas2 = parser.extract_contract_addresses(message2)
    print(f"✅ URL CA extraction: {len(cas2)} CAs found")
    if cas2:
        print(f"   CA: {cas2[0]}")
    
    # Test 3: Extract PNL data
    message3 = """🎉 UPDATE 🎉
    
Signal is up 2.3X!
Alerted at: $0.05k
First Hit at: $0.08k
Now at: $0.115k"""
    
    pnl_data = parser.extract_pnl_data(message3)
    print(f"✅ PNL extraction:")
    print(f"   Multiplier: {pnl_data.get('multiplier')}")
    print(f"   Alerted at: {pnl_data.get('alerted_at_price')}")
    print(f"   Current: {pnl_data.get('current_price')}")
    
    # Test 4: Signal detection
    is_signal = parser.is_signal_message(message1)
    is_update = parser.is_update_message(message3)
    print(f"✅ Signal detection: Signal={is_signal}, Update={is_update}")
    
    return True

def test_ca_validation():
    """Test CA validation without database."""
    print("\n🧪 Testing CA Validation...")
    
    # Import base58 for validation
    try:
        import base58
        
        valid_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
        invalid_ca = "invalid_address_123"
        
        # Test valid CA
        try:
            decoded = base58.b58decode(valid_ca)
            is_valid = len(decoded) == 32 and len(valid_ca) == 44
            print(f"✅ Valid CA test: {is_valid}")
        except:
            print("❌ Valid CA test failed")
        
        # Test invalid CA
        try:
            decoded = base58.b58decode(invalid_ca)
            is_invalid = True
        except:
            is_invalid = False
        
        print(f"✅ Invalid CA rejection: {not is_invalid}")
        
    except ImportError:
        print("❌ base58 not available for testing")
    
    return True

def main():
    """Run all tests."""
    print("🚀 CLA v2.0 Bot - Core Functionality Test\n")
    
    try:
        # Test message parsing
        test_message_parsing()
        
        # Test CA validation
        test_ca_validation()
        
        print("\n🎉 All core functionality tests passed!")
        print("\n📋 Next Steps:")
        print("1. Set up your .env file with Telegram API credentials")
        print("2. Run: python main.py")
        print("3. Follow the authentication prompts")
        print("4. Bot will start monitoring for signals")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
