"""Production monitoring script for CLA v2.0 Bot."""

import time
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

def monitor_bot_status():
    """Monitor the bot's production status."""
    print("🚀 CLA v2.0 BOT PRODUCTION MONITORING")
    print("=" * 50)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Check if log files exist
    log_files = {
        'Main Log': 'logs/cla_bot.log',
        'Error Log': 'logs/error.log',
        'Enhanced Log': 'logs/enhanced_bot_startup.log'
    }
    
    print("📁 LOG FILE STATUS:")
    for name, path in log_files.items():
        if os.path.exists(path):
            stat = os.stat(path)
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            print(f"   ✅ {name}: {size:,} bytes, modified {modified.strftime('%H:%M:%S')}")
        else:
            print(f"   ❌ {name}: Not found")
    
    print()
    
    # Check for recent activity
    print("🔍 RECENT ACTIVITY CHECK:")
    
    # Check main log for recent entries
    main_log = 'logs/cla_bot.log'
    if os.path.exists(main_log):
        try:
            with open(main_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    print(f"   📝 Last log entry: {last_line[:100]}...")
                    
                    # Check if there are entries from today
                    today = datetime.now().strftime('%Y-%m-%d')
                    recent_entries = [line for line in lines[-50:] if today in line]
                    print(f"   📊 Recent entries today: {len(recent_entries)}")
                else:
                    print("   ⚠️ Log file is empty")
        except Exception as e:
            print(f"   ❌ Error reading log: {e}")
    
    # Check error log
    error_log = 'logs/error.log'
    if os.path.exists(error_log):
        try:
            with open(error_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    recent_errors = [line for line in lines[-10:] if datetime.now().strftime('%Y-%m-%d') in line]
                    if recent_errors:
                        print(f"   ⚠️ Recent errors today: {len(recent_errors)}")
                        for error in recent_errors[-3:]:  # Show last 3 errors
                            print(f"      {error.strip()}")
                    else:
                        print("   ✅ No recent errors today")
                else:
                    print("   ✅ No errors logged")
        except Exception as e:
            print(f"   ❌ Error reading error log: {e}")
    
    print()
    
    # Check database
    print("💾 DATABASE STATUS:")
    db_path = 'data/cla_bot.db'
    if os.path.exists(db_path):
        stat = os.stat(db_path)
        size = stat.st_size
        modified = datetime.fromtimestamp(stat.st_mtime)
        print(f"   ✅ Database: {size:,} bytes, modified {modified.strftime('%H:%M:%S')}")
    else:
        print("   ❌ Database not found")
    
    print()
    
    # Check session files
    print("🔐 SESSION STATUS:")
    session_files = ['cla_bot.session', 'cla_bot.session-journal']
    for session_file in session_files:
        if os.path.exists(session_file):
            stat = os.stat(session_file)
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            print(f"   ✅ {session_file}: {size:,} bytes, modified {modified.strftime('%H:%M:%S')}")
        else:
            print(f"   ❌ {session_file}: Not found")
    
    print()
    
    # Performance summary
    print("📈 PRODUCTION STATUS SUMMARY:")
    
    # Check if bot appears to be running
    if os.path.exists(main_log):
        try:
            with open(main_log, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Look for startup indicators
                startup_indicators = [
                    'Bot initialized successfully',
                    'Bot started successfully',
                    'Starting message listener',
                    'Message handler setup'
                ]
                
                startup_found = any(indicator in content for indicator in startup_indicators)
                
                if startup_found:
                    print("   ✅ Bot appears to have started successfully")
                else:
                    print("   ⚠️ No clear startup indicators found")
                
                # Look for recent activity
                today = datetime.now().strftime('%Y-%m-%d')
                if today in content:
                    print("   ✅ Bot has activity today")
                else:
                    print("   ⚠️ No activity detected today")
                
                # Look for CA detection
                if 'CA detected' in content:
                    ca_count = content.count('CA detected')
                    print(f"   📊 Total CAs detected: {ca_count}")
                
                # Look for forwarding activity
                if 'sent to BonkBot' in content:
                    forward_count = content.count('sent to BonkBot')
                    print(f"   📤 Total forwards to BonkBot: {forward_count}")
                    
        except Exception as e:
            print(f"   ❌ Error analyzing logs: {e}")
    
    print()
    print("🎯 MONITORING COMPLETE")
    print("=" * 50)

if __name__ == "__main__":
    try:
        monitor_bot_status()
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n💥 Monitoring error: {e}")
        sys.exit(1)
