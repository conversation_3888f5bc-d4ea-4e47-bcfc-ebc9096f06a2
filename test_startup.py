#!/usr/bin/env python3
"""Test script to verify which startup method works best."""

import sys
import os
import asyncio
from datetime import datetime

def test_imports():
    """Test if all required modules can be imported."""
    print("🧪 Testing Module Imports")
    print("=" * 40)
    
    try:
        # Test basic imports
        print("📦 Testing basic imports...")
        from config import config
        print("   ✅ config")
        
        from src.database import DatabaseManager
        print("   ✅ DatabaseManager")
        
        from src.bot import CLABot
        print("   ✅ CLABot")
        
        from src.enhanced_stats_tracker import enhanced_stats
        print("   ✅ enhanced_stats")
        
        # Test enhanced imports
        print("\n📦 Testing enhanced imports...")
        try:
            from src.dependency_container import DependencyContainer
            print("   ✅ DependencyContainer")
            enhanced_available = True
        except Exception as e:
            print(f"   ❌ DependencyContainer: {e}")
            enhanced_available = False
        
        print(f"\n📊 Import Results:")
        print(f"   Basic imports: ✅ WORKING")
        print(f"   Enhanced imports: {'✅ WORKING' if enhanced_available else '❌ ISSUES'}")
        
        return True, enhanced_available
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False, False

async def test_basic_startup():
    """Test basic startup using main.py approach."""
    print("\n🚀 Testing Basic Startup (main.py approach)")
    print("=" * 50)
    
    try:
        # Test database initialization
        print("📊 Testing database initialization...")
        from src.database import DatabaseManager
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        print("   ✅ Database initialized")
        
        # Test bot initialization
        print("🤖 Testing bot initialization...")
        from src.bot import CLABot
        
        bot = CLABot(db_manager)
        # Don't fully initialize to avoid starting Telegram client
        print("   ✅ Bot created successfully")
        
        # Cleanup
        await db_manager.close()
        print("   ✅ Cleanup completed")
        
        print("\n✅ Basic startup test: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Basic startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_enhanced_startup():
    """Test enhanced startup using dependency injection."""
    print("\n🚀 Testing Enhanced Startup (dependency injection)")
    print("=" * 55)
    
    try:
        print("🔗 Testing dependency container...")
        from src.dependency_container import DependencyContainer
        
        container = DependencyContainer()
        await container.initialize()
        print("   ✅ Dependency container initialized")
        
        # Test getting components
        print("📦 Testing component retrieval...")
        db_manager = container.get('db_manager')
        print("   ✅ db_manager retrieved")
        
        enhanced_stats_tracker = container.get('enhanced_stats_tracker')
        print("   ✅ enhanced_stats_tracker retrieved")
        
        # Cleanup
        await container.cleanup()
        print("   ✅ Cleanup completed")
        
        print("\n✅ Enhanced startup test: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def recommend_startup_method(basic_works, enhanced_works):
    """Recommend the best startup method based on test results."""
    print("\n🎯 Startup Method Recommendation")
    print("=" * 40)
    
    if enhanced_works:
        print("✅ RECOMMENDATION: Use start_enhanced_bot.py")
        print("   Reasons:")
        print("   • Enhanced dependency injection working")
        print("   • Better component isolation")
        print("   • Advanced monitoring features")
        print("   • Performance optimizations")
        return "start_enhanced_bot.py"
    
    elif basic_works:
        print("✅ RECOMMENDATION: Use main.py")
        print("   Reasons:")
        print("   • Basic startup working reliably")
        print("   • Simpler architecture")
        print("   • Fewer dependencies")
        print("   • Enhanced features may have issues")
        return "main.py"
    
    else:
        print("❌ RECOMMENDATION: Fix import issues first")
        print("   Issues:")
        print("   • Neither startup method working")
        print("   • Check Python environment")
        print("   • Verify all dependencies installed")
        return None

async def main():
    """Main test function."""
    print("🔧 CLA v2.0 Startup Method Testing")
    print("=" * 50)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python Version: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    
    # Test imports
    basic_imports, enhanced_imports = test_imports()
    
    if not basic_imports:
        print("\n❌ Basic imports failed. Cannot proceed with startup tests.")
        return
    
    # Test basic startup
    basic_startup_works = await test_basic_startup()
    
    # Test enhanced startup (only if enhanced imports work)
    enhanced_startup_works = False
    if enhanced_imports:
        enhanced_startup_works = await test_enhanced_startup()
    else:
        print("\n⚠️ Skipping enhanced startup test (import issues)")
    
    # Recommend startup method
    recommended_script = recommend_startup_method(basic_startup_works, enhanced_startup_works)
    
    print("\n" + "=" * 50)
    print("🎯 FINAL RECOMMENDATION:")
    
    if recommended_script:
        print(f"   Use: python {recommended_script}")
        
        if recommended_script == "main.py":
            print("\n📋 Next Steps:")
            print("   1. Kill any running bot processes")
            print("   2. Run: python main.py")
            print("   3. Monitor logs for successful startup")
            print("   4. Wait for CA processing to verify fixes")
        
        elif recommended_script == "start_enhanced_bot.py":
            print("\n📋 Next Steps:")
            print("   1. Kill any running bot processes")
            print("   2. Run: python start_enhanced_bot.py")
            print("   3. Monitor enhanced startup logs")
            print("   4. Verify all enhanced features are active")
    
    else:
        print("   ❌ Fix environment issues before proceeding")
        print("\n🔧 Troubleshooting:")
        print("   1. Check Python environment")
        print("   2. Verify all dependencies installed")
        print("   3. Check config.py settings")
        print("   4. Review import errors above")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ Test script failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
