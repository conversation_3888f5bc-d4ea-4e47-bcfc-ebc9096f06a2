"""Debug exclusion configuration."""

import sys
sys.path.insert(0, 'src')

def debug_exclusions():
    """Debug exclusion configuration."""
    print("🔍 Debugging Exclusion Configuration...")
    
    try:
        from config import config
        
        print(f"Config object: {config}")
        print(f"Trending config: {config.trending}")
        print(f"Exclude destinations: {getattr(config.trending, 'exclude_destinations', 'NOT FOUND')}")
        
        # Test the method directly
        from src.trending_analyzer import TrendingAnalyzer
        analyzer = TrendingAnalyzer()
        
        # Test exclusion
        result = analyzer.should_forward_to_destination("TestCA", "WINNERS", -1002380594298)
        print(f"Should forward to WINNERS: {result}")
        
        # Check what's actually in exclude_destinations
        excluded = getattr(config.trending, 'exclude_destinations', [])
        print(f"Excluded destinations: {excluded}")
        print(f"Type: {type(excluded)}")
        
        if excluded:
            for dest in excluded:
                print(f"  - '{dest}' (type: {type(dest)})")
        
        # Test comparison
        test_dest = "WINNERS"
        print(f"Testing '{test_dest}' against exclusions:")
        for dest in excluded:
            print(f"  '{test_dest}'.upper() == '{dest}'.upper(): {'WINNERS'.upper() == dest.upper()}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_exclusions()
