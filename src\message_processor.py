"""Message processing component for CLA v2.0 Bot - handles message reception and routing."""

import asyncio
import time
from datetime import datetime
from typing import Optional, Dict, Any
from loguru import logger

from src.group_manager import group_manager

class MessageProcessor:
    """Handles message reception, validation, and routing."""

    def __init__(self, ca_analyzer, forwarding_manager, enhanced_stats_tracker):
        """Initialize message processor with dependencies."""
        self.ca_analyzer = ca_analyzer
        self.forwarding_manager = forwarding_manager
        self.enhanced_stats_tracker = enhanced_stats_tracker
        
        # Message deduplication to prevent race conditions
        self.recent_messages: Dict[int, datetime] = {}
        self.message_dedup_window = 30  # seconds
        
        # Statistics
        self.stats = {
            'messages_processed': 0,
            'duplicate_messages_filtered': 0,
            'empty_messages_skipped': 0,
            'processing_errors': 0
        }
        
        logger.info("Message Processor initialized")
    
    async def process_message(self, event) -> bool:
        """Process incoming Telegram message with performance monitoring."""
        start_time = time.perf_counter()
        
        try:
            message = event.message
            message_text = message.text or ""
            chat_id = event.chat_id
            message_id = message.id
            
            # Get group information
            group_name = group_manager.get_group_name(chat_id)
            group_type = group_manager.get_group_type(chat_id)
            
            # Log message reception
            logger.info(f"🔍 MESSAGE RECEIVED: {group_name} ({group_type}) | ID={message_id}")
            
            # Record message received in enhanced stats
            self.enhanced_stats_tracker.record_message_received(chat_id)
            
            # Check for duplicate messages (race condition protection)
            if await self._is_duplicate_message(message_id):
                self.stats['duplicate_messages_filtered'] += 1
                logger.info(f"🔍 DUPLICATE MESSAGE FILTERED: ID={message_id} | Group={group_name}")
                return False
            
            # Skip empty messages
            if not message_text.strip():
                self.stats['empty_messages_skipped'] += 1
                logger.info(f"🔍 EMPTY MESSAGE SKIPPED: {group_name}")
                return False
            
            self.stats['messages_processed'] += 1
            
            # Log message content based on group type
            if group_type == "HIGH-VOLUME":
                logger.info(f"🔥 HIGH-VOLUME MESSAGE: {group_name} | Text={message_text[:200]}...")
            elif group_type == "LOW-VOLUME":
                logger.info(f"⚡ LOW-VOLUME MESSAGE: {group_name} | Text={message_text[:100]}...")
            else:
                logger.info(f"📨 UNMONITORED MESSAGE: {group_name} | Text={message_text[:100]}...")
            
            # Route to CA analyzer for processing
            await self.ca_analyzer.analyze_message(
                message_text=message_text,
                message_id=message_id,
                chat_id=chat_id,
                group_name=group_name,
                group_type=group_type
            )
            
            # Log periodic statistics
            if self.stats['messages_processed'] % 100 == 0:
                await self._log_periodic_stats()
            
            return True
            
        except Exception as e:
            self.stats['processing_errors'] += 1
            logger.error(f"Error processing message: {e}")
            return False
            
        finally:
            # Performance monitoring
            duration_ms = (time.perf_counter() - start_time) * 1000
            self.enhanced_stats_tracker.record_performance_timing('message_processing', duration_ms)
            
            if duration_ms > 100:  # Log slow message processing
                logger.warning(f"⚠️ SLOW MESSAGE PROCESSING: {duration_ms:.1f}ms for {group_name}")
            elif duration_ms > 50:
                logger.info(f"📊 MESSAGE TIMING: {duration_ms:.1f}ms for {group_name}")
    
    async def _is_duplicate_message(self, message_id: int) -> bool:
        """Check if message is a duplicate within the deduplication window."""
        now = datetime.now()
        
        # Clean up old messages
        cutoff_time = now.timestamp() - self.message_dedup_window
        expired_ids = [
            msg_id for msg_id, timestamp in self.recent_messages.items()
            if timestamp.timestamp() < cutoff_time
        ]
        
        for msg_id in expired_ids:
            del self.recent_messages[msg_id]
        
        # Check if message is duplicate
        if message_id in self.recent_messages:
            return True
        
        # Add message to recent list
        self.recent_messages[message_id] = now
        return False
    
    async def _log_periodic_stats(self):
        """Log periodic message processing statistics."""
        try:
            logger.info("📊 MESSAGE PROCESSOR STATS:")
            logger.info(f"   Messages processed: {self.stats['messages_processed']}")
            logger.info(f"   Duplicates filtered: {self.stats['duplicate_messages_filtered']}")
            logger.info(f"   Empty messages skipped: {self.stats['empty_messages_skipped']}")
            logger.info(f"   Processing errors: {self.stats['processing_errors']}")
            logger.info(f"   Recent messages cache: {len(self.recent_messages)}")
            
        except Exception as e:
            logger.error(f"Error logging periodic stats: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message processor statistics."""
        return {
            **self.stats,
            'recent_messages_cache_size': len(self.recent_messages)
        }
    
    async def cleanup(self):
        """Cleanup message processor resources."""
        try:
            # Clear message cache
            self.recent_messages.clear()
            logger.info("Message processor cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during message processor cleanup: {e}")

class MessageValidator:
    """Validates and filters messages before processing."""
    
    def __init__(self):
        """Initialize message validator."""
        self.stats = {
            'messages_validated': 0,
            'messages_rejected': 0,
            'validation_errors': 0
        }
        
        logger.info("Message Validator initialized")
    
    async def validate_message(self, message_text: str, chat_id: int) -> tuple[bool, str]:
        """Validate message for processing."""
        try:
            self.stats['messages_validated'] += 1
            
            # Check if group is monitored
            if not group_manager.is_monitored_group(chat_id):
                self.stats['messages_rejected'] += 1
                return False, "Group not monitored"
            
            # Check message length
            if len(message_text) > 10000:  # Reasonable limit
                self.stats['messages_rejected'] += 1
                return False, "Message too long"
            
            # Check for suspicious patterns (basic spam detection)
            if message_text.count('http') > 10:  # Too many links
                self.stats['messages_rejected'] += 1
                return False, "Too many links"
            
            return True, "Valid"
            
        except Exception as e:
            self.stats['validation_errors'] += 1
            logger.error(f"Error validating message: {e}")
            return False, f"Validation error: {e}"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message validator statistics."""
        return self.stats.copy()

class MessageRouter:
    """Routes messages to appropriate processors based on content and source."""
    
    def __init__(self):
        """Initialize message router."""
        self.stats = {
            'messages_routed': 0,
            'routing_errors': 0,
            'high_volume_routed': 0,
            'low_volume_routed': 0
        }
        
        logger.info("Message Router initialized")
    
    async def route_message(self, message_text: str, chat_id: int, group_type: str) -> str:
        """Route message to appropriate processor."""
        try:
            self.stats['messages_routed'] += 1
            
            if group_type == "HIGH-VOLUME":
                self.stats['high_volume_routed'] += 1
                return "trending_analysis"
            elif group_type == "LOW-VOLUME":
                self.stats['low_volume_routed'] += 1
                return "direct_forwarding"
            else:
                return "ignore"
                
        except Exception as e:
            self.stats['routing_errors'] += 1
            logger.error(f"Error routing message: {e}")
            return "error"
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message router statistics."""
        return self.stats.copy()
