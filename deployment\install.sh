#!/bin/bash
# CLA v2.0 Telegram Bot - Production Installation Script
# =====================================================
# This script installs the CLA v2.0 bot on a Pebble Host VPS
# Run as root: sudo bash deployment/install.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BOT_USER="cla-bot"
BOT_GROUP="cla-bot"
INSTALL_DIR="/opt/cla-bot"
SERVICE_NAME="cla-bot"
PYTHON_VERSION="3.11"

echo -e "${BLUE}🚀 CLA v2.0 Telegram Bot - Production Installation${NC}"
echo -e "${BLUE}=================================================${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
   exit 1
fi

echo -e "${YELLOW}📋 Installation Configuration:${NC}"
echo -e "   User: ${BOT_USER}"
echo -e "   Group: ${BOT_GROUP}"
echo -e "   Install Directory: ${INSTALL_DIR}"
echo -e "   Service Name: ${SERVICE_NAME}"
echo -e "   Python Version: ${PYTHON_VERSION}"
echo ""

read -p "Continue with installation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Installation cancelled${NC}"
    exit 0
fi

# Update system packages
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt update && apt upgrade -y

# Install required system packages
echo -e "${BLUE}📦 Installing system dependencies...${NC}"
apt install -y \
    python3.11 \
    python3.11-venv \
    python3.11-dev \
    python3-pip \
    git \
    curl \
    wget \
    htop \
    sqlite3 \
    logrotate \
    fail2ban \
    ufw \
    supervisor \
    nginx

# Create bot user and group
echo -e "${BLUE}👤 Creating bot user and group...${NC}"
if ! getent group "$BOT_GROUP" > /dev/null 2>&1; then
    groupadd --system "$BOT_GROUP"
    echo -e "${GREEN}✅ Created group: $BOT_GROUP${NC}"
fi

if ! getent passwd "$BOT_USER" > /dev/null 2>&1; then
    useradd --system --gid "$BOT_GROUP" --home-dir "$INSTALL_DIR" \
            --shell /bin/bash --comment "CLA v2.0 Bot User" "$BOT_USER"
    echo -e "${GREEN}✅ Created user: $BOT_USER${NC}"
fi

# Create installation directory
echo -e "${BLUE}📁 Creating installation directory...${NC}"
mkdir -p "$INSTALL_DIR"
mkdir -p "$INSTALL_DIR/data"
mkdir -p "$INSTALL_DIR/logs"
mkdir -p "$INSTALL_DIR/backups"
mkdir -p "$INSTALL_DIR/config"

# Copy bot files (assuming script is run from bot directory)
echo -e "${BLUE}📋 Copying bot files...${NC}"
cp -r . "$INSTALL_DIR/"
chown -R "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR"

# Set up Python virtual environment
echo -e "${BLUE}🐍 Setting up Python virtual environment...${NC}"
cd "$INSTALL_DIR"
sudo -u "$BOT_USER" python3.11 -m venv .venv
sudo -u "$BOT_USER" .venv/bin/pip install --upgrade pip
sudo -u "$BOT_USER" .venv/bin/pip install -r requirements.txt

# Copy environment file template
echo -e "${BLUE}⚙️ Setting up configuration...${NC}"
if [[ ! -f "$INSTALL_DIR/.env" ]]; then
    cp "$INSTALL_DIR/.env.production" "$INSTALL_DIR/.env"
    echo -e "${YELLOW}⚠️ Please edit $INSTALL_DIR/.env with your actual configuration${NC}"
fi

# Set proper file permissions
echo -e "${BLUE}🔒 Setting file permissions...${NC}"
chmod 755 "$INSTALL_DIR"
chmod 750 "$INSTALL_DIR/data"
chmod 750 "$INSTALL_DIR/logs"
chmod 750 "$INSTALL_DIR/backups"
chmod 640 "$INSTALL_DIR/.env"
chmod +x "$INSTALL_DIR/main.py"

# Install systemd service
echo -e "${BLUE}🔧 Installing systemd service...${NC}"
cp "$INSTALL_DIR/deployment/cla-bot.service" "/etc/systemd/system/"
systemctl daemon-reload
systemctl enable "$SERVICE_NAME"

# Set up log rotation
echo -e "${BLUE}📝 Setting up log rotation...${NC}"
cat > "/etc/logrotate.d/cla-bot" << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $BOT_USER $BOT_GROUP
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

# Configure firewall
echo -e "${BLUE}🔥 Configuring firewall...${NC}"
ufw --force enable
ufw allow ssh
ufw allow 8080/tcp comment "CLA Bot Health Check"
ufw allow 8081/tcp comment "CLA Bot Metrics"

# Set up backup script
echo -e "${BLUE}💾 Setting up backup script...${NC}"
cat > "$INSTALL_DIR/scripts/backup.sh" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Backup Script

BACKUP_DIR="/opt/cla-bot/backups"
DB_PATH="/opt/cla-bot/data/cla_bot.db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/cla_bot_backup_$DATE.tar.gz"

# Create backup
mkdir -p "$BACKUP_DIR"
tar -czf "$BACKUP_FILE" -C /opt/cla-bot data/ logs/ .env

# Keep only last 30 days of backups
find "$BACKUP_DIR" -name "cla_bot_backup_*.tar.gz" -mtime +30 -delete

echo "Backup created: $BACKUP_FILE"
EOF

mkdir -p "$INSTALL_DIR/scripts"
chmod +x "$INSTALL_DIR/scripts/backup.sh"
chown "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/scripts/backup.sh"

# Set up cron job for backups
echo -e "${BLUE}⏰ Setting up backup cron job...${NC}"
(crontab -u "$BOT_USER" -l 2>/dev/null; echo "0 */6 * * * $INSTALL_DIR/scripts/backup.sh") | crontab -u "$BOT_USER" -

# Create monitoring script
echo -e "${BLUE}📊 Setting up monitoring script...${NC}"
cat > "$INSTALL_DIR/scripts/monitor.sh" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Monitoring Script

SERVICE_NAME="cla-bot"
HEALTH_URL="http://127.0.0.1:8080/health"
LOG_FILE="/opt/cla-bot/logs/monitor.log"

# Check if service is running
if ! systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "$(date): Service $SERVICE_NAME is not running, attempting restart" >> "$LOG_FILE"
    systemctl restart "$SERVICE_NAME"
    sleep 10
fi

# Check health endpoint
if ! curl -f "$HEALTH_URL" > /dev/null 2>&1; then
    echo "$(date): Health check failed for $SERVICE_NAME" >> "$LOG_FILE"
    systemctl restart "$SERVICE_NAME"
fi
EOF

chmod +x "$INSTALL_DIR/scripts/monitor.sh"
chown "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/scripts/monitor.sh"

# Set up monitoring cron job
(crontab -u "$BOT_USER" -l 2>/dev/null; echo "*/5 * * * * $INSTALL_DIR/scripts/monitor.sh") | crontab -u "$BOT_USER" -

# Install additional monitoring tools
echo -e "${BLUE}📈 Installing monitoring tools...${NC}"
apt install -y htop iotop nethogs

# Create status script
echo -e "${BLUE}📋 Creating status script...${NC}"
cat > "/usr/local/bin/cla-bot-status" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Status Script

echo "🤖 CLA v2.0 Telegram Bot Status"
echo "================================"

# Service status
echo "📊 Service Status:"
systemctl status cla-bot --no-pager -l

echo ""
echo "🏥 Health Check:"
curl -s http://127.0.0.1:8080/health | python3 -m json.tool 2>/dev/null || echo "Health check unavailable"

echo ""
echo "💾 Disk Usage:"
df -h /opt/cla-bot

echo ""
echo "📝 Recent Logs (last 10 lines):"
tail -n 10 /opt/cla-bot/logs/cla_bot.log 2>/dev/null || echo "No logs available"
EOF

chmod +x "/usr/local/bin/cla-bot-status"

echo -e "${GREEN}✅ Installation completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo -e "1. Edit configuration: ${BLUE}sudo nano $INSTALL_DIR/.env${NC}"
echo -e "2. Start the service: ${BLUE}sudo systemctl start $SERVICE_NAME${NC}"
echo -e "3. Check status: ${BLUE}cla-bot-status${NC}"
echo -e "4. View logs: ${BLUE}sudo journalctl -u $SERVICE_NAME -f${NC}"
echo ""
echo -e "${YELLOW}🔧 Useful Commands:${NC}"
echo -e "   Status: ${BLUE}cla-bot-status${NC}"
echo -e "   Start: ${BLUE}sudo systemctl start $SERVICE_NAME${NC}"
echo -e "   Stop: ${BLUE}sudo systemctl stop $SERVICE_NAME${NC}"
echo -e "   Restart: ${BLUE}sudo systemctl restart $SERVICE_NAME${NC}"
echo -e "   Logs: ${BLUE}sudo journalctl -u $SERVICE_NAME -f${NC}"
echo -e "   Health: ${BLUE}curl http://127.0.0.1:8080/health${NC}"
echo ""
echo -e "${RED}⚠️ IMPORTANT: Edit $INSTALL_DIR/.env with your actual API keys and configuration!${NC}"
