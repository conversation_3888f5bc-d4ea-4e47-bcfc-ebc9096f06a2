#!/usr/bin/env python3
"""
Telegram Authentication Script
"""

import asyncio
import sys
from telethon import TelegramClient
from telethon.errors import SessionPasswordNeededError

async def authenticate():
    """Authenticate with Telegram."""
    
    # Telegram API credentials from .env
    api_id = 29232008
    api_hash = '431f5f680d1975acfdd9050089661772'
    phone = '+254713599320'
    
    print("🔐 Starting Telegram Authentication...")
    print(f"📱 Phone: {phone}")
    print(f"🔑 API ID: {api_id}")
    
    try:
        # Create client with the exact session name the bot expects
        session_name = 'cla_bot_session'
        client = TelegramClient(session_name, api_id, api_hash)
        
        print("🔗 Connecting to Telegram...")
        await client.connect()
        
        if not await client.is_user_authorized():
            print("📲 Sending authentication code...")
            await client.send_code_request(phone)
            
            print("📨 Please check your Telegram app for the verification code")
            code = input("Enter the verification code: ")
            
            try:
                await client.sign_in(phone, code)
            except SessionPasswordNeededError:
                print("🔒 Two-factor authentication enabled")
                password = input("Enter your 2FA password: ")
                await client.sign_in(password=password)
        
        # Test connection
        me = await client.get_me()
        print(f"✅ Successfully authenticated as: {me.first_name}")
        if me.username:
            print(f"   Username: @{me.username}")
        print(f"   User ID: {me.id}")
        
        # Disconnect
        await client.disconnect()
        print("💾 Session saved successfully!")
        print("🎉 Telegram authentication complete!")
        
        return True
        
    except Exception as e:
        print(f"❌ Authentication failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("🔐 TELEGRAM AUTHENTICATION")
    print("=" * 60)
    
    try:
        success = asyncio.run(authenticate())
        
        print("=" * 60)
        if success:
            print("✅ AUTHENTICATION SUCCESSFUL!")
            print("🚀 You can now start the bot")
        else:
            print("❌ AUTHENTICATION FAILED!")
            print("🔧 Please check your credentials and try again")
        print("=" * 60)
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️ Authentication cancelled by user")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
