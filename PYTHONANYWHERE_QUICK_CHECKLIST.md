# CLA v2 PythonAnywhere Deployment - Quick Checklist

**⚠️ PRIVATE REPOSITORY - QUICK DEPLOYMENT REFERENCE**

## 🚀 Quick Deployment Steps

### 1. Pre-Deployment Setup
- [ ] **Python 3.8+** available on PythonAnywhere
- [ ] **Telegram API credentials** ready (API_ID, API_HASH, phone)
- [ ] **GitHub repository** access to https://github.com/bigdrako1/clav2.git

### 2. Upload and Setup (5 minutes)
```bash
# Clone repository
cd ~
git clone https://github.com/bigdrako1/clav2.git
cd clav2

# Create directories
mkdir -p data logs
chmod 755 data logs

# Install dependencies
pip3.9 install --user -r requirements.txt
```

### 3. Environment Configuration (3 minutes)
```bash
# Create .env file
nano .env
```

**Copy this template** (replace YOUR_* values):
```bash
TELEGRAM_API_ID=YOUR_API_ID
TELEGRAM_API_HASH=YOUR_API_HASH
TELEGRAM_PHONE=YOUR_PHONE_NUMBER
TELEGRAM_SESSION_NAME=cla_bot_session

TARGET_GROUP_ID=-1002380594298
TARGET_GROUP_NAME=FREE WHALE SIGNALS

ADDITIONAL_GROUP_IDS=-1002270988204,-1002064145465,-1001763265784,-1002139128702,-1002202241417,-1002333406905,-1002356333152
ADDITIONAL_GROUP_NAMES=Solana Activity Tracker,🌹 MANIFEST,👤 Mark Degens,💎 FINDERTRENDING,🧠 GMGN Featured Signals,🚀 MEME 1000X,BUGSIE
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE

BONKBOT_USER_ID=5312785865
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391

DATABASE_PATH=./data/cla_bot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=3600
CA_CACHE_EXPIRY_HOURS=168
MESSAGE_CACHE_SIZE=10000
DUPLICATE_PREVENTION_HOURS=24
```

### 4. Telegram Authentication (2 minutes)
```bash
# Run authentication
python3.9 authenticate_telegram.py

# Enter verification code from Telegram app
# Enter 2FA password if enabled
```

**Success indicators**:
- ✅ Session file created: `cla_bot_session.session`
- ✅ "Authentication successful!" message

### 5. Pre-Deployment Tests (3 minutes)
```bash
# Test 1: Module imports
python3.9 -c "
import sys; sys.path.append('.')
from src.bot import CLABot
print('✅ Imports successful')
"

# Test 2: Database connectivity
python3.9 -c "
import sys, asyncio; sys.path.append('.')
async def test():
    from src.database import DatabaseManager
    db = DatabaseManager()
    await db.connect()
    print('✅ Database OK')
    await db.close()
asyncio.run(test())
"

# Test 3: Run verification tests
python3.9 test_winners_disabled_and_indentation_fix.py
python3.9 test_optimized_ca_extraction.py
```

**All tests should show**: ✅ PASS

### 6. Deploy Bot (1 minute)
```bash
# Method 1: Screen session (recommended)
screen -S clav2_bot
cd ~/clav2
python3.9 main.py

# Detach: Ctrl+A, then D
# Reattach later: screen -r clav2_bot

# Method 2: Background process
nohup python3.9 main.py > bot_output.log 2>&1 &
```

## ✅ Deployment Verification (2 minutes)

### Check Bot Status
```bash
# Verify bot is running
ps aux | grep main.py

# Check startup logs
tail -20 ~/clav2/logs/cla_bot.log
```

**Expected startup output**:
```
✅ Configuration validation passed
✅ Database connected successfully
✅ BonkBot integration initialized
✅ CLA v2.0 integration initialized
✅ Monaco PNL integration initialized
⏸️ WINNERS integration temporarily disabled
✅ Message listener is now active and ready to receive messages
```

### Monitor Message Reception
```bash
# Watch for message reception
tail -f ~/clav2/logs/cla_bot.log | grep -E "📨|🧠|🚀|📤"
```

**Expected patterns**:
```
🧠 GMGN: ID=12345 | 🚀 NEW TOKEN ALERT...
📤 PARALLEL FORWARDING: BonkBot(1) | CLA v2.0(1) | Monaco PNL(1) | WINNERS(disabled)
```

## 🔧 Quick Troubleshooting

### Bot Won't Start
```bash
# Check for errors
tail -20 ~/clav2/logs/error.log

# Common fixes:
pip3.9 install --user --upgrade -r requirements.txt
rm cla_bot_session.session*
python3.9 authenticate_telegram.py
```

### Database Issues
```bash
# Fix database locks
pkill -f main.py
rm -f data/cla_bot.db-wal data/cla_bot.db-shm
python3.9 main.py
```

### Network Issues
```bash
# Test connectivity
ping telegram.org

# Restart bot (has built-in retry logic)
pkill -f main.py
python3.9 main.py
```

## 📊 Monitoring Commands

### Real-time Monitoring
```bash
# Monitor all activity
tail -f ~/clav2/logs/cla_bot.log

# Monitor CA forwarding only
tail -f ~/clav2/logs/cla_bot.log | grep "📤"

# Monitor errors only
tail -f ~/clav2/logs/error.log
```

### Health Checks
```bash
# Check bot process
ps aux | grep main.py

# Check database
python3.9 -c "
import sqlite3
conn = sqlite3.connect('data/cla_bot.db')
print(f'CAs in DB: {conn.execute(\"SELECT COUNT(*) FROM contract_addresses\").fetchone()[0]}')
conn.close()
"

# Check disk usage
du -h ~/clav2/
```

## 🔄 Maintenance

### Daily Checks
- [ ] Bot process running: `ps aux | grep main.py`
- [ ] Recent activity in logs: `tail -20 ~/clav2/logs/cla_bot.log`
- [ ] No errors: `tail -10 ~/clav2/logs/error.log`

### Weekly Maintenance
```bash
# Clean old logs
find ~/clav2/logs/ -name "*.log" -mtime +7 -delete

# Vacuum database
python3.9 -c "
import sqlite3
conn = sqlite3.connect('data/cla_bot.db')
conn.execute('VACUUM')
conn.close()
"
```

### Updates
```bash
# Stop bot
pkill -f main.py

# Update code
cd ~/clav2
git pull origin master
pip3.9 install --user -r requirements.txt

# Restart
python3.9 main.py
```

## 📞 Emergency Recovery

### If Bot Stops Responding
```bash
# Emergency restart
pkill -f main.py
cd ~/clav2
rm -f data/cla_bot.db-wal data/cla_bot.db-shm
screen -S clav2_bot
python3.9 main.py
# Ctrl+A, D to detach
```

### If Authentication Fails
```bash
# Re-authenticate
rm cla_bot_session.session*
python3.9 authenticate_telegram.py
```

## 🎯 Success Indicators

### Bot is Working Correctly When:
- ✅ Process shows in `ps aux | grep main.py`
- ✅ Logs show "Message listener is now active"
- ✅ Regular message reception: `🧠 GMGN:` or `📨` entries
- ✅ CA forwarding: `📤 PARALLEL FORWARDING:` entries
- ✅ 3 destinations active: BonkBot, CLA v2.0, Monaco PNL
- ✅ WINNERS shows as "disabled" in logs

### Performance Indicators:
- **Message Processing**: 10-50 messages per hour
- **CA Detection**: 5-20 CAs per hour  
- **Forwarding Success**: 95%+ success rate
- **Memory Usage**: <100MB
- **Log File Growth**: <10MB per day

## 📋 Deployment Summary

**Total Deployment Time**: ~15 minutes  
**Active Destinations**: 3 (BonkBot, CLA v2.0, Monaco PNL)  
**Disabled Destinations**: 1 (WINNERS - temporarily)  
**Monitoring Groups**: 8 Telegram groups  
**Database**: SQLite with automatic optimization  
**Logging**: Comprehensive with rotation  

**Status**: ✅ Production Ready
