"""CA Rescue Tracker for high-volume filtered CAs that appear in low-volume groups."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from loguru import logger
from src.group_manager import group_manager
from src.bounded_cache import BoundedLRUCache, BoundedSetCache

@dataclass
class RescueEligibleCA:
    """CA that was filtered from high-volume group and is eligible for rescue."""
    ca: str
    original_group_id: int
    original_group_name: str
    filtered_timestamp: datetime
    mention_count: int
    message_text: str
    rescued: bool = False
    rescue_timestamp: Optional[datetime] = None
    rescue_group_id: Optional[int] = None
    rescue_group_name: Optional[str] = None

class CARescueTracker:
    """Tracks CAs filtered from high-volume groups for potential rescue."""

    def __init__(self):
        # CONSOLIDATION FIX: Use centralized group manager as single source of truth
        # Remove hardcoded group mappings - use group_manager instead

        # BOUNDED CACHE FIX: Replace unbounded caches with bounded versions
        # Rescue-eligible CAs cache (max 1000 entries, 24-hour retention)
        self.rescue_eligible_cache = BoundedLRUCache(
            max_size=1000,
            name="RescueEligible"
        )

        # Already rescued CAs (max 5000 entries to prevent duplicates)
        self.rescued_cas_cache = BoundedSetCache(
            max_size=5000,
            name="RescuedCAs"
        )

        # Keep legacy dict for backward compatibility during transition
        self.rescue_eligible: Dict[str, RescueEligibleCA] = {}

        # RACE CONDITION FIX: Add locks for atomic operations
        self.rescue_lock = asyncio.Lock()  # Global rescue operation lock
        self.ca_locks: Dict[str, asyncio.Lock] = {}  # Per-CA locks
        self.cache_lock = asyncio.Lock()  # Cache modification lock

        # Statistics
        self.stats = {
            'total_filtered': 0,
            'rescue_eligible_added': 0,
            'rescue_attempts': 0,
            'successful_rescues': 0,
            'duplicate_rescue_prevented': 0,
            'expired_entries_cleaned': 0,
            'race_conditions_prevented': 0
        }

        logger.info("CA Rescue Tracker initialized with race condition protection")
        logger.info(f"High-volume groups: {len(group_manager.get_high_volume_groups())}")
        logger.info(f"Low-volume groups: {len(group_manager.get_low_volume_groups())}")

        # Log group details
        for group_id in group_manager.get_high_volume_groups():
            logger.info(f"  🔥 HIGH-VOLUME: {group_manager.get_group_name(group_id)} ({group_id})")
        for group_id in group_manager.get_low_volume_groups():
            logger.info(f"  ⚡ LOW-VOLUME: {group_manager.get_group_name(group_id)} ({group_id})")

    async def _get_ca_lock(self, ca: str) -> asyncio.Lock:
        """Get or create a lock for a specific CA to prevent race conditions."""
        async with self.cache_lock:
            if ca not in self.ca_locks:
                self.ca_locks[ca] = asyncio.Lock()

                # Cleanup old locks if too many
                if len(self.ca_locks) > 1000:
                    # Remove oldest 500 locks
                    old_keys = list(self.ca_locks.keys())[:500]
                    for key in old_keys:
                        if key in self.ca_locks:
                            del self.ca_locks[key]
                    logger.debug(f"Cleaned up {len(old_keys)} CA locks")

            return self.ca_locks[ca]

    async def add_filtered_ca(self, ca: str, group_id: int, group_name: str,
                             mention_count: int, message_text: str):
        """Add a CA that was filtered from a high-volume group."""

        # Only track CAs filtered from high-volume groups
        if not group_manager.is_high_volume_group(group_id):
            return
        
        # Don't add if already rescued (check bounded cache)
        if await self.rescued_cas_cache.contains(ca):
            logger.debug(f"CA {ca} already rescued, not adding to rescue-eligible cache")
            return

        rescue_entry = RescueEligibleCA(
            ca=ca,
            original_group_id=group_id,
            original_group_name=group_name,
            filtered_timestamp=datetime.now(),
            mention_count=mention_count,
            message_text=message_text[:200]  # Truncate for storage
        )

        # Add to both bounded cache and legacy dict
        await self.rescue_eligible_cache.put(ca, rescue_entry)
        self.rescue_eligible[ca] = rescue_entry
        self.stats['rescue_eligible_added'] += 1

        logger.info(f"🛡️ RESCUE ELIGIBLE: {ca} from {group_name} | Count: {mention_count}")

        # PHASE 1: Enhanced logging for multi-mention rescue eligibility
        if mention_count > 1:
            logger.info(f"🐌 MULTI-MENTION RESCUE ELIGIBLE: {ca} | {mention_count} mentions | "
                       f"Group: {group_name} | Potential slow cook pattern")

        cache_size = await self.rescue_eligible_cache.size()
        logger.debug(f"Rescue cache size: {cache_size}")
    
    async def check_for_rescue(self, ca: str, group_id: int, group_name: str,
                              message_text: str, ca_detector=None) -> bool:
        """Check if a CA from low-volume group should be rescued with atomic operations."""

        # Only process CAs from low-volume groups
        if not group_manager.is_low_volume_group(group_id):
            return False

        # ATOMIC OPERATION: Get CA-specific lock to prevent race conditions
        ca_lock = await self._get_ca_lock(ca)

        async with ca_lock:
            logger.debug(f"🔒 RESCUE LOCK ACQUIRED: {ca} from {group_name}")

            # Check if this CA is rescue-eligible (check bounded cache first)
            rescue_entry = await self.rescue_eligible_cache.get(ca)
            if not rescue_entry and ca not in self.rescue_eligible:
                logger.debug(f"🚫 RESCUE SKIPPED: {ca} not in rescue-eligible cache")
                return False

            # Use cache entry if available, otherwise fallback to legacy dict
            if not rescue_entry:
                rescue_entry = self.rescue_eligible.get(ca)

            # Check if already rescued (check bounded cache)
            if await self.rescued_cas_cache.contains(ca):
                self.stats['duplicate_rescue_prevented'] += 1
                logger.info(f"🚫 RESCUE PREVENTED: {ca} already rescued previously")
                return False

            # CRITICAL: Check if CA has already been forwarded via global duplicate prevention
            if ca_detector and await self._check_already_forwarded(ca, ca_detector):
                self.stats['duplicate_rescue_prevented'] += 1
                logger.info(f"🚫 RESCUE PREVENTED: {ca} already forwarded previously")

                # SAFE REMOVAL: Remove from rescue-eligible since it's already been processed
                async with self.cache_lock:
                    if ca in self.rescue_eligible:
                        del self.rescue_eligible[ca]
                return False

            # Check if rescue entry has expired (24 hours)
            if self._is_expired(rescue_entry):
                logger.info(f"⏰ RESCUE EXPIRED: {ca} from {rescue_entry.original_group_name}")
                async with self.cache_lock:
                    # Remove from both caches
                    await self.rescue_eligible_cache.remove(ca)
                    if ca in self.rescue_eligible:
                        del self.rescue_eligible[ca]
                return False

            # Execute rescue atomically
            result = await self._execute_rescue(rescue_entry, group_id, group_name, message_text)

            if result:
                logger.info(f"🔓 RESCUE COMPLETED: {ca} lock released")
            else:
                self.stats['race_conditions_prevented'] += 1
                logger.warning(f"🔓 RESCUE FAILED: {ca} lock released")

            return result
    
    async def _execute_rescue(self, rescue_entry: RescueEligibleCA,
                            rescue_group_id: int, rescue_group_name: str,
                            message_text: str) -> bool:
        """Execute the rescue operation for a CA."""

        try:
            ca = rescue_entry.ca
            now = datetime.now()

            # FINAL VALIDATION: Double-check that this CA hasn't been processed
            # This is a critical safety check before forwarding
            logger.info(f"🔍 RESCUE VALIDATION: Final check for CA {ca}")

            # Calculate time since original filtering
            time_since_filtered = (now - rescue_entry.filtered_timestamp).total_seconds()

            # Validate rescue is still appropriate
            if time_since_filtered > 86400:  # 24 hours
                logger.warning(f"🚫 RESCUE ABORTED: CA {ca} too old ({time_since_filtered/3600:.1f} hours)")
                return False

            # Update rescue entry
            rescue_entry.rescued = True
            rescue_entry.rescue_timestamp = now
            rescue_entry.rescue_group_id = rescue_group_id
            rescue_entry.rescue_group_name = rescue_group_name

            # Add to rescued cache to prevent future rescues
            await self.rescued_cas_cache.add(ca)

            # Update statistics
            self.stats['rescue_attempts'] += 1
            self.stats['successful_rescues'] += 1

            # Log rescue event with detailed information
            logger.info(f"🚀 CA RESCUE EXECUTED: {ca}")
            logger.info(f"   ⏰ Original: {rescue_entry.original_group_name} at {rescue_entry.filtered_timestamp.strftime('%H:%M:%S')}")
            logger.info(f"   🎯 Rescued by: {rescue_group_name} at {now.strftime('%H:%M:%S')}")
            logger.info(f"   ⏱️ Time delay: {time_since_filtered/60:.1f} minutes")
            logger.info(f"   📊 Original mentions: {rescue_entry.mention_count}")
            logger.info(f"   🛡️ Rescue mechanism: High-volume filtered → Low-volume override")

            # PHASE 1: Enhanced logging for multi-mention rescues
            if rescue_entry.mention_count > 1:
                logger.warning(f"🐌 SLOW COOK RESCUE: Multi-mention CA rescued | "
                             f"{rescue_entry.mention_count} mentions | "
                             f"Delay: {time_since_filtered/60:.1f}min | "
                             f"Potential missed trend pattern!")

            return True

        except Exception as e:
            logger.error(f"Error executing rescue for {rescue_entry.ca}: {e}")
            self.stats['rescue_attempts'] += 1  # Count failed attempts
            return False
    
    async def _check_already_forwarded(self, ca: str, ca_detector) -> bool:
        """Check if CA has already been forwarded via global duplicate prevention."""
        try:
            # Check if CA is in the processed cache (indicates it was already processed/forwarded)
            if hasattr(ca_detector, 'processed_cache'):
                if ca in ca_detector.processed_cache:
                    logger.debug(f"🚫 RESCUE BLOCKED: CA {ca} found in processed cache")
                    return True

            # Check database for CA processing history
            if hasattr(ca_detector, 'is_ca_in_cache'):
                if await ca_detector.is_ca_in_cache(ca):
                    logger.debug(f"🚫 RESCUE BLOCKED: CA {ca} found in database cache")
                    return True

            # Additional check: query database directly for CA processing
            if hasattr(ca_detector, 'db_manager'):
                try:
                    is_processed = await ca_detector.db_manager.is_ca_processed(ca)
                    if is_processed:
                        logger.debug(f"🚫 RESCUE BLOCKED: CA {ca} marked as processed in database")
                        return True
                except Exception as db_e:
                    logger.warning(f"Database check failed for CA {ca}: {db_e}")
                    # Continue with other checks

            # If we reach here, CA appears to not have been forwarded yet
            logger.debug(f"✅ RESCUE ALLOWED: CA {ca} not found in any forwarding caches")
            return False

        except Exception as e:
            logger.error(f"Error checking if CA {ca} was already forwarded: {e}")
            # If we can't determine, err on the side of caution and don't rescue
            logger.warning(f"🚫 RESCUE BLOCKED: Unable to verify forwarding status for {ca}")
            return True

    def _is_expired(self, rescue_entry: RescueEligibleCA) -> bool:
        """Check if a rescue entry has expired (24 hours)."""
        expiry_time = rescue_entry.filtered_timestamp + timedelta(hours=24)
        return datetime.now() > expiry_time
    
    async def cleanup_expired_entries(self):
        """Clean up expired rescue-eligible entries with safe iteration."""
        try:
            async with self.cache_lock:
                # SAFE ITERATION: Collect expired CAs first, then modify
                expired_cas = []

                for ca, entry in list(self.rescue_eligible.items()):
                    if self._is_expired(entry):
                        expired_cas.append(ca)

                # Safe removal after iteration
                for ca in expired_cas:
                    if ca in self.rescue_eligible:
                        del self.rescue_eligible[ca]
                        self.stats['expired_entries_cleaned'] += 1

                if expired_cas:
                    logger.info(f"🧹 RESCUE CLEANUP: Removed {len(expired_cas)} expired entries")
                    logger.debug(f"Rescue cache size after cleanup: {len(self.rescue_eligible)}")

        except Exception as e:
            logger.error(f"Error during rescue cache cleanup: {e}")
    
    def get_rescue_eligible_count(self) -> int:
        """Get count of currently rescue-eligible CAs."""
        return len(self.rescue_eligible)
    
    def get_rescued_count(self) -> int:
        """Get count of successfully rescued CAs."""
        return len(self.rescued_cas)
    
    def get_rescue_statistics(self) -> Dict:
        """Get comprehensive rescue statistics."""
        return {
            **self.stats,
            'rescue_eligible_count': self.get_rescue_eligible_count(),
            'rescued_count': self.get_rescued_count(),
            'rescue_success_rate': (self.stats['successful_rescues'] / max(1, self.stats['rescue_attempts'])) * 100
        }
    
    def get_recent_rescues(self, hours: int = 24) -> List[RescueEligibleCA]:
        """Get list of recent rescues within specified hours."""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        recent_rescues = []
        for entry in self.rescue_eligible.values():
            if entry.rescued and entry.rescue_timestamp and entry.rescue_timestamp > cutoff:
                recent_rescues.append(entry)
        
        return sorted(recent_rescues, key=lambda x: x.rescue_timestamp, reverse=True)
    
    def get_pending_rescues(self) -> List[RescueEligibleCA]:
        """Get list of CAs currently eligible for rescue."""
        pending = []
        
        for entry in self.rescue_eligible.values():
            if not entry.rescued and not self._is_expired(entry):
                pending.append(entry)
        
        return sorted(pending, key=lambda x: x.filtered_timestamp, reverse=True)
    
    def generate_rescue_report(self) -> str:
        """Generate comprehensive rescue mechanism report."""
        stats = self.get_rescue_statistics()
        recent_rescues = self.get_recent_rescues(24)
        pending_rescues = self.get_pending_rescues()
        
        report = []
        report.append("🛡️ CA Rescue Mechanism Report")
        report.append("=" * 50)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Statistics
        report.append("📊 Rescue Statistics:")
        report.append(f"   Total CAs filtered from high-volume: {stats['total_filtered']}")
        report.append(f"   Added to rescue-eligible cache: {stats['rescue_eligible_added']}")
        report.append(f"   Rescue attempts: {stats['rescue_attempts']}")
        report.append(f"   Successful rescues: {stats['successful_rescues']}")
        report.append(f"   Duplicate rescues prevented: {stats['duplicate_rescue_prevented']}")
        report.append(f"   Success rate: {stats['rescue_success_rate']:.1f}%")
        report.append("")
        
        # Current status
        report.append("📋 Current Status:")
        report.append(f"   Rescue-eligible CAs: {stats['rescue_eligible_count']}")
        report.append(f"   Total rescued CAs: {stats['rescued_count']}")
        report.append(f"   Expired entries cleaned: {stats['expired_entries_cleaned']}")
        report.append("")
        
        # Recent rescues
        if recent_rescues:
            report.append(f"🚀 Recent Rescues (Last 24 hours): {len(recent_rescues)}")
            for rescue in recent_rescues[:5]:  # Show top 5
                time_delay = (rescue.rescue_timestamp - rescue.filtered_timestamp).total_seconds() / 60
                report.append(f"   {rescue.ca[:20]}...")
                report.append(f"      Original: {rescue.original_group_name} | Rescued by: {rescue.rescue_group_name}")
                report.append(f"      Delay: {time_delay:.1f} minutes | Original mentions: {rescue.mention_count}")
        else:
            report.append("🚀 Recent Rescues: None in last 24 hours")
        
        report.append("")
        
        # Pending rescues
        if pending_rescues:
            report.append(f"⏳ Pending Rescues: {len(pending_rescues)}")
            for pending in pending_rescues[:5]:  # Show top 5
                time_waiting = (datetime.now() - pending.filtered_timestamp).total_seconds() / 60
                report.append(f"   {pending.ca[:20]}...")
                report.append(f"      From: {pending.original_group_name} | Waiting: {time_waiting:.1f} minutes")
                report.append(f"      Original mentions: {pending.mention_count}")
        else:
            report.append("⏳ Pending Rescues: None currently eligible")
        
        return "\n".join(report)

# Global instance
ca_rescue_tracker = CARescueTracker()
