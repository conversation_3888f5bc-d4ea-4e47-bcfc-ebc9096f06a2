#!/usr/bin/env python3
"""
Slow Cook Pattern Monitoring Tool - Phase 1 Implementation
Real-time monitoring and analysis of slow cook CA patterns
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def monitor_slow_cook_patterns():
    """Monitor and analyze slow cook patterns in real-time."""
    print("🐌 SLOW COOK PATTERN MONITOR - Phase 1")
    print("=" * 60)
    
    try:
        # Import after path setup
        from src.trending_analyzer import TrendingAnalyzer
        from src.slow_cook_analytics import SlowCookAnalytics
        from config import config
        
        # Initialize components (read-only monitoring)
        trending_analyzer = TrendingAnalyzer()
        analytics = SlowCookAnalytics()
        
        print(f"✅ Monitoring initialized")
        print(f"📊 Configuration:")
        print(f"   - Time window: {config.trending.time_window_minutes} minutes")
        print(f"   - Min mentions: {config.trending.min_mentions}")
        print(f"   - High-volume groups: {len(config.trending.high_volume_groups)}")
        print(f"   - Low-volume groups: {len(config.trending.low_volume_groups)}")
        print()
        
        # Monitoring loop
        last_report_time = datetime.now()
        report_interval = timedelta(minutes=15)  # Report every 15 minutes
        
        while True:
            try:
                current_time = datetime.now()
                
                # Generate periodic reports
                if current_time - last_report_time >= report_interval:
                    await generate_pattern_report(trending_analyzer, analytics)
                    last_report_time = current_time
                
                # Check for immediate alerts
                await check_slow_cook_alerts(trending_analyzer)
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
                
    except Exception as e:
        print(f"❌ Failed to initialize monitoring: {e}")
        return False

async def generate_pattern_report(trending_analyzer, analytics):
    """Generate comprehensive pattern analysis report."""
    try:
        print(f"\n📊 SLOW COOK PATTERN REPORT - {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 50)
        
        # Get trending analyzer statistics
        trending_stats = trending_analyzer.get_trending_stats()
        slow_cook_stats = trending_analyzer.get_slow_cook_stats()
        
        # Current activity summary
        print(f"🔥 Current Activity:")
        print(f"   Active CAs tracking: {trending_stats.get('active_cas_tracking', 0)}")
        print(f"   Trending CAs: {trending_stats.get('trending_cas_count', 0)}")
        print(f"   Mentions/minute: {trending_stats.get('mentions_per_minute', 0):.1f}")
        
        # Slow cook statistics
        if slow_cook_stats and slow_cook_stats.get('slow_cook_stats'):
            stats = slow_cook_stats['slow_cook_stats']
            print(f"\n🐌 Slow Cook Analysis:")
            print(f"   Multi-mention filtered: {stats.get('multi_mention_filtered', 0)}")
            print(f"   Slow cook candidates: {stats.get('slow_cook_candidates', 0)}")
            print(f"   Cross-group patterns: {stats.get('cross_group_patterns', 0)}")
            print(f"   Active 24h patterns: {slow_cook_stats.get('active_patterns', 0)}")
            
            # Mention count distribution
            mention_dist = stats.get('filtered_by_mention_count', {})
            if mention_dist:
                print(f"   Mention distribution: {dict(sorted(mention_dist.items()))}")
            
            # Time span distribution
            time_dist = stats.get('time_span_distribution', {})
            if time_dist:
                print(f"   Time span (hours): {dict(sorted(time_dist.items()))}")
        
        # Pattern details
        pattern_details = slow_cook_stats.get('pattern_details', [])
        if pattern_details:
            print(f"\n🏆 Top Slow Cook Patterns:")
            for i, pattern in enumerate(pattern_details[:5], 1):
                ca_short = pattern['ca'][:8] + "..." if len(pattern['ca']) > 8 else pattern['ca']
                print(f"   {i}. {ca_short} | "
                      f"{pattern['total_mentions']} mentions | "
                      f"{pattern['time_span_hours']:.1f}h | "
                      f"{pattern['groups_count']} groups")
        
        # Analytics insights
        insights = analytics.get_pattern_insights()
        if insights and 'total_patterns' in insights:
            print(f"\n🔬 Pattern Analysis:")
            print(f"   Total patterns: {insights['total_patterns']}")
            print(f"   Slow cook patterns: {insights['slow_cook_patterns']}")
            print(f"   Rescue rate: {insights['rescue_rate']:.1f}%")
            
            pattern_types = insights.get('pattern_types', {})
            print(f"   Pattern types: {pattern_types}")
        
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ Error generating pattern report: {e}")

async def check_slow_cook_alerts(trending_analyzer):
    """Check for immediate slow cook alerts."""
    try:
        slow_cook_stats = trending_analyzer.get_slow_cook_stats()
        
        if slow_cook_stats and slow_cook_stats.get('slow_cook_stats'):
            stats = slow_cook_stats['slow_cook_stats']
            candidates = stats.get('slow_cook_candidates', 0)
            
            # Alert on high slow cook candidate activity
            if candidates > 5:  # More than 5 candidates in recent period
                print(f"🚨 HIGH SLOW COOK ACTIVITY: {candidates} candidates detected!")
                
                # Show recent patterns
                pattern_details = slow_cook_stats.get('pattern_details', [])
                if pattern_details:
                    print(f"   Recent patterns:")
                    for pattern in pattern_details[:3]:
                        ca_short = pattern['ca'][:8] + "..."
                        print(f"   - {ca_short}: {pattern['total_mentions']} mentions, "
                              f"{pattern['time_span_hours']:.1f}h")
        
    except Exception as e:
        print(f"❌ Error checking slow cook alerts: {e}")

def analyze_historical_data():
    """Analyze historical slow cook data if available."""
    try:
        analytics_file = "./data/slow_cook_analytics.json"
        
        if os.path.exists(analytics_file):
            print(f"📁 Loading historical data from {analytics_file}")
            
            with open(analytics_file, 'r') as f:
                data = json.load(f)
            
            pattern_db = data.get('pattern_database', {})
            daily_summaries = data.get('daily_summaries', {})
            
            print(f"📊 Historical Analysis:")
            print(f"   Total patterns recorded: {len(pattern_db)}")
            print(f"   Daily summaries: {len(daily_summaries)}")
            
            # Analyze pattern types
            if pattern_db:
                pattern_types = {}
                for pattern_data in pattern_db.values():
                    ptype = pattern_data.get('pattern_type', 'unknown')
                    pattern_types[ptype] = pattern_types.get(ptype, 0) + 1
                
                print(f"   Pattern type distribution: {pattern_types}")
            
            # Show recent daily summaries
            if daily_summaries:
                recent_dates = sorted(daily_summaries.keys())[-3:]  # Last 3 days
                print(f"   Recent daily summaries:")
                for date in recent_dates:
                    summary = daily_summaries[date]
                    print(f"     {date}: {summary.get('slow_cook_candidates', 0)} candidates, "
                          f"{summary.get('slow_cook_rescue_rate', 0):.1f}% rescue rate")
        else:
            print(f"📁 No historical data found at {analytics_file}")
            
    except Exception as e:
        print(f"❌ Error analyzing historical data: {e}")

def display_configuration_analysis():
    """Display analysis of current configuration for slow cook detection."""
    try:
        from config import config
        
        print(f"🔧 CONFIGURATION ANALYSIS:")
        print(f"   Time window: {config.trending.time_window_minutes} minutes")
        print(f"   Min mentions: {config.trending.min_mentions}")
        print(f"   Anti-pump min spread: {config.trending.min_time_spread_seconds} seconds")
        print(f"   Max velocity: {config.trending.max_velocity_mentions_per_minute}/min")
        
        # Calculate theoretical slow cook scenarios
        window_hours = config.trending.time_window_minutes / 60
        print(f"\n📐 Theoretical Analysis:")
        print(f"   CAs with mentions every {config.trending.time_window_minutes + 1} minutes: FILTERED")
        print(f"   CAs with {config.trending.min_mentions - 1} mentions in {window_hours}h: FILTERED")
        print(f"   Slow cook threshold: >{config.trending.min_mentions} mentions in <{window_hours}h")
        
        # Recommendations
        print(f"\n💡 Slow Cook Detection Gaps:")
        print(f"   - CAs with 10+ mentions over 2+ hours: MISSED")
        print(f"   - CAs with steady 1 mention/15min pattern: MISSED")
        print(f"   - Cross-group gradual building: PARTIALLY MISSED")
        
    except Exception as e:
        print(f"❌ Error in configuration analysis: {e}")

async def main():
    """Main monitoring function."""
    print("🚀 Starting Slow Cook Pattern Monitor...")
    
    # Display configuration analysis
    display_configuration_analysis()
    print()
    
    # Analyze historical data
    analyze_historical_data()
    print()
    
    # Start real-time monitoring
    await monitor_slow_cook_patterns()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
