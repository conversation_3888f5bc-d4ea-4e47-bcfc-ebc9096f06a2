"""Continuous 30-minute production monitoring for CLA v2.0 Bot."""

import time
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

def get_log_stats():
    """Get current log statistics."""
    stats = {}
    
    # Main log stats
    main_log = 'logs/cla_bot.log'
    if os.path.exists(main_log):
        with open(main_log, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            
            # Count today's entries
            today = datetime.now().strftime('%Y-%m-%d')
            today_lines = [line for line in lines if today in line and line.strip()]
            
            stats['total_entries'] = len(today_lines)
            stats['ca_detected'] = content.count('CA detected')
            stats['forwards_bonkbot'] = content.count('sent to BonkBot')
            stats['forwards_cla'] = content.count('sent to CLA v2.0')
            stats['forwards_monaco'] = content.count('sent to Monaco PNL')
            stats['message_processing'] = content.count('MESSAGE TIMING')
            
            # Get last entry
            if today_lines:
                stats['last_entry'] = today_lines[-1].strip()
            else:
                stats['last_entry'] = 'No entries today'
    
    # Database stats
    db_path = 'data/cla_bot.db'
    if os.path.exists(db_path):
        stat = os.stat(db_path)
        stats['db_size'] = stat.st_size
        stats['db_modified'] = datetime.fromtimestamp(stat.st_mtime)
    
    return stats

def monitor_continuous():
    """Monitor bot for 30 minutes continuously."""
    print("🔄 CONTINUOUS 30-MINUTE PRODUCTION MONITORING")
    print("=" * 60)
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=30)
    
    print(f"📅 Start: {start_time.strftime('%H:%M:%S')}")
    print(f"📅 End:   {end_time.strftime('%H:%M:%S')}")
    print()
    
    # Get initial stats
    initial_stats = get_log_stats()
    print("📊 INITIAL STATS:")
    print(f"   📝 Total entries today: {initial_stats.get('total_entries', 0)}")
    print(f"   🎯 CAs detected: {initial_stats.get('ca_detected', 0)}")
    print(f"   📤 BonkBot forwards: {initial_stats.get('forwards_bonkbot', 0)}")
    print(f"   📤 CLA v2.0 forwards: {initial_stats.get('forwards_cla', 0)}")
    print(f"   📤 Monaco PNL forwards: {initial_stats.get('forwards_monaco', 0)}")
    print(f"   ⏱️ Message processing events: {initial_stats.get('message_processing', 0)}")
    print(f"   💾 Database size: {initial_stats.get('db_size', 0):,} bytes")
    print()
    
    # Monitor every 2 minutes
    check_interval = 120  # 2 minutes
    check_count = 0
    
    while datetime.now() < end_time:
        time.sleep(check_interval)
        check_count += 1
        current_time = datetime.now()
        remaining = end_time - current_time
        
        print(f"🔍 CHECK #{check_count} - {current_time.strftime('%H:%M:%S')} (Remaining: {remaining.seconds//60}m {remaining.seconds%60}s)")
        
        # Get current stats
        current_stats = get_log_stats()
        
        # Calculate deltas
        entries_delta = current_stats.get('total_entries', 0) - initial_stats.get('total_entries', 0)
        ca_delta = current_stats.get('ca_detected', 0) - initial_stats.get('ca_detected', 0)
        bonkbot_delta = current_stats.get('forwards_bonkbot', 0) - initial_stats.get('forwards_bonkbot', 0)
        cla_delta = current_stats.get('forwards_cla', 0) - initial_stats.get('forwards_cla', 0)
        monaco_delta = current_stats.get('forwards_monaco', 0) - initial_stats.get('forwards_monaco', 0)
        processing_delta = current_stats.get('message_processing', 0) - initial_stats.get('message_processing', 0)
        
        db_size_delta = current_stats.get('db_size', 0) - initial_stats.get('db_size', 0)
        
        print(f"   📈 New entries: +{entries_delta}")
        print(f"   🎯 New CAs: +{ca_delta}")
        print(f"   📤 New BonkBot forwards: +{bonkbot_delta}")
        print(f"   📤 New CLA forwards: +{cla_delta}")
        print(f"   📤 New Monaco forwards: +{monaco_delta}")
        print(f"   ⏱️ New processing events: +{processing_delta}")
        print(f"   💾 Database growth: +{db_size_delta:,} bytes")
        
        # Show last entry
        if current_stats.get('last_entry'):
            last_entry = current_stats['last_entry']
            if len(last_entry) > 80:
                last_entry = last_entry[:80] + "..."
            print(f"   📝 Last: {last_entry}")
        
        print()
        
        # Check if bot is still active (new entries in last 5 minutes)
        if entries_delta == 0 and check_count > 1:
            print("   ⚠️ WARNING: No new entries detected - bot may be idle or stuck")
        else:
            print("   ✅ Bot is actively processing messages")
        
        print("-" * 60)
    
    # Final summary
    print("\n🎯 30-MINUTE MONITORING COMPLETE")
    print("=" * 60)
    
    final_stats = get_log_stats()
    total_entries = final_stats.get('total_entries', 0) - initial_stats.get('total_entries', 0)
    total_cas = final_stats.get('ca_detected', 0) - initial_stats.get('ca_detected', 0)
    total_bonkbot = final_stats.get('forwards_bonkbot', 0) - initial_stats.get('forwards_bonkbot', 0)
    total_cla = final_stats.get('forwards_cla', 0) - initial_stats.get('forwards_cla', 0)
    total_monaco = final_stats.get('forwards_monaco', 0) - initial_stats.get('forwards_monaco', 0)
    total_processing = final_stats.get('message_processing', 0) - initial_stats.get('message_processing', 0)
    total_db_growth = final_stats.get('db_size', 0) - initial_stats.get('db_size', 0)
    
    print(f"📊 PERFORMANCE SUMMARY (30 minutes):")
    print(f"   📝 Total new entries: {total_entries}")
    print(f"   🎯 Total new CAs detected: {total_cas}")
    print(f"   📤 Total BonkBot forwards: {total_bonkbot}")
    print(f"   📤 Total CLA v2.0 forwards: {total_cla}")
    print(f"   📤 Total Monaco PNL forwards: {total_monaco}")
    print(f"   ⏱️ Total processing events: {total_processing}")
    print(f"   💾 Total database growth: {total_db_growth:,} bytes")
    
    # Calculate rates
    if total_entries > 0:
        ca_rate = (total_cas / total_entries) * 100 if total_entries > 0 else 0
        forward_rate = ((total_bonkbot + total_cla + total_monaco) / total_cas) * 100 if total_cas > 0 else 0
        
        print(f"\n📈 PERFORMANCE METRICS:")
        print(f"   🎯 CA Detection Rate: {ca_rate:.1f}% ({total_cas}/{total_entries})")
        print(f"   📤 Forward Success Rate: {forward_rate:.1f}% ({total_bonkbot + total_cla + total_monaco}/{total_cas})")
        print(f"   ⚡ Processing Rate: {total_entries/30:.1f} entries/minute")
    
    print(f"\n✅ PRODUCTION MONITORING COMPLETED SUCCESSFULLY")

if __name__ == "__main__":
    try:
        monitor_continuous()
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"\n💥 Monitoring error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
