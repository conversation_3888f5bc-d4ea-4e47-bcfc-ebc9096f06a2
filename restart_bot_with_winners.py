#!/usr/bin/env python3
"""
Restart CLA Bot with WINNERS properly reactivated
"""
import os
import sys
import time
import asyncio
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('restart_bot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def verify_winners_config():
    """Verify WINNERS is properly reactivated"""
    logger.info("Verifying WINNERS configuration...")
    
    # Check .env file
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            content = f.read()
            
        # Look for TRENDING_EXCLUDE_DESTINATIONS
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('TRENDING_EXCLUDE_DESTINATIONS=') and not line.startswith('#'):
                value = line.split('=', 1)[1].strip()
                if 'WINNERS' in value:
                    logger.error("WINNERS is still excluded! Line: " + line)
                    return False
                else:
                    logger.info("WINNERS exclusion properly cleared: " + line)
                    return True
        
        logger.info("No active TRENDING_EXCLUDE_DESTINATIONS found - WINNERS should be enabled")
        return True
    else:
        logger.warning("No .env file found")
        return True

def create_startup_message():
    """Create a clear startup message"""
    message = """
    ========================================
    CLA Bot Enhanced Restart
    ========================================
    
    WINNERS Status: REACTIVATED
    Mark Degens: ACTIVELY MONITORED
    Enhanced CA Detection: ENABLED
    
    Recent Fixes Applied:
    - WINNERS exclusion removed from .env
    - Enhanced stats tracker updated
    - Improved CA detection debugging
    - Optimized status reporting
    
    Monitoring for:
    - Mark Degens (-1001763265784)
    - CA detection and forwarding
    - WINNERS group forwarding
    
    ========================================
    """
    return message

async def main():
    try:
        print(create_startup_message())
        logger.info("Starting CLA Bot restart process...")
        
        # Verify WINNERS configuration
        if not verify_winners_config():
            logger.error("WINNERS configuration verification failed!")
            return False
        
        logger.info("Configuration verified - starting bot...")
        
        # Import and start the bot
        from main import main as bot_main
        await bot_main()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        logger.exception("Full error details:")
        return False
    
    return True

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)
