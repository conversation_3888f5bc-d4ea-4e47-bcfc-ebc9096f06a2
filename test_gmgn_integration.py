"""Comprehensive test for GMGN Featured Signals integration and trending analysis."""

import os
import sys
import asyncio
import tempfile
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

def test_gmgn_activation():
    """Test GMGN Featured Signals activation."""
    print("🧪 Testing GMGN Featured Signals Activation...")
    
    try:
        from config import config
        
        # Check if GMGN is now active
        gmgn_group_id = -1002202241417
        
        print(f"✅ Total groups configured: {len(config.target_group.all_group_ids)}")
        print(f"✅ Active groups: {len(config.target_group.active_group_ids)}")
        
        # Check if GMGN is in active groups
        if gmgn_group_id in config.target_group.active_group_ids:
            print("✅ GMGN Featured Signals is ACTIVE")
        else:
            print("❌ GMGN Featured Signals is still PAUSED")
            return False
        
        # Check group status
        status = config.target_group.group_status.get(gmgn_group_id, 'UNKNOWN')
        print(f"✅ GMGN status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ GMGN activation test failed: {e}")
        return False

def test_trending_configuration():
    """Test trending analysis configuration."""
    print("\n🧪 Testing Trending Analysis Configuration...")
    
    try:
        from config import config
        
        trending_config = config.trending
        
        print(f"✅ Trending enabled: {trending_config.enabled}")
        print(f"✅ Time window: {trending_config.time_window_minutes} minutes")
        print(f"✅ Min mentions: {trending_config.min_mentions}")
        print(f"✅ High volume groups: {trending_config.high_volume_groups}")
        print(f"✅ Exclude destinations: {trending_config.exclude_destinations}")
        
        # Verify GMGN is in high volume groups
        gmgn_group_id = -1002202241417
        if gmgn_group_id in trending_config.high_volume_groups:
            print("✅ GMGN is configured as high-volume group")
        else:
            print("❌ GMGN not in high-volume groups")
            return False
        
        # Verify WINNERS is excluded
        if 'WINNERS' in trending_config.exclude_destinations:
            print("✅ WINNERS is excluded from trending forwarding")
        else:
            print("❌ WINNERS not excluded from trending")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Trending configuration test failed: {e}")
        return False

def test_trending_analyzer():
    """Test trending analyzer functionality."""
    print("\n🧪 Testing Trending Analyzer...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        print(f"✅ Trending analyzer initialized")
        print(f"   Enabled: {analyzer.enabled}")
        print(f"   Time window: {analyzer.time_window_minutes} minutes")
        print(f"   Min mentions: {analyzer.min_mentions}")
        print(f"   High volume groups: {analyzer.high_volume_groups}")
        
        # Test CA mention analysis
        async def test_ca_analysis():
            ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
            gmgn_group_id = -1002202241417
            
            # First mention
            result1 = await analyzer.analyze_ca_mention(ca, gmgn_group_id, "GMGN Featured Signals", 1001)
            print(f"   First mention - Trending: {result1.is_trending}, Count: {result1.mention_count}")
            
            # Second mention
            result2 = await analyzer.analyze_ca_mention(ca, gmgn_group_id, "GMGN Featured Signals", 1002)
            print(f"   Second mention - Trending: {result2.is_trending}, Count: {result2.mention_count}")
            
            # Third mention (should trigger trending)
            result3 = await analyzer.analyze_ca_mention(ca, gmgn_group_id, "GMGN Featured Signals", 1003)
            print(f"   Third mention - Trending: {result3.is_trending}, Count: {result3.mention_count}")
            
            if result3.is_trending and result3.mention_count >= 3:
                print("✅ Trending detection working correctly")
                return True
            else:
                print("❌ Trending detection not working")
                return False
        
        # Run async test
        result = asyncio.run(test_ca_analysis())
        return result
        
    except Exception as e:
        print(f"❌ Trending analyzer test failed: {e}")
        return False

def test_destination_filtering():
    """Test destination filtering based on trending analysis."""
    print("\n🧪 Testing Destination Filtering...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        gmgn_group_id = -1002202241417
        regular_group_id = -1002380594298  # FREE WHALE SIGNALS
        
        # Test CA that's not trending
        ca_not_trending = "NotTrendingCA123456789012345678901234567890"
        
        # Test forwarding decisions for GMGN group
        bonkbot_forward = analyzer.should_forward_to_destination(ca_not_trending, 'BONKBOT', gmgn_group_id)
        winners_forward = analyzer.should_forward_to_destination(ca_not_trending, 'WINNERS', gmgn_group_id)
        cla_v2_forward = analyzer.should_forward_to_destination(ca_not_trending, 'CLA_V2', gmgn_group_id)
        
        print(f"   Non-trending CA from GMGN:")
        print(f"     BonkBot: {bonkbot_forward}")
        print(f"     WINNERS: {winners_forward} (should be False)")
        print(f"     CLA v2.0: {cla_v2_forward}")
        
        # Test forwarding decisions for regular group
        bonkbot_regular = analyzer.should_forward_to_destination(ca_not_trending, 'BONKBOT', regular_group_id)
        winners_regular = analyzer.should_forward_to_destination(ca_not_trending, 'WINNERS', regular_group_id)
        
        print(f"   Non-trending CA from regular group:")
        print(f"     BonkBot: {bonkbot_regular} (should be True)")
        print(f"     WINNERS: {winners_regular} (should be True)")
        
        # Verify filtering logic
        if not winners_forward and winners_regular and not bonkbot_forward and bonkbot_regular:
            print("✅ Destination filtering working correctly")
            return True
        else:
            print("❌ Destination filtering not working as expected")
            return False
        
    except Exception as e:
        print(f"❌ Destination filtering test failed: {e}")
        return False

def test_database_integration():
    """Test database integration for trending data."""
    print("\n🧪 Testing Database Integration...")
    
    try:
        from src.database import DatabaseManager
        import tempfile
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        async def test_db_operations():
            db_manager = DatabaseManager()
            db_manager.db_path = temp_db.name
            await db_manager.initialize()
            
            # Test CA mention tracking
            await db_manager.add_ca_mention(
                "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk",
                -1002202241417,
                "GMGN Featured Signals",
                1001
            )
            print("✅ CA mention added to database")
            
            # Test trending result tracking
            await db_manager.add_trending_result(
                "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk",
                -1002202241417,
                "GMGN Featured Signals",
                datetime.now() - timedelta(minutes=2),
                datetime.now(),
                3,
                120.0  # 2 minutes to trend
            )
            print("✅ Trending result added to database")
            
            # Test trending statistics
            stats = await db_manager.get_trending_stats(24)
            print(f"✅ Trending stats retrieved: {stats}")
            
            await db_manager.close()
            os.unlink(temp_db.name)
            
            return True
        
        result = asyncio.run(test_db_operations())
        return result
        
    except Exception as e:
        print(f"❌ Database integration test failed: {e}")
        return False

def test_high_volume_processing():
    """Test high-volume processing capability simulation."""
    print("\n🧪 Testing High-Volume Processing Simulation...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        import time
        
        analyzer = TrendingAnalyzer()
        gmgn_group_id = -1002202241417
        
        async def simulate_high_volume():
            start_time = time.time()
            
            # Simulate 50 CA mentions (high volume)
            for i in range(50):
                ca = f"HighVolume{i:044d}"  # Generate unique 44-char CAs
                await analyzer.analyze_ca_mention(ca, gmgn_group_id, "GMGN Featured Signals", 2000 + i)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✅ Processed 50 CAs in {processing_time:.2f} seconds")
            print(f"   Rate: {50/processing_time:.1f} CAs/second")
            
            # Get statistics
            stats = analyzer.get_trending_stats()
            print(f"   Total mentions: {stats['total_mentions']}")
            print(f"   Trending qualified: {stats['trending_qualified']}")
            print(f"   Noise filtered: {stats['noise_filtered']}")
            
            if processing_time < 5.0:  # Should process quickly
                print("✅ High-volume processing performance acceptable")
                return True
            else:
                print("❌ High-volume processing too slow")
                return False
        
        result = asyncio.run(simulate_high_volume())
        return result
        
    except Exception as e:
        print(f"❌ High-volume processing test failed: {e}")
        return False

def main():
    """Run all GMGN integration tests."""
    print("🚀 CLA v2.0 Bot - GMGN Featured Signals Integration Testing\n")
    
    try:
        # Test 1: GMGN Activation
        if not test_gmgn_activation():
            return False
        
        # Test 2: Trending Configuration
        if not test_trending_configuration():
            return False
        
        # Test 3: Trending Analyzer
        if not test_trending_analyzer():
            return False
        
        # Test 4: Destination Filtering
        if not test_destination_filtering():
            return False
        
        # Test 5: Database Integration
        if not test_database_integration():
            return False
        
        # Test 6: High-Volume Processing
        if not test_high_volume_processing():
            return False
        
        print("\n🎉 All GMGN integration tests passed!")
        print("\n📋 GMGN Integration Summary:")
        print("✅ GMGN Featured Signals: ACTIVATED")
        print("✅ Trending Analysis: Enabled (5min window, 3 mentions)")
        print("✅ High-Volume Processing: Ready for 10+ CAs/second")
        print("✅ Noise Reduction: WINNERS excluded for trending groups")
        print("✅ Database Tracking: CA mentions and trending results")
        print("✅ Performance Monitoring: Real-time statistics")
        
        print("\n🔧 The enhanced bot will now:")
        print("  - Monitor GMGN for high-volume CA activity")
        print("  - Apply trending analysis (3 mentions in 5 minutes)")
        print("  - Forward trending CAs to: BonkBot, CLA v2.0, Monaco PNL")
        print("  - Filter noise from GMGN (exclude WINNERS)")
        print("  - Track detailed trending statistics")
        
        return True
        
    except Exception as e:
        print(f"\n❌ GMGN integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
