#!/usr/bin/env python3
import sys
import os

print("Python version:", sys.version)
print("Current directory:", os.getcwd())
print("Python path:", sys.path[:3])

try:
    print("Testing basic imports...")
    import asyncio
    print("✓ asyncio imported")
    
    import logging
    print("✓ logging imported")
    
    print("Testing config import...")
    import config
    print("✓ config imported")
    
    print("Testing src imports...")
    from src import bot
    print("✓ src.bot imported")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"Import failed: {e}")
    import traceback
    traceback.print_exc()
