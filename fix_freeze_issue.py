#!/usr/bin/env python3
"""
Fix Freeze Issue Script
Addresses common causes of bot freezing on PythonAnywhere
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path

def print_step(step, description):
    """Print step with formatting."""
    print(f"\n🔧 STEP {step}: {description}")
    print("-" * 50)

def run_command(command, description=""):
    """Run command and return success status."""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description or 'Command'} successful")
            return True
        else:
            print(f"❌ {description or 'Command'} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def fix_session_file():
    """Fix Telegram session file issues."""
    print_step(1, "FIXING TELEGRAM SESSION")
    
    # Remove potentially corrupted session files
    session_files = [f for f in os.listdir('.') if f.startswith('cla_bot_session.session')]
    
    if session_files:
        print(f"Found session files: {session_files}")
        for session_file in session_files:
            try:
                os.remove(session_file)
                print(f"✅ Removed {session_file}")
            except Exception as e:
                print(f"❌ Failed to remove {session_file}: {e}")
    else:
        print("No session files found")
    
    print("\n💡 You'll need to re-authenticate after this fix")
    print("Run: python authenticate_telegram.py")
    return True

def fix_database_permissions():
    """Fix database file permissions."""
    print_step(2, "FIXING DATABASE PERMISSIONS")
    
    # Ensure data directory exists with correct permissions
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    try:
        os.chmod("data", 0o755)
        print("✅ Data directory permissions fixed")
    except Exception as e:
        print(f"❌ Failed to fix data directory permissions: {e}")
        return False
    
    # Remove database lock files if they exist
    lock_files = ["data/cla_bot.db-wal", "data/cla_bot.db-shm"]
    for lock_file in lock_files:
        if os.path.exists(lock_file):
            try:
                os.remove(lock_file)
                print(f"✅ Removed database lock file: {lock_file}")
            except Exception as e:
                print(f"❌ Failed to remove {lock_file}: {e}")
    
    return True

def fix_log_permissions():
    """Fix log file permissions."""
    print_step(3, "FIXING LOG PERMISSIONS")
    
    # Ensure logs directory exists with correct permissions
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    try:
        os.chmod("logs", 0o755)
        print("✅ Logs directory permissions fixed")
    except Exception as e:
        print(f"❌ Failed to fix logs directory permissions: {e}")
        return False
    
    return True

def install_missing_dependencies():
    """Install any missing dependencies."""
    print_step(4, "CHECKING DEPENDENCIES")
    
    # Check for base58 (you mentioned installing it)
    try:
        import base58
        print("✅ base58 is installed")
    except ImportError:
        print("Installing base58...")
        if run_command("pip install --user base58", "base58 installation"):
            print("✅ base58 installed successfully")
        else:
            print("❌ Failed to install base58")
            return False
    
    # Check other critical dependencies
    critical_deps = ['telethon', 'aiosqlite', 'loguru', 'python-dotenv']
    
    for dep in critical_deps:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✅ {dep} is available")
        except ImportError:
            print(f"❌ {dep} is missing")
            if run_command(f"pip install --user {dep}", f"{dep} installation"):
                print(f"✅ {dep} installed successfully")
            else:
                print(f"❌ Failed to install {dep}")
                return False
    
    return True

def optimize_environment_for_pythonanywhere():
    """Optimize environment settings for PythonAnywhere."""
    print_step(5, "OPTIMIZING FOR PYTHONANYWHERE")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("Please create .env file with your configuration")
        return False
    
    # Read current .env
    with open('.env', 'r') as f:
        env_content = f.read()
    
    # Add PythonAnywhere optimizations if not present
    optimizations = [
        "# PythonAnywhere Optimizations",
        "MESSAGE_CACHE_SIZE=5000",
        "CA_CACHE_EXPIRY_HOURS=72", 
        "DATABASE_BACKUP_INTERVAL=7200"
    ]
    
    needs_update = False
    for opt in optimizations:
        if opt not in env_content and not opt.startswith('#'):
            env_content += f"\n{opt}"
            needs_update = True
    
    if needs_update:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Added PythonAnywhere optimizations to .env")
    else:
        print("✅ Environment already optimized")
    
    return True

def kill_existing_processes():
    """Kill any existing bot processes."""
    print_step(6, "CLEANING UP PROCESSES")
    
    try:
        # Find and kill any existing main.py processes
        result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    print(f"Killing process {pid}")
                    subprocess.run(['kill', pid])
            print("✅ Existing processes cleaned up")
        else:
            print("✅ No existing processes found")
    except Exception as e:
        print(f"⚠️ Process cleanup failed: {e}")
    
    return True

async def test_quick_connection():
    """Test a quick connection to verify fixes."""
    print_step(7, "TESTING QUICK CONNECTION")
    
    try:
        # Test database connection
        sys.path.append('.')
        from src.database import DatabaseManager
        
        db = DatabaseManager()
        await db.connect()
        print("✅ Database connection test passed")
        await db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Quick connection test failed: {e}")
        return False

def main():
    """Main fix function."""
    print("🔧 CLA v2 BOT FREEZE FIX UTILITY")
    print("=" * 50)
    print("This script will fix common causes of bot freezing on PythonAnywhere")
    print("=" * 50)
    
    fixes = [
        ("Session File Fix", fix_session_file),
        ("Database Permissions", fix_database_permissions),
        ("Log Permissions", fix_log_permissions),
        ("Dependencies Check", install_missing_dependencies),
        ("PythonAnywhere Optimization", optimize_environment_for_pythonanywhere),
        ("Process Cleanup", kill_existing_processes)
    ]
    
    results = []
    
    for fix_name, fix_func in fixes:
        try:
            result = fix_func()
            results.append((fix_name, result))
        except Exception as e:
            print(f"❌ Error in {fix_name}: {e}")
            results.append((fix_name, False))
    
    # Test connection
    try:
        test_result = asyncio.run(test_quick_connection())
        results.append(("Connection Test", test_result))
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        results.append(("Connection Test", False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 FIX SUMMARY")
    print("=" * 50)
    
    passed = 0
    for fix_name, result in results:
        status = "✅ FIXED" if result else "❌ FAILED"
        print(f"{fix_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} fixes applied successfully")
    
    if passed >= len(results) - 1:  # Allow connection test to fail
        print("\n🎉 FIXES APPLIED SUCCESSFULLY!")
        print("\nNext steps:")
        print("1. Re-authenticate: python authenticate_telegram.py")
        print("2. Test with debug mode: python main_debug.py")
        print("3. If working, run normally: python main.py")
    else:
        print("\n❌ SOME FIXES FAILED")
        print("Please review the failed fixes above")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
