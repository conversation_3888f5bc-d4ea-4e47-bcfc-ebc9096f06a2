#!/usr/bin/env python3
"""Test script to verify the status report counting bug fix."""

import sys
import os
import json
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

def test_enhanced_stats_tracking():
    """Test the enhanced stats tracking functionality."""
    print("🧪 Testing Enhanced Stats Tracking Fix")
    print("=" * 50)
    
    try:
        # Import enhanced stats
        from src.enhanced_stats_tracker import enhanced_stats
        
        # Test group ID (FREE WHALE SIGNALS)
        test_group_id = -1002380594298
        
        print(f"📊 Current Enhanced Stats Summary:")
        current_stats = enhanced_stats.get_current_stats_summary()
        
        # Display destination stats
        print("\n📤 Destination Statistics:")
        destinations = current_stats.get('destinations', {})
        for dest_name, dest_stats in destinations.items():
            total_sent = dest_stats.get('total_cas_sent', 0)
            last_sent = dest_stats.get('last_sent')
            
            if last_sent:
                try:
                    last_time = datetime.fromisoformat(last_sent.replace('Z', '+00:00'))
                    time_since = (datetime.now() - last_time.replace(tzinfo=None)).total_seconds() / 60
                    last_sent_str = f"{time_since:.1f}min ago" if time_since < 60 else f"{time_since/60:.1f}h ago"
                except:
                    last_sent_str = "Unknown"
            else:
                last_sent_str = "Never"
            
            print(f"   {dest_name}: {total_sent} CAs sent | Last: {last_sent_str}")
        
        # Display group stats for FREE WHALE SIGNALS
        print(f"\n⚡ FREE WHALE SIGNALS Group Stats:")
        groups = current_stats.get('groups', {})
        fws_stats = groups.get(str(test_group_id), {})
        
        if fws_stats:
            print(f"   Messages Received: {fws_stats.get('messages_received', 0)}")
            print(f"   CAs Detected: {fws_stats.get('cas_detected', 0)}")
            print(f"   CAs Forwarded: {fws_stats.get('cas_forwarded', 0)}")
            print(f"   Duplicates Filtered: {fws_stats.get('cas_filtered_duplicate', 0)}")
            
            last_activity = fws_stats.get('last_activity')
            if last_activity:
                try:
                    last_time = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                    time_since = (datetime.now() - last_time.replace(tzinfo=None)).total_seconds() / 60
                    activity_str = f"{time_since:.1f}min ago" if time_since < 60 else f"{time_since/60:.1f}h ago"
                except:
                    activity_str = "Unknown"
            else:
                activity_str = "Never"
            
            print(f"   Last Activity: {activity_str}")
        else:
            print("   No stats available for FREE WHALE SIGNALS")
        
        # Test manual recording (simulation)
        print(f"\n🧪 Testing Manual Stats Recording:")
        print("   Simulating CA forwarding to test tracking...")
        
        # Record test forwarding
        enhanced_stats.record_ca_forwarded(test_group_id, "BonkBot", 1)
        enhanced_stats.record_ca_forwarded(test_group_id, "CLA v2.0", 1)
        enhanced_stats.record_ca_forwarded(test_group_id, "Monaco PNL", 1)
        
        print("   ✅ Test forwarding recorded")
        
        # Check updated stats
        updated_stats = enhanced_stats.get_current_stats_summary()
        updated_destinations = updated_stats.get('destinations', {})
        
        print("\n📤 Updated Destination Statistics:")
        for dest_name, dest_stats in updated_destinations.items():
            total_sent = dest_stats.get('total_cas_sent', 0)
            last_sent = dest_stats.get('last_sent')
            
            if last_sent:
                try:
                    last_time = datetime.fromisoformat(last_sent.replace('Z', '+00:00'))
                    time_since = (datetime.now() - last_time.replace(tzinfo=None)).total_seconds() / 60
                    last_sent_str = f"{time_since:.1f}min ago" if time_since < 60 else "Just now"
                except:
                    last_sent_str = "Just now"
            else:
                last_sent_str = "Never"
            
            print(f"   {dest_name}: {total_sent} CAs sent | Last: {last_sent_str}")
        
        # Generate test status report
        print(f"\n📋 Generating Test Status Report:")
        test_report = enhanced_stats.generate_hourly_report()
        
        # Extract destination section from report
        report_lines = test_report.split('\n')
        in_destinations = False
        
        for line in report_lines:
            if "📤 Forwarding Destinations:" in line:
                in_destinations = True
                print(f"   {line}")
            elif in_destinations and line.strip():
                if line.startswith("📈") or line.startswith("🛡️") or line.startswith("🎯"):
                    break
                print(f"   {line}")
        
        print("\n✅ Enhanced Stats Tracking Test Completed")
        print("🔧 The fix should now properly track forwarding statistics!")
        
    except Exception as e:
        print(f"❌ Error testing enhanced stats: {e}")
        import traceback
        traceback.print_exc()

def check_log_vs_stats_discrepancy():
    """Check for discrepancies between log entries and stats."""
    print("\n🔍 Checking Log vs Stats Discrepancy")
    print("=" * 50)
    
    try:
        # Count forwarding entries in logs
        log_file = 'logs/cla_bot.log'
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Count today's forwarding entries
            today = datetime.now().strftime('%Y-%m-%d')
            
            # Count specific forwarding patterns
            bonkbot_forwards = content.count('sent to BonkBot')
            cla_v2_forwards = content.count('sent to CLA v2.0')
            monaco_forwards = content.count('sent to Monaco PNL')
            total_forwarding_logs = content.count('Total forwarding:')
            
            print(f"📊 Log Analysis (Today: {today}):")
            print(f"   BonkBot forwards in logs: {bonkbot_forwards}")
            print(f"   CLA v2.0 forwards in logs: {cla_v2_forwards}")
            print(f"   Monaco PNL forwards in logs: {monaco_forwards}")
            print(f"   Total forwarding summary logs: {total_forwarding_logs}")
            
            # Count FREE WHALE SIGNALS specific entries
            fws_forwards = 0
            fws_total_logs = 0
            
            lines = content.split('\n')
            for line in lines:
                if today in line and 'FREE WHALE SIGNALS' in line:
                    if 'Total forwarding:' in line:
                        fws_total_logs += 1
                        # Extract numbers from the log
                        if 'BonkBot(1)' in line:
                            fws_forwards += 1
            
            print(f"\n⚡ FREE WHALE SIGNALS Specific:")
            print(f"   Total forwarding logs: {fws_total_logs}")
            print(f"   Successful forwards: {fws_forwards}")
            
        else:
            print("❌ Log file not found")
            
    except Exception as e:
        print(f"❌ Error analyzing logs: {e}")

if __name__ == "__main__":
    print("🔧 CLA v2.0 Status Report Fix Verification")
    print("=" * 60)
    
    test_enhanced_stats_tracking()
    check_log_vs_stats_discrepancy()
    
    print("\n" + "=" * 60)
    print("🎯 Summary:")
    print("   1. ✅ Added enhanced_stats.record_ca_forwarded() calls to main forwarding method")
    print("   2. ✅ Rescue forwarding already had proper tracking")
    print("   3. ✅ Status reports should now show accurate forwarding counts")
    print("   4. 🧪 Run this test after the bot processes new CAs to verify the fix")
