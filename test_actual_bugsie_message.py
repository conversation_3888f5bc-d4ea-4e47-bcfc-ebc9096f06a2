#!/usr/bin/env python3
"""
Test Actual BUGSIE Message Format
Tests CA extraction from the real BUGSIE message format (not truncated)
"""

import sys
sys.path.append('.')

def test_actual_bugsie_message():
    """Test CA extraction from the actual BUGSIE message format."""
    print("🧪 TESTING ACTUAL BUGSIE MESSAGE FORMAT")
    print("=" * 60)
    
    # The ACTUAL BUGSIE message format (complete, not truncated)
    actual_bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $NIL
├ DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk
└| ⏳ 1h | 👁️ 490

📊 Token details
├ PRICE:    $0
├ MC:       $56.2K /2.4X from VIP/
├ Vol:      $208.5K


🔒 Security
├ Dev S:    🟢
├ Dex P:    🟢
├ Risk:     only available for Premium users
├ Bundled:  only available for Premium users


🤖 Bots
└ only available for premium users

📈 Charts + Exchanges
└ only available for premium users
├ Axiom (https://axiom.trade/t/DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

💰 JOIN PREMIUM  TO CATCH THE NEXT 2.4X! 🔥🚀"""
    
    # Expected CA
    expected_ca = "DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk"
    
    print(f"Expected CA: {expected_ca}")
    print(f"CA Length: {len(expected_ca)} characters")
    print()
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test 1: CA Validation
        print("🧪 TEST 1: CA VALIDATION")
        print("-" * 30)
        is_valid = parser._validate_solana_ca(expected_ca)
        print(f"CA validation: {'✅ VALID' if is_valid else '❌ INVALID'}")
        
        if not is_valid:
            print("❌ CA validation failed - this is the root issue!")
            
            # Debug CA validation
            try:
                import base58
                decoded = base58.b58decode(expected_ca)
                print(f"Base58 decode: ✅ Success ({len(decoded)} bytes)")
                print(f"32-byte format: {'✅' if len(decoded) == 32 else '❌'}")
                print(f"Not all zeros: {'✅' if decoded != b'\\x00' * 32 else '❌'}")
            except Exception as e:
                print(f"Base58 decode: ❌ Failed - {e}")
        
        print()
        
        # Test 2: Activity Tracker Format Detection
        print("🧪 TEST 2: ACTIVITY TRACKER FORMAT DETECTION")
        print("-" * 30)
        is_activity_format = parser._is_activity_tracker_format(actual_bugsie_message)
        print(f"Activity Tracker format: {'✅ DETECTED' if is_activity_format else '❌ NOT DETECTED'}")
        
        if not is_activity_format:
            print("❌ Activity Tracker format not detected!")
            
            # Check individual indicators
            indicators = [
                "🔥 ACTIVITY DETECTED 🔥",
                "📊 Token details", 
                "🔒 Security",
                "├ PRICE:",
                "├ MC:",
                "from VIP"
            ]
            
            print("Checking individual indicators:")
            for indicator in indicators:
                found = indicator.upper() in actual_bugsie_message.upper()
                print(f"  '{indicator}': {'✅' if found else '❌'}")
        
        print()
        
        # Test 3: Activity Tracker CA Extraction
        print("🧪 TEST 3: ACTIVITY TRACKER CA EXTRACTION")
        print("-" * 30)
        
        if is_activity_format:
            activity_cas = parser._extract_activity_tracker_cas(actual_bugsie_message)
            print(f"Activity Tracker CAs: {activity_cas}")
            
            if expected_ca in activity_cas:
                print("✅ CA successfully extracted by Activity Tracker method")
            else:
                print("❌ CA not extracted by Activity Tracker method")
                
                # Debug line by line
                print("\\nDebugging line by line:")
                lines = actual_bugsie_message.split('\\n')
                for i, line in enumerate(lines):
                    if expected_ca in line:
                        print(f"Line {i}: '{line}'")
                        cleaned = line.replace('├', '').replace('└', '').replace('│', '').strip()
                        print(f"Cleaned: '{cleaned}'")
                        print(f"Length: {len(cleaned)}")
                        print(f"Is CA: {'✅' if cleaned == expected_ca else '❌'}")
                        print(f"Validation: {'✅' if parser._validate_solana_ca(cleaned) else '❌'}")
        
        print()
        
        # Test 4: URL Extraction
        print("🧪 TEST 4: URL EXTRACTION")
        print("-" * 30)
        url_cas = parser._extract_cas_from_urls(actual_bugsie_message)
        print(f"URL CAs: {url_cas}")
        
        if expected_ca in url_cas:
            print("✅ CA successfully extracted from Axiom URL")
        else:
            print("❌ CA not extracted from URL")
        
        print()
        
        # Test 5: Full Message Parser
        print("🧪 TEST 5: FULL MESSAGE PARSER")
        print("-" * 30)
        extracted_cas = parser.extract_contract_addresses(actual_bugsie_message)
        print(f"Full parser result: {extracted_cas}")
        
        if expected_ca in extracted_cas:
            print("✅ CA successfully extracted by full parser")
            extraction_success = True
        else:
            print("❌ CA not extracted by full parser")
            extraction_success = False
            
            # Test individual methods
            print("\\nTesting individual extraction methods:")
            
            # Standalone extraction
            cleaned_text = parser._clean_message_text_no_url_extraction(actual_bugsie_message)
            standalone_cas = parser._extract_standalone_cas(cleaned_text)
            print(f"Standalone CAs: {standalone_cas}")
            
            # Check if CA is in cleaned text
            if expected_ca in cleaned_text:
                print("✅ CA present in cleaned text")
            else:
                print("❌ CA lost during text cleaning")
        
        print()
        
        # Summary
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        
        tests = [
            ("CA Validation", is_valid),
            ("Activity Format Detection", is_activity_format),
            ("Activity CA Extraction", expected_ca in (activity_cas if is_activity_format else [])),
            ("URL CA Extraction", expected_ca in url_cas),
            ("Full Parser Extraction", extraction_success)
        ]
        
        passed = sum(1 for _, result in tests if result)
        total = len(tests)
        
        for test_name, result in tests:
            print(f"{test_name}: {'✅ PASS' if result else '❌ FAIL'}")
        
        print(f"\\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - CA extraction working correctly!")
        else:
            print("⚠️ ISSUES IDENTIFIED - see failed tests above")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_line_by_line_extraction():
    """Test line-by-line extraction to identify the exact issue."""
    print("\\n🧪 LINE-BY-LINE EXTRACTION TEST")
    print("=" * 60)
    
    # Test the specific line that contains the CA
    test_line = "├ DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk"
    expected_ca = "DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk"
    
    print(f"Test line: '{test_line}'")
    print(f"Expected CA: {expected_ca}")
    print()
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Step 1: Remove tree characters
        step1 = test_line.replace('├', '').replace('└', '').replace('│', '').strip()
        print(f"Step 1 (remove tree chars): '{step1}'")
        print(f"Length: {len(step1)}")
        print(f"Matches expected: {'✅' if step1 == expected_ca else '❌'}")
        
        # Step 2: Validate CA
        if step1 == expected_ca:
            is_valid = parser._validate_solana_ca(step1)
            print(f"Step 2 (validation): {'✅ VALID' if is_valid else '❌ INVALID'}")
            
            if not is_valid:
                print("❌ This is the root issue - CA validation is failing!")
        
        # Step 3: Test regex pattern
        ca_matches = parser.ca_pattern.findall(step1)
        print(f"Step 3 (regex pattern): {ca_matches}")
        
        # Step 4: Test full extraction on just this line
        line_extraction = parser.extract_contract_addresses(test_line)
        print(f"Step 4 (full extraction): {line_extraction}")
        
        return step1 == expected_ca and len(line_extraction) > 0
        
    except Exception as e:
        print(f"❌ Line test failed: {e}")
        return False

def main():
    """Run all tests for the actual BUGSIE message format."""
    print("🚀 ACTUAL BUGSIE MESSAGE FORMAT TEST")
    print("=" * 80)
    print("Testing CA extraction from real BUGSIE message (not truncated)")
    print("=" * 80)
    
    # Run tests
    message_success = test_actual_bugsie_message()
    line_success = test_line_by_line_extraction()
    
    print("\\n🎯 FINAL RESULTS")
    print("=" * 80)
    print(f"Full Message Test: {'✅ PASS' if message_success else '❌ FAIL'}")
    print(f"Line-by-Line Test: {'✅ PASS' if line_success else '❌ FAIL'}")
    
    if message_success and line_success:
        print("\\n🎉 ALL TESTS PASSED!")
        print("✅ BUGSIE CA extraction working correctly")
    else:
        print("\\n⚠️ ISSUES IDENTIFIED")
        print("The root cause of BUGSIE CA extraction failure has been identified")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
