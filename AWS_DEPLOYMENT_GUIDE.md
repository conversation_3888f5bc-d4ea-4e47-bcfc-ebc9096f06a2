# CLA v2.0 Telegram Bot - Complete AWS EC2 Deployment Guide

## 🎯 **OVERVIEW**

This guide provides complete step-by-step instructions for deploying your CLA v2.0 Telegram bot to AWS EC2 free tier. Follow this guide to get your bot running in production on AWS.

---

## 📋 **PREREQUISITES CHECKLIST**

### **AWS Account Setup**
- [ ] AWS Free Tier account created
- [ ] Payment method added to AWS account
- [ ] AWS CLI installed locally (optional but recommended)
- [ ] Basic understanding of AWS EC2 console

### **Local Environment**
- [ ] Bo<PERSON> tested and working locally
- [ ] All required API keys and credentials available
- [ ] SSH client installed (PuTTY for Windows, Terminal for Mac/Linux)
- [ ] Your current public IP address noted

### **Bot Configuration**
- [ ] Telegram API ID and Hash from https://my.telegram.org/apps
- [ ] Telegram phone number for authentication
- [ ] All Telegram group IDs identified
- [ ] BonkBot chat ID configured
- [ ] Integration channel IDs ready

---

## 🚀 **PHASE 1: AWS INFRASTRUCTURE SETUP**

### **Step 1: Create SSH Key Pair**

1. **Go to AWS EC2 Console**
   - Navigate to https://console.aws.amazon.com/ec2/
   - Select region: `us-east-1` (N. Virginia) for free tier

2. **Create Key Pair**
   ```
   EC2 Dashboard → Key Pairs → Create key pair

   Name: cla-bot-key
   Key pair type: RSA
   Private key file format: .pem

   Click "Create key pair"
   ```

3. **Secure Your Key**
   - Download saves automatically
   - Move to secure location: `~/.ssh/cla-bot-key.pem`
   - Set permissions: `chmod 400 ~/.ssh/cla-bot-key.pem`

### **Step 2: Create Security Group**

1. **Create Security Group**
   ```
   EC2 Dashboard → Security Groups → Create security group

   Name: cla-bot-sg
   Description: Security group for CLA v2.0 Telegram Bot
   VPC: Default VPC
   ```

2. **Configure Inbound Rules**
   ```
   Rule 1 - SSH:
   Type: SSH (22)
   Source: My IP (YOUR_IP/32)
   Description: SSH access

   Rule 2 - Health Check:
   Type: Custom TCP (8080)
   Source: My IP (YOUR_IP/32)
   Description: Bot health check
   ```

### **Step 3: Launch EC2 Instance**

1. **Launch Instance**
   ```
   EC2 Dashboard → Instances → Launch instances

   Name: cla-bot-server
   OS: Ubuntu Server 22.04 LTS (Free tier eligible)
   Instance type: t2.micro (Free tier eligible)
   Key pair: cla-bot-key
   Security group: cla-bot-sg
   Storage: 8 GiB gp3 (Free tier eligible)
   ```

2. **Wait for Instance to Start**
   - Status should show "running"
   - Note the public IP address

### **Step 4: Test SSH Connection**

```bash
# Test connection (replace with your IP and key path)
ssh -i ~/.ssh/cla-bot-key.pem ubuntu@YOUR_EC2_PUBLIC_IP

# If successful, you should see Ubuntu welcome message
```

---

## 🔧 **PHASE 2: AUTOMATED DEPLOYMENT**

### **Option A: Automated Deployment (Recommended)**

1. **Prepare Local Environment**
   ```bash
   # Navigate to your bot directory
   cd "C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2"

   # Make deployment script executable (Linux/Mac)
   chmod +x aws-deployment/deploy-to-ec2.sh
   ```

2. **Run Deployment Script**

   **For Linux/Mac:**
   ```bash
   ./aws-deployment/deploy-to-ec2.sh YOUR_EC2_IP ~/.ssh/cla-bot-key.pem
   ```

   **For Windows PowerShell:**
   ```powershell
   .\aws-deployment\deploy-to-ec2.ps1 -EC2IP "YOUR_EC2_IP" -SSHKey "C:\path\to\cla-bot-key.pem"
   ```

3. **Script Will Automatically:**
   - Test SSH connection
   - Create deployment package
   - Transfer files to EC2
   - Install Python 3.11 and dependencies
   - Set up virtual environment
   - Create bot user and directories
   - Install systemd service
   - Configure log rotation and monitoring

### **Option B: Manual Deployment**

1. **Transfer Files to EC2**
   ```bash
   # Create deployment package
   tar -czf cla-bot-deployment.tar.gz \
       --exclude='.git' \
       --exclude='__pycache__' \
       --exclude='.venv' \
       --exclude='logs/*' \
       --exclude='data/*' \
       --exclude='*.log' \
       --exclude='.env' \
       src/ *.py requirements.txt aws-deployment/ deployment/ *.md

   # Transfer to EC2
   scp -i ~/.ssh/cla-bot-key.pem cla-bot-deployment.tar.gz ubuntu@YOUR_EC2_IP:/tmp/
   ```

2. **Connect and Install**
   ```bash
   # Connect to EC2
   ssh -i ~/.ssh/cla-bot-key.pem ubuntu@YOUR_EC2_IP

   # Extract files
   cd /tmp
   tar -xzf cla-bot-deployment.tar.gz

   # Run installation
   sudo bash aws-deployment/aws-install.sh
   ```

---

## ⚙️ **PHASE 3: CONFIGURATION**

### **Step 1: Configure Environment Variables**

1. **Connect to EC2**
   ```bash
   ssh -i ~/.ssh/cla-bot-key.pem ubuntu@YOUR_EC2_IP
   ```

2. **Edit Configuration**
   ```bash
   sudo nano /opt/cla-bot/.env
   ```

3. **Update Required Values**
   ```bash
   # Telegram API (REQUIRED)
   TELEGRAM_API_ID=YOUR_ACTUAL_API_ID
   TELEGRAM_API_HASH=YOUR_ACTUAL_API_HASH
   TELEGRAM_PHONE=YOUR_ACTUAL_PHONE_NUMBER

   # BonkBot Integration (REQUIRED)
   BONKBOT_CHAT_ID=YOUR_ACTUAL_BONKBOT_CHAT_ID

   # Update group IDs with your actual values
   TARGET_GROUP_ID=YOUR_ACTUAL_TARGET_GROUP_ID
   ADDITIONAL_GROUP_IDS=YOUR_ACTUAL_GROUP_IDS

   # Integration channels
   CLA_V2_GROUP_ID=YOUR_ACTUAL_CLA_V2_GROUP_ID
   MONACO_PNL_GROUP_ID=YOUR_ACTUAL_MONACO_PNL_GROUP_ID
   WINNERS_GROUP_ID=YOUR_ACTUAL_WINNERS_GROUP_ID
   ```

4. **Save and Exit**
   - Press `Ctrl+X`, then `Y`, then `Enter`

### **Step 2: Set Secure Permissions**
```bash
sudo chmod 640 /opt/cla-bot/.env
sudo chown cla-bot:cla-bot /opt/cla-bot/.env
```

---

## 🚀 **PHASE 4: START AND VERIFY BOT**

### **Step 1: Start the Bot Service**

```bash
# Start the bot service
sudo systemctl start cla-bot

# Enable auto-start on boot
sudo systemctl enable cla-bot

# Check service status
sudo systemctl status cla-bot
```

### **Step 2: Verify Bot is Running**

1. **Check Service Status**
   ```bash
   # Quick status check
   cla-bot-status

   # Detailed service status
   sudo systemctl status cla-bot --no-pager -l
   ```

2. **Check Health Endpoint**
   ```bash
   # Test health check endpoint
   curl http://127.0.0.1:8080/health

   # Should return JSON with bot status
   ```

3. **Monitor Logs**
   ```bash
   # View real-time logs
   sudo journalctl -u cla-bot -f

   # View recent logs
   sudo journalctl -u cla-bot --since "10 minutes ago"

   # View application logs
   tail -f /opt/cla-bot/logs/cla_bot.log
   ```

### **Step 3: Telegram Authentication**

1. **Monitor Authentication Process**
   ```bash
   # Watch logs for authentication prompts
   sudo journalctl -u cla-bot -f
   ```

2. **If Phone Verification Required**
   - Bot will prompt for phone verification code
   - Check your Telegram app for verification code
   - Enter code when prompted in logs

3. **Verify Telegram Connection**
   - Bot should connect to Telegram successfully
   - Check logs for "Bot started successfully" message

---

## 📊 **PHASE 5: MONITORING AND MAINTENANCE**

### **CloudWatch Monitoring Setup**

1. **Enable Detailed Monitoring**
   ```
   EC2 Dashboard → Instances → Select your instance
   Actions → Monitor and troubleshoot → Manage detailed monitoring
   Enable detailed monitoring
   ```

2. **Create CloudWatch Alarms**
   ```
   CloudWatch Dashboard → Alarms → Create alarm

   CPU Alarm:
   - Metric: EC2 > Per-Instance Metrics > CPUUtilization
   - Threshold: Greater than 80%
   - Period: 5 minutes

   Memory Alarm (requires CloudWatch agent):
   - Metric: CWAgent > MemoryUtilization
   - Threshold: Greater than 85%
   ```

3. **Set Up Billing Alerts**
   ```
   AWS Billing Dashboard → Billing preferences
   ✓ Receive Billing Alerts

   CloudWatch → Alarms → Create alarm
   - Metric: Billing > Total Estimated Charge
   - Threshold: Greater than $1.00
   ```

### **Regular Maintenance Commands**

```bash
# Check bot status
cla-bot-status

# View logs
sudo journalctl -u cla-bot -f

# Restart bot if needed
sudo systemctl restart cla-bot

# Check disk usage
df -h /opt/cla-bot

# Check memory usage
free -h

# View recent backups
ls -la /opt/cla-bot/backups/

# Manual backup
sudo -u cla-bot /opt/cla-bot/scripts/backup.sh

# Update system packages
sudo apt update && sudo apt upgrade -y

# Check security status
sudo fail2ban-client status sshd
sudo ufw status verbose
```

---

## 🔒 **SECURITY BEST PRACTICES**

### **SSH Security**
- [ ] SSH key authentication only (no passwords)
- [ ] SSH access restricted to your IP only
- [ ] Fail2Ban configured and running
- [ ] Non-standard SSH port (optional but recommended)

### **Application Security**
- [ ] Bot runs as dedicated user (not root)
- [ ] Environment variables secured with proper permissions
- [ ] Database files protected
- [ ] Log files rotated and secured

### **Network Security**
- [ ] Security group allows minimal required access
- [ ] UFW firewall enabled and configured
- [ ] Health check endpoint restricted to your IP
- [ ] No unnecessary ports exposed

### **Monitoring Security**
- [ ] CloudWatch monitoring enabled
- [ ] Billing alerts configured
- [ ] Log monitoring for suspicious activity
- [ ] Regular security updates applied

---

## 💰 **COST OPTIMIZATION**

### **Free Tier Monitoring**
```bash
# Check current usage
aws ec2 describe-instances --query 'Reservations[*].Instances[*].[InstanceId,InstanceType,State.Name]'

# Monitor data transfer
aws cloudwatch get-metric-statistics \
    --namespace AWS/EC2 \
    --metric-name NetworkOut \
    --dimensions Name=InstanceId,Value=YOUR_INSTANCE_ID \
    --start-time 2025-07-01T00:00:00Z \
    --end-time 2025-07-31T23:59:59Z \
    --period 86400 \
    --statistics Sum
```

### **Cost Control Measures**
- [ ] Use t2.micro instance only
- [ ] Monitor monthly usage in AWS Billing Dashboard
- [ ] Set up billing alerts for $1, $5, and $10
- [ ] Stop instance during extended maintenance
- [ ] Use log rotation to prevent disk bloat
- [ ] Monitor data transfer usage

---

## 🔧 **TROUBLESHOOTING**

### **Common Issues and Solutions**

1. **Bot Won't Start**
   ```bash
   # Check configuration
   sudo nano /opt/cla-bot/.env

   # Check logs for errors
   sudo journalctl -u cla-bot --since "1 hour ago"

   # Verify Python environment
   sudo -u cla-bot /opt/cla-bot/.venv/bin/python --version

   # Test configuration
   sudo -u cla-bot /opt/cla-bot/.venv/bin/python -c "from config import config; print('Config loaded successfully')"
   ```

2. **Telegram Authentication Issues**
   ```bash
   # Remove old session and restart
   sudo rm -f /opt/cla-bot/*.session*
   sudo systemctl restart cla-bot

   # Monitor authentication process
   sudo journalctl -u cla-bot -f
   ```

3. **High Memory Usage**
   ```bash
   # Check memory usage
   free -h
   htop

   # Restart bot to clear memory
   sudo systemctl restart cla-bot

   # Check for memory leaks in logs
   grep -i "memory\|oom" /opt/cla-bot/logs/cla_bot.log
   ```

4. **Database Issues**
   ```bash
   # Check database file
   ls -la /opt/cla-bot/data/

   # Test database connection
   sqlite3 /opt/cla-bot/data/cla_bot.db ".tables"

   # Restore from backup if needed
   sudo -u cla-bot tar -xzf /opt/cla-bot/backups/latest_backup.tar.gz -C /opt/cla-bot/
   ```

5. **Network Connectivity Issues**
   ```bash
   # Test internet connectivity
   ping -c 4 *******

   # Test Telegram API access
   curl -I https://api.telegram.org

   # Check security group rules
   # Ensure outbound traffic is allowed
   ```

---

## ✅ **DEPLOYMENT VERIFICATION CHECKLIST**

### **Infrastructure**
- [ ] EC2 instance running and accessible
- [ ] Security group properly configured
- [ ] SSH access working
- [ ] Elastic IP attached (if using)

### **Application**
- [ ] Bot service running (`systemctl status cla-bot`)
- [ ] Health check responding (`curl http://127.0.0.1:8080/health`)
- [ ] Telegram authentication successful
- [ ] All required environment variables configured

### **Security**
- [ ] SSH key authentication working
- [ ] Fail2Ban active (`sudo fail2ban-client status`)
- [ ] UFW firewall enabled (`sudo ufw status`)
- [ ] File permissions secure

### **Monitoring**
- [ ] CloudWatch monitoring enabled
- [ ] Billing alerts configured
- [ ] Log rotation working
- [ ] Backup script functional

### **Functionality**
- [ ] Bot connecting to Telegram groups
- [ ] Message processing working
- [ ] Forwarding to target channels working
- [ ] Database operations successful

---

## 🎉 **CONGRATULATIONS!**

Your CLA v2.0 Telegram bot is now successfully deployed on AWS EC2!

### **What's Next?**
1. **Monitor Performance**: Keep an eye on CloudWatch metrics and logs
2. **Regular Maintenance**: Apply security updates and monitor costs
3. **Optimize Configuration**: Fine-tune bot settings based on usage patterns
4. **Scale if Needed**: Consider upgrading instance type if you exceed free tier limits

### **Support Resources**
- **AWS Documentation**: https://docs.aws.amazon.com/ec2/
- **Telegram Bot API**: https://core.telegram.org/bots/api
- **Ubuntu Server Guide**: https://ubuntu.com/server/docs

Your bot is now running 24/7 in the cloud, ready to monitor Telegram groups and forward signals automatically!