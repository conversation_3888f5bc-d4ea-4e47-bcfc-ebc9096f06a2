"""Quick bot startup test."""

import asyncio
import sys
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def quick_test():
    """Quick test of bot startup."""
    print("🔍 QUICK BOT STARTUP TEST")
    print("=" * 30)
    
    try:
        print("1. Testing imports...")
        from main import CLABotRunner
        print("   ✅ Main imports OK")
        
        print("2. Creating runner...")
        runner = CLABotRunner()
        print("   ✅ Runner created")
        
        print("3. Testing initialization...")
        start_time = time.time()
        
        # Set a timeout for initialization
        try:
            await asyncio.wait_for(runner.initialize(), timeout=30.0)
            init_time = time.time() - start_time
            print(f"   ✅ Initialization completed in {init_time:.2f}s")
            
            print("4. Testing startup...")
            start_time = time.time()
            
            # Start the bot with timeout
            await asyncio.wait_for(runner.start(), timeout=10.0)
            start_time_elapsed = time.time() - start_time
            print(f"   ✅ Bot started in {start_time_elapsed:.2f}s")
            
            print("✅ Bot is running successfully!")
            
            # Let it run for a few seconds to see if it processes messages
            print("🔄 Monitoring for 30 seconds...")
            await asyncio.sleep(30)
            
            print("🛑 Stopping bot...")
            runner.running = False
            
        except asyncio.TimeoutError:
            print("   ❌ Initialization/startup timed out")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(quick_test())
        if result:
            print("✅ Quick test passed!")
        else:
            print("❌ Quick test failed!")
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted")
    except Exception as e:
        print(f"\n💥 Test error: {e}")
