# CLA v2 Telegram Bot - PythonAnywhere Deployment Guide

**⚠️ PRIVATE REPOSITORY - INTERNAL DEPLOYMENT GUIDE**

This comprehensive guide provides step-by-step instructions for deploying the CLA v2 Telegram bot on PythonAnywhere hosting platform.

## 📋 Pre-Deployment Requirements

### Python Environment Requirements

**Minimum Python Version**: Python 3.8+  
**Recommended**: Python 3.9 or 3.10  
**PythonAnywhere Compatibility**: Verified on PythonAnywhere's Python 3.9 environment

### Required Dependencies

**Core Dependencies** (from requirements.txt):
```
telethon==1.28.5
aiosqlite==0.19.0
loguru==0.7.0
python-dotenv==1.0.0
asyncio-throttle==1.0.2
```

**Additional Dependencies**:
```
aiofiles==23.1.0
cryptg==0.4.0  # Optional: Faster encryption
```

### Environment Variables Required

**Telegram API Configuration**:
```bash
TELEGRAM_API_ID=29232008
TELEGRAM_API_HASH=431f5f680d1975acfdd9050089661772
TELEGRAM_PHONE=+************
TELEGRAM_SESSION_NAME=cla_bot_session
```

**Group Configuration**:
```bash
TARGET_GROUP_ID=-1002380594298
TARGET_GROUP_NAME=FREE WHALE SIGNALS

ADDITIONAL_GROUP_IDS=-1002270988204,-1002064145465,-1001763265784,-1002139128702,-1002202241417,-1002333406905,-1002356333152
ADDITIONAL_GROUP_NAMES=Solana Activity Tracker,🌹 MANIFEST,👤 Mark Degens,💎 FINDERTRENDING,🧠 GMGN Featured Signals,🚀 MEME 1000X,BUGSIE
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE
```

**Destination Configuration**:
```bash
BONKBOT_USER_ID=5312785865
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391
```

**Database and Cache Configuration**:
```bash
DATABASE_PATH=./data/cla_bot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=3600
CA_CACHE_EXPIRY_HOURS=168
MESSAGE_CACHE_SIZE=10000
DUPLICATE_PREVENTION_HOURS=24
```

### Required Directory Structure

```
/home/<USER>/clav2/
├── src/                    # Core bot modules
├── tests/                  # Test scripts
├── data/                   # Database storage (create manually)
├── logs/                   # Log files (create manually)
├── main.py                 # Main bot entry point
├── authenticate_telegram.py
├── config.py
├── requirements.txt
├── .env                    # Environment variables
└── README.md
```

## 🚀 PythonAnywhere Deployment Steps

### Step 1: Upload Codebase to PythonAnywhere

**Method 1: Git Clone (Recommended)**
```bash
# In PythonAnywhere Bash console
cd ~
git clone https://github.com/bigdrako1/clav2.git
cd clav2
```

**Method 2: File Upload**
1. Download repository as ZIP from GitHub
2. Upload via PythonAnywhere Files interface
3. Extract in home directory

### Step 2: Create Required Directories

```bash
# In PythonAnywhere Bash console
cd ~/clav2
mkdir -p data logs
chmod 755 data logs
```

### Step 3: Install Dependencies

```bash
# In PythonAnywhere Bash console
cd ~/clav2

# Install dependencies
pip3.9 install --user -r requirements.txt

# Verify installation
pip3.9 list | grep -E "(telethon|aiosqlite|loguru|python-dotenv|asyncio-throttle)"
```

### Step 4: Configure Environment Variables

**Create .env file**:
```bash
# In PythonAnywhere Bash console
cd ~/clav2
nano .env
```

**Copy and paste environment variables** (replace with your actual values):
```bash
# Telegram API Configuration
TELEGRAM_API_ID=YOUR_API_ID
TELEGRAM_API_HASH=YOUR_API_HASH
TELEGRAM_PHONE=YOUR_PHONE_NUMBER
TELEGRAM_SESSION_NAME=cla_bot_session

# Group Configuration (use provided values)
TARGET_GROUP_ID=-1002380594298
TARGET_GROUP_NAME=FREE WHALE SIGNALS

ADDITIONAL_GROUP_IDS=-1002270988204,-1002064145465,-1001763265784,-1002139128702,-1002202241417,-1002333406905,-1002356333152
ADDITIONAL_GROUP_NAMES=Solana Activity Tracker,🌹 MANIFEST,👤 Mark Degens,💎 FINDERTRENDING,🧠 GMGN Featured Signals,🚀 MEME 1000X,BUGSIE
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE

# Destination Configuration (use provided values)
BONKBOT_USER_ID=5312785865
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391

# Database Configuration
DATABASE_PATH=./data/cla_bot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=3600
CA_CACHE_EXPIRY_HOURS=168
MESSAGE_CACHE_SIZE=10000
DUPLICATE_PREVENTION_HOURS=24
```

**Save and exit**: Ctrl+X, Y, Enter

### Step 5: Set File Permissions

```bash
# In PythonAnywhere Bash console
cd ~/clav2
chmod +x main.py authenticate_telegram.py
chmod 644 .env
chmod -R 755 src/ tests/
```

## 🔐 Telegram Authentication Setup

### Step 1: Run Authentication Script

```bash
# In PythonAnywhere Bash console
cd ~/clav2
python3.9 authenticate_telegram.py
```

### Step 2: Handle 2FA Verification

**Expected Output**:
```
Please enter your phone (or bot token): +************
Please enter the code you received: 
```

**Steps**:
1. Check your Telegram app for verification code
2. Enter the 5-digit code when prompted
3. If 2FA is enabled, enter your password when requested

**Success Indicators**:
```
✅ Authentication successful!
✅ Session file created: cla_bot_session.session
✅ Connected as: Your Name (@yourusername)
```

### Step 3: Verify Session File

```bash
# Check session file was created
ls -la cla_bot_session.session*

# Expected output:
# -rw-r--r-- 1 <USER> <GROUP> XXXX date cla_bot_session.session
```

## ✅ Pre-Deployment Verification Checklist

### Test 1: Dependencies and Imports

```bash
# In PythonAnywhere Bash console
cd ~/clav2
python3.9 -c "
import sys
sys.path.append('.')
try:
    from src.bot import CLABot
    from src.ca_detector import CADetector
    from src.message_parser import MessageParser
    print('✅ All core modules imported successfully')
except Exception as e:
    print(f'❌ Import error: {e}')
"
```

### Test 2: Configuration Validation

```bash
# Test configuration loading
python3.9 -c "
import sys
sys.path.append('.')
try:
    from config import config
    print('✅ Configuration loaded successfully')
    print(f'Target groups: {len(config.target_group.all_group_ids)}')
    print(f'Database path: {config.database.path}')
except Exception as e:
    print(f'❌ Configuration error: {e}')
"
```

### Test 3: Database Connectivity

```bash
# Test database initialization
python3.9 -c "
import sys, asyncio
sys.path.append('.')
async def test_db():
    try:
        from src.database import DatabaseManager
        db = DatabaseManager()
        await db.connect()
        print('✅ Database connection successful')
        await db.close()
    except Exception as e:
        print(f'❌ Database error: {e}')
asyncio.run(test_db())
"
```

### Test 4: Telegram Connectivity

```bash
# Test Telegram connection
python3.9 -c "
import sys, asyncio
sys.path.append('.')
async def test_telegram():
    try:
        from src.telegram_client import TelegramClientManager
        client = TelegramClientManager()
        await client.connect()
        print('✅ Telegram connection successful')
        me = await client.client.get_me()
        print(f'Connected as: {me.first_name}')
        await client.disconnect()
    except Exception as e:
        print(f'❌ Telegram error: {e}')
asyncio.run(test_telegram())
"
```

### Test 5: Run Verification Test Suites

```bash
# Run IndentationError and WINNERS disable verification
python3.9 test_winners_disabled_and_indentation_fix.py

# Run CA extraction optimization verification
python3.9 test_optimized_ca_extraction.py
```

**Expected Results**: All tests should show "✅ PASS" status

## 🔧 PythonAnywhere-Specific Configuration

### Console Application Setup

**Create startup script** (`start_bot.sh`):
```bash
#!/bin/bash
cd /home/<USER>/clav2
python3.9 main.py
```

**Make executable**:
```bash
chmod +x start_bot.sh
```

### Always-On Tasks Configuration

**For Paid Accounts** (Always-On Tasks):
1. Go to PythonAnywhere Dashboard
2. Click "Tasks" tab
3. Create new task:
   - **Command**: `/home/<USER>/clav2/start_bot.sh`
   - **Hour**: `*` (every hour)
   - **Minute**: `0` (at minute 0)

### Console Monitoring Setup

**Create monitoring script** (`monitor_bot.py`):
```python
#!/usr/bin/env python3.9
import time
import subprocess
import sys
import os

def monitor_bot():
    while True:
        try:
            # Check if bot is running
            result = subprocess.run(['pgrep', '-f', 'main.py'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Bot not running, starting...")
                os.chdir('/home/<USER>/clav2')
                subprocess.Popen([sys.executable, 'main.py'])
            else:
                print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Bot running (PID: {result.stdout.strip()})")
            
            time.sleep(300)  # Check every 5 minutes
            
        except Exception as e:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] Monitor error: {e}")
            time.sleep(60)

if __name__ == "__main__":
    monitor_bot()
```

## 📊 Runtime Monitoring and Logging

### Log File Monitoring

**View real-time logs**:
```bash
# Monitor main log file
tail -f ~/clav2/logs/cla_bot.log

# Monitor error logs
tail -f ~/clav2/logs/error.log

# Monitor specific patterns
tail -f ~/clav2/logs/cla_bot.log | grep -E "📤|✅|❌"
```

### Performance Monitoring

**Check bot status**:
```bash
# Check if bot process is running
ps aux | grep main.py

# Check memory usage
ps aux | grep main.py | awk '{print $4"%"}'

# Check log file sizes
du -h ~/clav2/logs/*.log
```

### Database Monitoring

**Check database size and health**:
```bash
# Database file size
ls -lh ~/clav2/data/cla_bot.db

# Quick database check
python3.9 -c "
import sqlite3
conn = sqlite3.connect('/home/<USER>/clav2/data/cla_bot.db')
cursor = conn.execute('SELECT COUNT(*) FROM contract_addresses')
print(f'Total CAs in database: {cursor.fetchone()[0]}')
conn.close()
"
```

## 🚀 Bot Deployment and Startup

### Method 1: Manual Startup (Testing)

```bash
# In PythonAnywhere Bash console
cd ~/clav2
python3.9 main.py
```

**Expected Startup Output**:
```
✅ Configuration validation passed
✅ Database connected successfully
✅ Telegram client connected: True
⏸️ WINNERS integration temporarily disabled
Forwarding to 3 destinations: BonkBot, CLA v2.0, Monaco PNL (WINNERS temporarily disabled)
🔍 DIAGNOSTICS: Waiting for any message from monitored groups...
```

### Method 2: Background Process

```bash
# Start bot in background
cd ~/clav2
nohup python3.9 main.py > bot_output.log 2>&1 &

# Check if running
ps aux | grep main.py

# View output
tail -f bot_output.log
```

### Method 3: Screen Session (Recommended)

```bash
# Create screen session
screen -S clav2_bot

# Inside screen session
cd ~/clav2
python3.9 main.py

# Detach from screen: Ctrl+A, then D
# Reattach later: screen -r clav2_bot
```

## ✅ Post-Deployment Validation Checklist

### Validation 1: Bot Initialization

**Check startup logs for these indicators**:
```
✅ Configuration validation passed
✅ Database connected successfully
✅ BonkBot integration initialized
✅ CLA v2.0 integration initialized
✅ Monaco PNL integration initialized
⏸️ WINNERS integration temporarily disabled
✅ Message listener is now active and ready to receive messages
```

### Validation 2: Integration Status

**Test each integration**:
```bash
# Check integration status
python3.9 -c "
import sys, asyncio
sys.path.append('.')
async def test_integrations():
    try:
        from src.database import DatabaseManager
        from src.bot import CLABot

        db = DatabaseManager()
        await db.connect()
        bot = CLABot(db)

        # Test initialization (without starting)
        print('Testing integrations...')
        # This will show which integrations are available

        await db.close()
        print('✅ Integration test completed')
    except Exception as e:
        print(f'❌ Integration test failed: {e}')

asyncio.run(test_integrations())
"
```

### Validation 3: Message Reception Test

**Monitor for message reception**:
```bash
# Watch logs for message reception
tail -f ~/clav2/logs/cla_bot.log | grep -E "MESSAGE RECEIVED|📨|🧠|🚀"
```

**Expected patterns**:
```
🧠 GMGN: ID=12345 | 🚀 NEW TOKEN ALERT...
🚀 MEME: ID=12346 | 🔥 HOT SIGNAL...
📨 FREE WHALE SIGNALS: ID=12347 | New discovery...
```

### Validation 4: CA Detection and Forwarding

**Test CA detection**:
```bash
# Monitor CA detection and forwarding
tail -f ~/clav2/logs/cla_bot.log | grep -E "📤|✅|CAs detected|FORWARDING"
```

**Expected patterns**:
```
🔍 CAs detected: 1 | Trending qualified: 1 | Source: GMGN Featured Signals
📤 PARALLEL FORWARDING: BonkBot(1) | CLA v2.0(1) | Monaco PNL(1) | WINNERS(disabled)
✅ BonkBot: 1 CAs sent
✅ CLA v2.0: 1 CAs sent
✅ Monaco PNL: 1 CAs sent
```

### Validation 5: Database Operations

**Verify database is working**:
```bash
# Check database tables and data
python3.9 -c "
import sqlite3
import json

conn = sqlite3.connect('/home/<USER>/clav2/data/cla_bot.db')

# Check tables exist
tables = conn.execute(\"SELECT name FROM sqlite_master WHERE type='table'\").fetchall()
print('Database tables:', [t[0] for t in tables])

# Check recent CAs
recent_cas = conn.execute('SELECT ca, first_seen FROM contract_addresses ORDER BY first_seen DESC LIMIT 5').fetchall()
print('Recent CAs:', len(recent_cas))

conn.close()
print('✅ Database validation completed')
"
```

### Validation 6: Performance Metrics

**Check bot performance**:
```bash
# Monitor performance statistics
tail -f ~/clav2/logs/cla_bot.log | grep -E "📊|Statistics|SLOW|Performance"
```

**Expected periodic stats**:
```
📊 Bot Statistics - Uptime: 0:15:30
   Messages processed: 45
   CAs detected: 12
   CAs sent to BonkBot: 8
   CAs sent to CLA v2.0: 8
   CAs sent to Monaco PNL: 8
```

## 🔧 Troubleshooting Guide

### Common Issue 1: Import Errors

**Problem**: Module import failures
```
ModuleNotFoundError: No module named 'telethon'
```

**Solution**:
```bash
# Reinstall dependencies
cd ~/clav2
pip3.9 install --user --upgrade -r requirements.txt

# Check Python path
python3.9 -c "import sys; print(sys.path)"
```

### Common Issue 2: Authentication Failures

**Problem**: Telegram authentication errors
```
SessionPasswordNeededError: Two-step verification is enabled
```

**Solution**:
```bash
# Re-run authentication with 2FA
cd ~/clav2
rm cla_bot_session.session*
python3.9 authenticate_telegram.py
# Enter 2FA password when prompted
```

### Common Issue 3: Database Lock Errors

**Problem**: Database is locked
```
sqlite3.OperationalError: database is locked
```

**Solution**:
```bash
# Kill any running bot processes
pkill -f main.py

# Remove lock files
cd ~/clav2/data
rm -f cla_bot.db-wal cla_bot.db-shm

# Restart bot
cd ~/clav2
python3.9 main.py
```

### Common Issue 4: Network Connectivity

**Problem**: Connection timeouts or network errors
```
OSError: [Errno 101] Network is unreachable
```

**Solution**:
```bash
# Test network connectivity
ping telegram.org

# Check PythonAnywhere network status
# Visit: https://status.pythonanywhere.com/

# Restart bot with retry logic (built-in)
cd ~/clav2
python3.9 main.py
```

### Common Issue 5: File Permission Errors

**Problem**: Permission denied errors
```
PermissionError: [Errno 13] Permission denied: './data/cla_bot.db'
```

**Solution**:
```bash
# Fix permissions
cd ~/clav2
chmod -R 755 data/ logs/
chmod 644 data/*.db
```

### Common Issue 6: Memory Issues

**Problem**: Bot crashes due to memory limits
```
MemoryError: Unable to allocate memory
```

**Solution**:
```bash
# Monitor memory usage
ps aux | grep main.py

# Optimize cache settings in .env
echo "MESSAGE_CACHE_SIZE=5000" >> .env
echo "CA_CACHE_EXPIRY_HOURS=72" >> .env

# Restart bot
python3.9 main.py
```

## 📈 Performance Optimization for PythonAnywhere

### Memory Optimization

**Optimize .env settings**:
```bash
# Reduce cache sizes for limited memory
MESSAGE_CACHE_SIZE=5000
CA_CACHE_EXPIRY_HOURS=72
DATABASE_BACKUP_INTERVAL=7200
```

### CPU Optimization

**Monitor CPU usage**:
```bash
# Check CPU usage
top -p $(pgrep -f main.py)

# If high CPU usage, check for infinite loops in logs
grep -E "ERROR|Exception" ~/clav2/logs/error.log
```

### Disk Space Management

**Monitor disk usage**:
```bash
# Check disk usage
du -h ~/clav2/

# Clean old logs (keep last 7 days)
find ~/clav2/logs/ -name "*.log" -mtime +7 -delete

# Vacuum database periodically
python3.9 -c "
import sqlite3
conn = sqlite3.connect('/home/<USER>/clav2/data/cla_bot.db')
conn.execute('VACUUM')
conn.close()
print('Database vacuumed')
"
```

## 🔄 Maintenance and Updates

### Regular Maintenance Tasks

**Weekly maintenance script** (`maintenance.sh`):
```bash
#!/bin/bash
cd /home/<USER>/clav2

echo "Starting weekly maintenance..."

# Clean old logs
find logs/ -name "*.log" -mtime +7 -delete

# Vacuum database
python3.9 -c "
import sqlite3
conn = sqlite3.connect('data/cla_bot.db')
conn.execute('VACUUM')
conn.close()
"

# Check disk usage
du -h .

echo "Maintenance completed"
```

### Update Deployment

**To update bot with new changes**:
```bash
# Stop bot
pkill -f main.py

# Pull latest changes
cd ~/clav2
git pull origin master

# Install any new dependencies
pip3.9 install --user -r requirements.txt

# Restart bot
python3.9 main.py
```

## 📞 Support and Monitoring

### Health Check Script

**Create health check** (`health_check.py`):
```python
#!/usr/bin/env python3.9
import subprocess
import sqlite3
import os
import time

def health_check():
    print(f"Health Check - {time.strftime('%Y-%m-%d %H:%M:%S')}")

    # Check if bot is running
    result = subprocess.run(['pgrep', '-f', 'main.py'], capture_output=True)
    if result.returncode == 0:
        print("✅ Bot process is running")
    else:
        print("❌ Bot process is not running")
        return False

    # Check database
    try:
        conn = sqlite3.connect('/home/<USER>/clav2/data/cla_bot.db')
        cursor = conn.execute('SELECT COUNT(*) FROM contract_addresses')
        count = cursor.fetchone()[0]
        conn.close()
        print(f"✅ Database accessible ({count} CAs)")
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

    # Check log file
    log_file = '/home/<USER>/clav2/logs/cla_bot.log'
    if os.path.exists(log_file):
        size = os.path.getsize(log_file)
        print(f"✅ Log file exists ({size} bytes)")
    else:
        print("❌ Log file missing")
        return False

    print("✅ All health checks passed")
    return True

if __name__ == "__main__":
    health_check()
```

### Emergency Recovery

**If bot stops responding**:
```bash
# Emergency restart procedure
pkill -f main.py
cd ~/clav2
rm -f data/cla_bot.db-wal data/cla_bot.db-shm
python3.9 main.py
```

This comprehensive deployment guide provides everything needed to successfully deploy and maintain the CLA v2 Telegram bot on PythonAnywhere hosting platform.
