#!/usr/bin/env python3
"""
Local testing script for CLA v2.0 Telegram Bot
Tests configuration, database, and basic functionality before deployment
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_configuration():
    """Test configuration loading"""
    print("🔍 Testing configuration...")
    try:
        from config import config
        print("✅ Configuration loaded successfully")
        print(f"📊 High-volume groups: {len(config.trending.high_volume_groups)}")
        print(f"📊 Low-volume groups: {len(config.trending.low_volume_groups)}")
        print(f"📊 Active groups: {len(config.target_group.active_group_ids)}")
        print(f"🔧 Trending enabled: {config.trending.enabled}")
        print(f"🔧 Database path: {config.database.path}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

async def test_database():
    """Test database initialization"""
    print("\n🔍 Testing database...")
    try:
        from src.database import DatabaseManager
        db = DatabaseManager()
        await db.initialize()
        print("✅ Database initialized successfully")

        # Test a simple query using the connection directly
        cursor = await db.connection.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = await cursor.fetchall()
        print(f"📊 Database tables: {len(tables)}")
        for table in tables:
            print(f"   - {table[0]}")

        await db.close()
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

async def test_imports():
    """Test all critical imports"""
    print("\n🔍 Testing imports...")
    try:
        from src.bot import CLABot
        from src.telegram_client import TelegramClientManager
        from src.message_processor import MessageProcessor
        from src.ca_detector import CADetector
        from src.trending_analyzer import TrendingAnalyzer
        from src.forwarding_manager import ForwardingManager
        print("✅ All critical imports successful")
        return True
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

async def test_telegram_connection():
    """Test Telegram API connection (without full bot startup)"""
    print("\n🔍 Testing Telegram connection...")
    try:
        from src.telegram_client import TelegramClientManager
        from config import config

        client_manager = TelegramClientManager()
        await client_manager.initialize()
        await client_manager.connect()

        # Test connection
        me = await client_manager.get_me()
        if me:
            print(f"✅ Telegram connection successful")
            print(f"📱 Connected as: {me.first_name} ({me.phone})")
        else:
            print("⚠️ Telegram connection established but user info not available")

        await client_manager.disconnect()
        return True
    except Exception as e:
        print(f"❌ Telegram connection test failed: {e}")
        print("   This might be normal if you haven't authenticated yet")
        return False

async def test_health_check():
    """Test health check functionality"""
    print("\n🔍 Testing health check...")
    try:
        # Try to import health check module
        import src.health_check
        print("✅ Health check module can be imported")

        # Try to create health check server (may fail due to missing psutil)
        try:
            from src.health_check import HealthCheckServer
            health_server = HealthCheckServer()
            print("✅ Health check server can be created")
        except ImportError as ie:
            print(f"⚠️ Health check server creation failed (missing dependency): {ie}")
            print("   This is expected if psutil is not installed")

        return True
    except Exception as e:
        print(f"❌ Health check test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 CLA v2.0 Bot - Local Testing Suite")
    print("=" * 50)

    tests = [
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("Imports", test_imports),
        ("Health Check", test_health_check),
        ("Telegram Connection", test_telegram_connection),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False

    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)

    passed = 0
    total = len(tests)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Bot is ready for deployment.")
        return True
    else:
        print("⚠️ Some tests failed. Please fix issues before deployment.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)