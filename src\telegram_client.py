"""Telegram client management for CLA v2.0 Bot."""

import asyncio
from typing import Optional, Callable, Any
from telethon import TelegramClient, events
from telethon.errors import (
    SessionPasswordNeededError, 
    PhoneCodeInvalidError,
    PhoneNumberInvalidError,
    FloodWaitError
)
from telethon.tl.types import User, Chat, Channel
from loguru import logger
from asyncio_throttle import Throttler

from config import config

class TelegramClientManager:
    """Manages Telegram client connection and operations."""
    
    def __init__(self):
        self.client: Optional[TelegramClient] = None
        self.connected = False
        self.message_handler: Optional[Callable] = None
        self.throttler = Throttler(rate_limit=config.rate_limit.api_rate_limit)
    
    async def initialize(self):
        """Initialize Telegram client."""
        try:
            # Create client
            self.client = TelegramClient(
                config.telegram.session_name,
                config.telegram.api_id,
                config.telegram.api_hash
            )
            
            logger.info("Telegram client created")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create Telegram client: {e}")
            return False
    
    async def connect(self):
        """Connect to Telegram."""
        if not self.client:
            logger.error("Client not initialized")
            return False
        
        try:
            await self.client.connect()
            
            # Check if already authorized
            if not await self.client.is_user_authorized():
                await self._authenticate()
            
            # Verify connection
            me = await self.client.get_me()
            logger.info(f"Connected as: {me.first_name} (@{me.username})")
            
            self.connected = True
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to Telegram: {e}")
            return False
    
    async def _authenticate(self):
        """Handle user authentication."""
        try:
            # Send code request
            await self.client.send_code_request(config.telegram.phone)
            logger.info(f"Code sent to {config.telegram.phone}")
            
            # Get code from user input
            code = input("Enter the code you received: ")
            
            try:
                await self.client.sign_in(config.telegram.phone, code)
                logger.info("Successfully authenticated")
                
            except SessionPasswordNeededError:
                # 2FA is enabled
                password = input("Enter your 2FA password: ")
                await self.client.sign_in(password=password)
                logger.info("Successfully authenticated with 2FA")
                
        except PhoneCodeInvalidError:
            logger.error("Invalid phone code")
            raise
        except PhoneNumberInvalidError:
            logger.error("Invalid phone number")
            raise
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise
    
    async def setup_message_handler(self, handler: Callable):
        """Setup message event handler."""
        if not self.client:
            logger.error("Client not initialized")
            return False

        try:
            self.message_handler = handler

            # Get active group IDs to monitor (only ACTIVE groups)
            active_group_ids = config.target_group.active_group_ids

            # Add event handler for new messages in active target groups only
            @self.client.on(events.NewMessage(chats=active_group_ids))
            async def message_handler_wrapper(event):
                """Message handler wrapper with error handling and throttling."""
                try:
                    # Log that handler was called (for debugging)
                    logger.debug(f"🔧 MESSAGE HANDLER WRAPPER CALLED: Chat={event.chat_id}, Message ID={event.message.id}")

                    # Apply throttling
                    async with self.throttler:
                        # Call the actual message handler
                        await self.message_handler(event)

                except FloodWaitError as e:
                    logger.warning(f"Rate limited, waiting {e.seconds} seconds")
                    await asyncio.sleep(e.seconds)
                except Exception as e:
                    logger.error(f"Error in message handler wrapper: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")

            logger.info(f"Message handler setup for {len(active_group_ids)} active groups:")
            logger.info(f"🔧 Event handler registered for group IDs: {active_group_ids}")
            logger.info(f"  - {config.target_group.group_name} ({config.target_group.group_id}) [ACTIVE]")
            for i, group_id in enumerate(config.target_group.additional_group_ids):
                group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {group_id}"
                status = config.target_group.group_status.get(group_id, 'UNKNOWN')
                if status == 'ACTIVE':
                    logger.info(f"  - {group_name} ({group_id}) [ACTIVE]")
                else:
                    logger.info(f"  - {group_name} ({group_id}) [PAUSED - NOT MONITORING]")

            return True

        except Exception as e:
            logger.error(f"Failed to setup message handler: {e}")
            return False
    
    async def send_message(self, entity: Any, message: str) -> bool:
        """Send a message to an entity."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return False

        try:
            async with self.throttler:
                await self.client.send_message(entity, message)
            logger.debug(f"Message sent to {entity}")
            return True

        except FloodWaitError as e:
            logger.warning(f"Rate limited, waiting {e.seconds} seconds")
            await asyncio.sleep(e.seconds)
            return await self.send_message(entity, message)
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            return False

    async def send_message_as_admin(self, entity: Any, message: str) -> bool:
        """Send a message to an entity with admin privileges (if available)."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return False

        try:
            # For now, this is the same as regular send_message
            # The admin identity is determined by the authenticated user session
            # If the user has admin permissions, messages will appear as from that admin
            async with self.throttler:
                await self.client.send_message(entity, message)
            logger.debug(f"Message sent to {entity} with admin identity")
            return True

        except FloodWaitError as e:
            logger.warning(f"Rate limited, waiting {e.seconds} seconds")
            await asyncio.sleep(e.seconds)
            return await self.send_message_as_admin(entity, message)
        except Exception as e:
            logger.error(f"Failed to send message as admin: {e}")
            return False

    async def get_permissions(self, entity: Any, user_id: int):
        """Get user permissions in a chat/channel."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return None

        try:
            return await self.client.get_permissions(entity, user_id)
        except Exception as e:
            logger.error(f"Failed to get permissions: {e}")
            return None

    async def get_me(self):
        """Get current user information."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return None

        try:
            return await self.client.get_me()
        except Exception as e:
            logger.error(f"Failed to get current user: {e}")
            return None
    
    async def get_entity(self, identifier: Any):
        """Get entity by identifier."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return None
        
        try:
            async with self.throttler:
                entity = await self.client.get_entity(identifier)
            return entity
            
        except Exception as e:
            logger.error(f"Failed to get entity {identifier}: {e}")
            return None
    
    async def join_group(self, group_id: int) -> bool:
        """Join a group by ID."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return False
        
        try:
            async with self.throttler:
                await self.client.get_entity(group_id)
            logger.info(f"Successfully accessed group {group_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to access group {group_id}: {e}")
            return False
    
    async def start_listening(self):
        """Start listening for messages."""
        if not self.client or not self.connected:
            logger.error("Client not connected")
            return False

        try:
            logger.info("Starting message listener...")
            # Don't use run_until_disconnected() as it blocks the bot
            # The message handler is already set up and will receive events
            logger.info("Message listener is now active and ready to receive messages")
            return True

        except Exception as e:
            logger.error(f"Error in message listener: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from Telegram."""
        if self.client and self.connected:
            try:
                await self.client.disconnect()
                self.connected = False
                logger.info("Disconnected from Telegram")
            except Exception as e:
                logger.error(f"Error disconnecting: {e}")
    
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self.connected and self.client is not None
