#!/usr/bin/env python3
"""
Test performance optimizations for the Telegram bot
"""
import sys
import os
import time
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_ca_detector_performance():
    """Test CA detector performance improvements."""
    print("🔍 TESTING CA DETECTOR PERFORMANCE...")
    
    try:
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        ca_detector = CADetector(db_manager)
        
        # Test message with multiple CAs
        test_message = """
        Check out these tokens:
        8aWzZb5kZfF4CxnESSyVvs4k9XwH3SbbJovYnDLZL12e
        67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
        9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM
        """
        
        # Test performance
        start_time = time.perf_counter()
        result = await ca_detector.process_message(test_message, 12345, -1002202241417)
        processing_time = (time.perf_counter() - start_time) * 1000
        
        print(f"✅ CA Detection Performance: {processing_time:.1f}ms for {len(result)} CAs")
        
        if processing_time < 100:
            print("🚀 EXCELLENT: Processing time under 100ms")
        elif processing_time < 150:
            print("✅ GOOD: Processing time under 150ms")
        else:
            print("⚠️ SLOW: Processing time over 150ms")
        
        await db_manager.close()
        return processing_time < 150
        
    except Exception as e:
        print(f"❌ CA detector performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_optimizations():
    """Test database optimization settings."""
    print("\n🔍 TESTING DATABASE OPTIMIZATIONS...")
    
    try:
        from src.database import DatabaseManager
        
        # Initialize database with optimizations
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Test database settings
        cursor = await db_manager.connection.execute("PRAGMA journal_mode")
        journal_mode = (await cursor.fetchone())[0]
        
        cursor = await db_manager.connection.execute("PRAGMA cache_size")
        cache_size = (await cursor.fetchone())[0]
        
        cursor = await db_manager.connection.execute("PRAGMA synchronous")
        synchronous = (await cursor.fetchone())[0]
        
        print(f"✅ Database Optimizations Applied:")
        print(f"  - Journal Mode: {journal_mode}")
        print(f"  - Cache Size: {cache_size}")
        print(f"  - Synchronous: {synchronous}")
        
        # Test batch operations
        test_cas = [
            f"Test{i:040d}CA{i:010d}" for i in range(10)
        ]
        
        start_time = time.perf_counter()
        for i, ca in enumerate(test_cas):
            await db_manager.add_contract_address(ca, 1000 + i, -1002202241417)
        batch_time = (time.perf_counter() - start_time) * 1000
        
        print(f"✅ Batch Insert Performance: {batch_time:.1f}ms for {len(test_cas)} CAs")
        print(f"  - Average per CA: {batch_time/len(test_cas):.1f}ms")
        
        await db_manager.close()
        
        return journal_mode == "wal" and batch_time < 500
        
    except Exception as e:
        print(f"❌ Database optimization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_thresholds():
    """Test performance threshold adjustments."""
    print("\n🔍 TESTING PERFORMANCE THRESHOLDS...")
    
    try:
        # Simulate different processing times
        test_times = [25, 60, 80, 120, 180]
        
        for duration_ms in test_times:
            if duration_ms > 150:
                level = "SLOW"
            elif duration_ms > 75:
                level = "MEDIUM"
            elif duration_ms > 50:
                level = "NORMAL"
            else:
                level = "FAST"
            
            print(f"  {duration_ms}ms → {level} processing")
        
        print("✅ Performance thresholds configured correctly")
        return True
        
    except Exception as e:
        print(f"❌ Performance threshold test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚨 PERFORMANCE OPTIMIZATION TESTS")
    print("=" * 50)
    
    success = True
    
    success &= await test_ca_detector_performance()
    success &= await test_database_optimizations()
    success &= test_performance_thresholds()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL PERFORMANCE TESTS PASSED")
        print("🚀 Performance optimizations are working correctly!")
    else:
        print("❌ SOME PERFORMANCE TESTS FAILED")
        print("⚠️ Check the errors above for issues")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
