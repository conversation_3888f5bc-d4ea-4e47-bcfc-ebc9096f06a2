"""Health check endpoint for CLA v2.0 Bot production monitoring."""

import os
import json
import asyncio
import time
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from pathlib import Path
from aiohttp import web, ClientSession
from loguru import logger

class HealthChecker:
    """Comprehensive health check system for production monitoring."""
    
    def __init__(self, bot_instance=None):
        self.bot = bot_instance
        self.start_time = datetime.now()
        self.last_health_check = None
        self.health_status = "unknown"
        
        # Configuration
        self.enabled = os.getenv('HEALTH_CHECK_ENABLED', 'true').lower() == 'true'
        self.port = int(os.getenv('HEALTH_CHECK_PORT', '8080'))
        self.host = os.getenv('HEALTH_CHECK_HOST', '127.0.0.1')
        
        # Health check thresholds
        self.max_memory_mb = int(os.getenv('MAX_MEMORY_MB', '2048'))
        self.max_cpu_percent = float(os.getenv('MAX_CPU_PERCENT', '80'))
        self.max_disk_usage_percent = float(os.getenv('MAX_DISK_USAGE_PERCENT', '85'))
        
        # Performance thresholds
        self.max_processing_time_ms = 100  # Alert if processing > 100ms
        self.max_error_rate_percent = 5    # Alert if error rate > 5%
        
        self.app = None
        self.runner = None
        self.site = None
    
    async def start_health_server(self):
        """Start the health check HTTP server."""
        if not self.enabled:
            logger.info("Health check server disabled")
            return
        
        try:
            self.app = web.Application()
            self.app.router.add_get('/health', self.health_endpoint)
            self.app.router.add_get('/health/detailed', self.detailed_health_endpoint)
            self.app.router.add_get('/metrics', self.metrics_endpoint)
            self.app.router.add_get('/status', self.status_endpoint)
            
            self.runner = web.AppRunner(self.app)
            await self.runner.setup()
            
            self.site = web.TCPSite(self.runner, self.host, self.port)
            await self.site.start()
            
            logger.info(f"🏥 Health check server started on http://{self.host}:{self.port}")
            logger.info(f"   📊 Endpoints: /health, /health/detailed, /metrics, /status")
            
        except Exception as e:
            logger.error(f"Failed to start health check server: {e}")
    
    async def stop_health_server(self):
        """Stop the health check HTTP server."""
        try:
            if self.site:
                await self.site.stop()
            if self.runner:
                await self.runner.cleanup()
            logger.info("🏥 Health check server stopped")
        except Exception as e:
            logger.error(f"Error stopping health check server: {e}")
    
    async def health_endpoint(self, request):
        """Basic health check endpoint - returns 200 if healthy."""
        try:
            health_data = await self.get_basic_health()
            
            if health_data['status'] == 'healthy':
                return web.json_response(health_data, status=200)
            else:
                return web.json_response(health_data, status=503)
                
        except Exception as e:
            logger.error(f"Health check endpoint error: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)
    
    async def detailed_health_endpoint(self, request):
        """Detailed health check with comprehensive system information."""
        try:
            health_data = await self.get_detailed_health()
            return web.json_response(health_data, status=200)
            
        except Exception as e:
            logger.error(f"Detailed health check error: {e}")
            return web.json_response({
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }, status=500)
    
    async def metrics_endpoint(self, request):
        """Prometheus-style metrics endpoint."""
        try:
            metrics = await self.get_prometheus_metrics()
            return web.Response(text=metrics, content_type='text/plain')
            
        except Exception as e:
            logger.error(f"Metrics endpoint error: {e}")
            return web.Response(text=f"# Error generating metrics: {e}", status=500)
    
    async def status_endpoint(self, request):
        """Simple status endpoint for load balancers."""
        try:
            if self.bot and hasattr(self.bot, 'running') and self.bot.running:
                return web.Response(text="OK", status=200)
            else:
                return web.Response(text="NOT_READY", status=503)
                
        except Exception as e:
            return web.Response(text="ERROR", status=500)
    
    async def get_basic_health(self) -> Dict[str, Any]:
        """Get basic health status."""
        try:
            # System resource checks
            memory_usage = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk_usage = psutil.disk_usage('/')
            
            # Bot status checks
            bot_running = self.bot and hasattr(self.bot, 'running') and self.bot.running
            
            # Determine overall health
            issues = []
            
            if memory_usage.percent > 90:
                issues.append(f"High memory usage: {memory_usage.percent:.1f}%")
            
            if cpu_percent > self.max_cpu_percent:
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            
            if disk_usage.percent > self.max_disk_usage_percent:
                issues.append(f"High disk usage: {disk_usage.percent:.1f}%")
            
            if not bot_running:
                issues.append("Bot is not running")
            
            status = "healthy" if not issues else "unhealthy"
            
            health_data = {
                'status': status,
                'timestamp': datetime.now().isoformat(),
                'uptime_seconds': (datetime.now() - self.start_time).total_seconds(),
                'bot_running': bot_running,
                'memory_percent': memory_usage.percent,
                'cpu_percent': cpu_percent,
                'disk_percent': disk_usage.percent,
                'issues': issues
            }
            
            self.last_health_check = datetime.now()
            self.health_status = status
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting basic health: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_detailed_health(self) -> Dict[str, Any]:
        """Get detailed health information."""
        try:
            basic_health = await self.get_basic_health()
            
            # Additional detailed information
            process = psutil.Process()
            
            detailed_info = {
                **basic_health,
                'system': {
                    'platform': psutil.LINUX if hasattr(psutil, 'LINUX') else 'unknown',
                    'cpu_count': psutil.cpu_count(),
                    'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                    'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
                    'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else None
                },
                'process': {
                    'pid': process.pid,
                    'memory_mb': process.memory_info().rss / (1024**2),
                    'cpu_percent': process.cpu_percent(),
                    'num_threads': process.num_threads(),
                    'open_files': len(process.open_files()),
                    'connections': len(process.connections())
                },
                'bot_stats': await self._get_bot_statistics(),
                'database': await self._check_database_health(),
                'integrations': await self._check_integration_health()
            }
            
            return detailed_info
            
        except Exception as e:
            logger.error(f"Error getting detailed health: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def _get_bot_statistics(self) -> Dict[str, Any]:
        """Get bot-specific statistics."""
        try:
            if not self.bot:
                return {'available': False}
            
            stats = getattr(self.bot, 'stats', {})
            return {
                'available': True,
                'messages_processed': stats.get('messages_processed', 0),
                'cas_detected': stats.get('cas_detected', 0),
                'cas_sent_to_bonkbot': stats.get('cas_sent_to_bonkbot', 0),
                'cas_sent_to_cla_v2': stats.get('cas_sent_to_cla_v2', 0),
                'cas_sent_to_monaco_pnl': stats.get('cas_sent_to_monaco_pnl', 0),
                'start_time': stats.get('start_time', self.start_time).isoformat() if stats.get('start_time') else None
            }
        except Exception as e:
            logger.error(f"Error getting bot statistics: {e}")
            return {'available': False, 'error': str(e)}
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and health."""
        try:
            if not self.bot or not hasattr(self.bot, 'db_manager'):
                return {'available': False}
            
            db_manager = self.bot.db_manager
            
            # Simple connectivity test
            start_time = time.perf_counter()
            # Perform a simple query to test connectivity
            # This would need to be implemented based on your database manager
            query_time = (time.perf_counter() - start_time) * 1000
            
            db_path = os.getenv('DATABASE_PATH', './data/cla_bot.db')
            db_file = Path(db_path)
            
            return {
                'available': True,
                'query_time_ms': query_time,
                'file_exists': db_file.exists(),
                'file_size_mb': db_file.stat().st_size / (1024**2) if db_file.exists() else 0,
                'last_modified': datetime.fromtimestamp(db_file.stat().st_mtime).isoformat() if db_file.exists() else None
            }
            
        except Exception as e:
            logger.error(f"Error checking database health: {e}")
            return {'available': False, 'error': str(e)}
    
    async def _check_integration_health(self) -> Dict[str, Any]:
        """Check integration health status."""
        try:
            if not self.bot:
                return {'available': False}
            
            integrations = {
                'bonkbot': getattr(self.bot, 'bonkbot_integration', None) is not None,
                'cla_v2': getattr(self.bot, 'cla_v2_integration', None) is not None,
                'monaco_pnl': getattr(self.bot, 'monaco_pnl_integration', None) is not None,
                'telegram_client': getattr(self.bot, 'telegram_client', None) is not None
            }
            
            return {
                'available': True,
                'integrations': integrations,
                'active_count': sum(integrations.values())
            }
            
        except Exception as e:
            logger.error(f"Error checking integration health: {e}")
            return {'available': False, 'error': str(e)}
    
    async def get_prometheus_metrics(self) -> str:
        """Generate Prometheus-style metrics."""
        try:
            health_data = await self.get_detailed_health()
            
            metrics = []
            
            # System metrics
            metrics.append(f"cla_bot_memory_percent {health_data.get('memory_percent', 0)}")
            metrics.append(f"cla_bot_cpu_percent {health_data.get('cpu_percent', 0)}")
            metrics.append(f"cla_bot_disk_percent {health_data.get('disk_percent', 0)}")
            metrics.append(f"cla_bot_uptime_seconds {health_data.get('uptime_seconds', 0)}")
            
            # Bot metrics
            bot_stats = health_data.get('bot_stats', {})
            if bot_stats.get('available'):
                metrics.append(f"cla_bot_messages_processed {bot_stats.get('messages_processed', 0)}")
                metrics.append(f"cla_bot_cas_detected {bot_stats.get('cas_detected', 0)}")
                metrics.append(f"cla_bot_cas_sent_bonkbot {bot_stats.get('cas_sent_to_bonkbot', 0)}")
                metrics.append(f"cla_bot_cas_sent_cla_v2 {bot_stats.get('cas_sent_to_cla_v2', 0)}")
                metrics.append(f"cla_bot_cas_sent_monaco_pnl {bot_stats.get('cas_sent_to_monaco_pnl', 0)}")
            
            # Health status (1 = healthy, 0 = unhealthy)
            health_status = 1 if health_data.get('status') == 'healthy' else 0
            metrics.append(f"cla_bot_health_status {health_status}")
            
            return '\n'.join(metrics) + '\n'
            
        except Exception as e:
            logger.error(f"Error generating Prometheus metrics: {e}")
            return f"# Error generating metrics: {e}\n"

# Global health checker instance
health_checker = None

async def start_health_check_server(bot_instance=None):
    """Start the health check server."""
    global health_checker
    health_checker = HealthChecker(bot_instance)
    await health_checker.start_health_server()

async def stop_health_check_server():
    """Stop the health check server."""
    global health_checker
    if health_checker:
        await health_checker.stop_health_server()

if __name__ == "__main__":
    # Test health check server
    async def test_health_server():
        await start_health_check_server()
        print(f"Health check server running on http://127.0.0.1:8080")
        print("Test endpoints:")
        print("  - http://127.0.0.1:8080/health")
        print("  - http://127.0.0.1:8080/health/detailed")
        print("  - http://127.0.0.1:8080/metrics")
        print("  - http://127.0.0.1:8080/status")
        
        # Keep running for testing
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            await stop_health_check_server()
    
    asyncio.run(test_health_server())
