#!/usr/bin/env python3
"""
Comprehensive Solana Contract Address Validation Audit
Verifies all CA validation logic across the entire codebase
"""

import sys
import re
import asyncio
sys.path.append('.')

def verify_standard_ca_format():
    """Verify standard Solana CA format compliance."""
    print("🔍 STANDARD SOLANA CA FORMAT VERIFICATION")
    print("=" * 60)
    
    # Test CAs from different sources
    test_cas = {
        "BUGSIE CA": "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
        "Wrapped SOL": "So111111111********************************",
        "Token Program": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
        "System Program": "********************************",
        "USDC Token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        "Raydium AMM": "675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8",
    }
    
    print("Testing known Solana addresses:")
    print()
    
    for name, ca in test_cas.items():
        print(f"{name}:")
        print(f"  Address: {ca}")
        print(f"  Length: {len(ca)} characters")
        
        # Test base58 validation
        try:
            import base58
            decoded = base58.b58decode(ca)
            print(f"  Base58: ✅ Valid ({len(decoded)} bytes)")
            
            if len(decoded) == 32:
                print(f"  Format: ✅ Standard 32-byte public key")
            else:
                print(f"  Format: ⚠️ Non-standard ({len(decoded)} bytes)")
                
            if decoded != b'\x00' * 32:
                print(f"  Content: ✅ Not all zeros")
            else:
                print(f"  Content: ❌ All zeros (invalid)")
                
        except Exception as e:
            print(f"  Base58: ❌ Invalid - {e}")
        
        print()
    
    # Verify BUGSIE CA specifically
    bugsie_ca = test_cas["BUGSIE CA"]
    print(f"🎯 BUGSIE CA SPECIFIC VERIFICATION:")
    print(f"Address: {bugsie_ca}")
    print(f"Length: {len(bugsie_ca)} characters")
    print(f"Expected: 44 characters (standard)")
    print(f"Compliance: {'✅ COMPLIANT' if len(bugsie_ca) == 44 else '❌ NON-COMPLIANT'}")
    
    return len(bugsie_ca) == 44

def audit_ca_validation_logic():
    """Audit all CA validation logic in the codebase."""
    print("\n🔍 CA VALIDATION LOGIC AUDIT")
    print("=" * 60)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test the validation method
        test_cas = [
            "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",  # BUGSIE CA
            "So111111111********************************",    # Wrapped SOL
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",    # Token Program
            "invalid_ca_too_short",                            # Invalid (too short)
            "this_ca_is_way_too_long_to_be_valid_solana_address", # Invalid (too long)
            "********************************",                # System Program (32 chars)
        ]
        
        print("Testing CA validation method:")
        print()
        
        for ca in test_cas:
            result = parser._validate_solana_ca(ca)
            print(f"CA: {ca[:30]}{'...' if len(ca) > 30 else ''}")
            print(f"Length: {len(ca)} chars")
            print(f"Result: {'✅ VALID' if result else '❌ INVALID'}")
            print()
        
        # Test enhanced DeFi validation
        print("Testing enhanced DeFi validation:")
        
        defi_test_cas = [
            "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups",  # DeFi CA with 'jups'
            "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",  # BUGSIE CA with 'bonk'
        ]
        
        for ca in defi_test_cas:
            result = parser._validate_solana_ca(ca)
            print(f"DeFi CA: {ca}")
            print(f"Result: {'✅ VALID' if result else '❌ INVALID'}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Validation audit failed: {e}")
        return False

def audit_extraction_methods():
    """Audit all CA extraction methods."""
    print("\n🔍 CA EXTRACTION METHODS AUDIT")
    print("=" * 60)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test different message formats
        test_messages = {
            "BUGSIE Activity Tracker": """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472""",
            
            "FREE WHALE SIGNALS": "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)",
            
            "Standalone CA": "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
            
            "URL Format": "Check this token: https://dexscreener.com/solana/GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
            
            "Mixed Format": "Token $BOOP GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk is pumping!"
        }
        
        print("Testing extraction methods on different message formats:")
        print()
        
        for format_name, message in test_messages.items():
            print(f"Format: {format_name}")
            print(f"Message: {message[:60]}{'...' if len(message) > 60 else ''}")
            
            # Test full extraction
            extracted = parser.extract_contract_addresses(message)
            print(f"Extracted: {extracted}")
            
            # Test individual methods
            print("Individual method results:")
            
            # Standalone extraction
            cleaned = parser._clean_message_text_no_url_extraction(message)
            standalone = parser._extract_standalone_cas(cleaned)
            print(f"  Standalone: {standalone}")
            
            # URL extraction
            url_cas = parser._extract_cas_from_urls(message)
            print(f"  URL: {url_cas}")
            
            # Activity Tracker (if applicable)
            if parser._is_activity_tracker_format(message):
                activity = parser._extract_activity_tracker_cas(message)
                print(f"  Activity Tracker: {activity}")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Extraction audit failed: {e}")
        return False

async def audit_full_pipeline():
    """Audit the full CA processing pipeline."""
    print("\n🔍 FULL PIPELINE AUDIT")
    print("=" * 60)
    
    try:
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        db_manager = DatabaseManager()
        await db_manager.initialize()
        ca_detector = CADetector(db_manager)
        
        # Test BUGSIE message through full pipeline
        bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472

📊 Token details
├ PRICE:    $0
├ MC:       $71.5K /3.5X from VIP/"""
        
        print("Testing BUGSIE message through full pipeline:")
        print(f"Message: {bugsie_message[:100]}...")
        print()
        
        # Process through CA detector
        detected_cas = await ca_detector.process_message(
            bugsie_message, 
            4856,  # Message ID
            -1002064145465  # BUGSIE group ID
        )
        
        print(f"Pipeline result: {detected_cas}")
        
        expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
        if expected_ca in detected_cas:
            print("✅ BUGSIE CA successfully detected by full pipeline")
        else:
            print("❌ BUGSIE CA not detected by full pipeline")
        
        await db_manager.close()
        return expected_ca in detected_cas
        
    except Exception as e:
        print(f"❌ Pipeline audit failed: {e}")
        return False

def audit_regex_patterns():
    """Audit all regex patterns for CA extraction."""
    print("\n🔍 REGEX PATTERNS AUDIT")
    print("=" * 60)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        print("Current regex patterns:")
        print(f"Main CA pattern: {parser.ca_pattern.pattern}")
        print()
        
        print("URL patterns:")
        for platform, pattern in parser.url_patterns.items():
            print(f"  {platform}: {pattern.pattern}")
        print()
        
        # Test patterns against BUGSIE CA
        test_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
        
        print(f"Testing patterns against BUGSIE CA: {test_ca}")
        print()
        
        # Test main pattern
        main_matches = parser.ca_pattern.findall(test_ca)
        print(f"Main pattern matches: {main_matches}")
        
        # Test in different contexts
        contexts = [
            f"Standalone: {test_ca}",
            f"├ {test_ca}",
            f"Token {test_ca} here",
            f"https://dexscreener.com/solana/{test_ca}",
        ]
        
        for context in contexts:
            matches = parser.ca_pattern.findall(context)
            print(f"Context '{context[:30]}...': {matches}")
        
        return True
        
    except Exception as e:
        print(f"❌ Regex audit failed: {e}")
        return False

def check_hardcoded_assumptions():
    """Check for hardcoded length assumptions in the codebase."""
    print("\n🔍 HARDCODED ASSUMPTIONS AUDIT")
    print("=" * 60)
    
    # Check message parser for hardcoded lengths
    try:
        from src.message_parser import MessageParser
        import inspect
        
        parser = MessageParser()
        
        # Get source code of key methods
        methods_to_check = [
            '_validate_solana_ca',
            '_extract_standalone_cas', 
            '_extract_activity_tracker_cas'
        ]
        
        print("Checking for hardcoded length assumptions:")
        print()
        
        for method_name in methods_to_check:
            method = getattr(parser, method_name)
            source = inspect.getsource(method)
            
            print(f"Method: {method_name}")
            
            # Look for hardcoded numbers
            hardcoded_numbers = re.findall(r'\b(43|44|32)\b', source)
            if hardcoded_numbers:
                print(f"  Found numbers: {set(hardcoded_numbers)}")
                
                # Check if 44 is used correctly
                if '44' in hardcoded_numbers:
                    if 'len(cleaned_line) == 44' in source:
                        print("  ✅ Uses 44 for exact length check")
                    if 'len(ca) > 44' in source:
                        print("  ✅ Uses 44 for max length check")
            else:
                print("  No hardcoded length numbers found")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Hardcoded assumptions audit failed: {e}")
        return False

async def main():
    """Run comprehensive CA validation audit."""
    print("🚀 COMPREHENSIVE SOLANA CA VALIDATION AUDIT")
    print("=" * 80)
    print("Auditing all CA validation logic across the entire codebase")
    print("=" * 80)
    
    # Run all audit components
    format_compliance = verify_standard_ca_format()
    validation_logic = audit_ca_validation_logic()
    extraction_methods = audit_extraction_methods()
    pipeline_result = await audit_full_pipeline()
    regex_patterns = audit_regex_patterns()
    hardcoded_check = check_hardcoded_assumptions()
    
    print("\n🎯 AUDIT RESULTS SUMMARY")
    print("=" * 80)
    
    results = [
        ("Standard CA Format Compliance", format_compliance),
        ("CA Validation Logic", validation_logic),
        ("CA Extraction Methods", extraction_methods),
        ("Full Pipeline Processing", pipeline_result),
        ("Regex Patterns", regex_patterns),
        ("Hardcoded Assumptions Check", hardcoded_check),
    ]
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall Audit Score: {passed}/{total} components passed")
    
    if passed == total:
        print("\n🎉 AUDIT PASSED!")
        print("✅ All CA validation logic is working correctly")
        print("✅ 44-character Solana CAs should be processed properly")
        print("✅ BUGSIE CA extraction should work as expected")
    else:
        print("\n⚠️ AUDIT ISSUES IDENTIFIED")
        print("Some components need attention - see detailed results above")
    
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
