#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""
import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and report results"""
    try:
        __import__(module_name)
        print(f"✅ {description}: OK")
        return True
    except Exception as e:
        print(f"❌ {description}: FAILED - {e}")
        traceback.print_exc()
        return False

def main():
    print("🔍 Testing CLA Bot imports...")
    print("=" * 50)
    
    success_count = 0
    total_tests = 0
    
    # Test basic imports
    tests = [
        ("config", "Configuration"),
        ("src.bot", "Main Bot"),
        ("src.telegram_client", "Telegram Client"),
        ("src.enhanced_stats_tracker", "Enhanced Stats Tracker"),
        ("src.ca_detector", "CA Detector"),
        ("src.message_processor", "Message Processor"),
        ("src.trending_analyzer", "Trending Analyzer"),
        ("src.forwarding_manager", "Forwarding Manager"),
        ("src.database", "Database"),
        ("src.logger_setup", "Logger Setup"),
    ]
    
    for module, description in tests:
        total_tests += 1
        if test_import(module, description):
            success_count += 1
    
    print("=" * 50)
    print(f"📊 Results: {success_count}/{total_tests} imports successful")
    
    if success_count == total_tests:
        print("🎉 All imports working correctly!")
        return True
    else:
        print("⚠️  Some imports failed - check the errors above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
