#!/usr/bin/env python3
"""
Direct start script for CLA Bot - bypasses potential issues
"""
import asyncio
import logging
import sys
import os
from datetime import datetime

# Configure logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('direct_start.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """Direct bot startup"""
    logger.info("=" * 60)
    logger.info("CLA Bot Direct Start - Enhanced Version")
    logger.info(f"Start time: {datetime.now()}")
    logger.info("=" * 60)
    
    try:
        # Import configuration
        logger.info("Loading configuration...")
        import config
        
        # Check WINNERS status
        excluded = getattr(config.trending, 'exclude_destinations', [])
        winners_excluded = 'WINNERS' in [dest.upper() for dest in excluded]
        
        if winners_excluded:
            logger.error("❌ WINNERS is still excluded in configuration!")
            logger.error(f"Excluded destinations: {excluded}")
            return False
        else:
            logger.info("✅ WINNERS is properly reactivated")
            logger.info(f"Excluded destinations: {excluded}")
        
        # Initialize database
        logger.info("Initializing database...")
        from src.database import initialize as db_init
        db_init()
        logger.info("✅ Database initialized")
        
        # Initialize bot
        logger.info("Initializing CLA Bot...")
        from src.bot import CLABot
        
        bot = CLABot()
        await bot.initialize()
        logger.info("✅ Bot initialized successfully")
        
        # Start bot
        logger.info("Starting bot monitoring...")
        logger.info("🎯 Monitoring for Mark Degens CA detection and WINNERS forwarding")
        await bot.start()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"❌ Bot startup failed: {e}")
        logger.exception("Full error details:")
        return False
    
    return True

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Critical startup error: {e}")
        sys.exit(1)
