"""Unit tests for MessageProcessor component."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.message_processor import MessageProcessor, MessageValidator, MessageRouter
from src.enhanced_stats_tracker import EnhancedStatsTracker

class TestMessageProcessor:
    """Test cases for MessageProcessor."""
    
    @pytest.fixture
    def mock_ca_analyzer(self):
        """Mock CA analyzer."""
        analyzer = Mock()
        analyzer.analyze_message = AsyncMock(return_value={
            'new_cas': ['test_ca_123'],
            'trending_cas': ['test_ca_123'],
            'rescue_cas': [],
            'analysis_time_ms': 50.0
        })
        return analyzer
    
    @pytest.fixture
    def mock_forwarding_manager(self):
        """Mock forwarding manager."""
        manager = Mock()
        manager.forward_trending_cas = AsyncMock(return_value={
            'forwarded_count': 1,
            'destinations': ['BonkBot']
        })
        return manager
    
    @pytest.fixture
    def mock_enhanced_stats(self):
        """Mock enhanced stats tracker."""
        stats = Mock(spec=EnhancedStatsTracker)
        stats.record_message_received = Mock()
        stats.record_performance_timing = Mock()
        return stats
    
    @pytest.fixture
    def message_processor(self, mock_ca_analyzer, mock_forwarding_manager, mock_enhanced_stats):
        """Create MessageProcessor instance with mocked dependencies."""
        return MessageProcessor(mock_ca_analyzer, mock_forwarding_manager, mock_enhanced_stats)
    
    @pytest.fixture
    def mock_event(self):
        """Mock Telegram event."""
        event = Mock()
        event.chat_id = -*************  # FREE WHALE SIGNALS
        event.message = Mock()
        event.message.id = 12345
        event.message.text = "Test message with CA: ABC123DEF456"
        return event
    
    @pytest.mark.asyncio
    async def test_process_message_success(self, message_processor, mock_event, mock_enhanced_stats):
        """Test successful message processing."""
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.get_group_name.return_value = "FREE WHALE SIGNALS"
            mock_group_manager.get_group_type.return_value = "LOW-VOLUME"
            
            result = await message_processor.process_message(mock_event)
            
            assert result is True
            mock_enhanced_stats.record_message_received.assert_called_once_with(-*************)
            mock_enhanced_stats.record_performance_timing.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_empty_message(self, message_processor, mock_event):
        """Test processing of empty message."""
        mock_event.message.text = ""
        
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.get_group_name.return_value = "FREE WHALE SIGNALS"
            mock_group_manager.get_group_type.return_value = "LOW-VOLUME"
            
            result = await message_processor.process_message(mock_event)
            
            assert result is False
            assert message_processor.stats['empty_messages_skipped'] == 1
    
    @pytest.mark.asyncio
    async def test_duplicate_message_filtering(self, message_processor, mock_event):
        """Test duplicate message filtering."""
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.get_group_name.return_value = "FREE WHALE SIGNALS"
            mock_group_manager.get_group_type.return_value = "LOW-VOLUME"
            
            # Process same message twice
            await message_processor.process_message(mock_event)
            result = await message_processor.process_message(mock_event)
            
            assert result is False
            assert message_processor.stats['duplicate_messages_filtered'] == 1
    
    @pytest.mark.asyncio
    async def test_error_handling(self, message_processor, mock_event, mock_ca_analyzer):
        """Test error handling in message processing."""
        mock_ca_analyzer.analyze_message.side_effect = Exception("Test error")
        
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.get_group_name.return_value = "FREE WHALE SIGNALS"
            mock_group_manager.get_group_type.return_value = "LOW-VOLUME"
            
            result = await message_processor.process_message(mock_event)
            
            assert result is False
            assert message_processor.stats['processing_errors'] == 1
    
    def test_get_stats(self, message_processor):
        """Test statistics retrieval."""
        stats = message_processor.get_stats()
        
        assert isinstance(stats, dict)
        assert 'messages_processed' in stats
        assert 'duplicate_messages_filtered' in stats
        assert 'recent_messages_cache_size' in stats

class TestMessageValidator:
    """Test cases for MessageValidator."""
    
    @pytest.fixture
    def validator(self):
        """Create MessageValidator instance."""
        return MessageValidator()
    
    @pytest.mark.asyncio
    async def test_validate_normal_message(self, validator):
        """Test validation of normal message."""
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.is_monitored_group.return_value = True
            
            is_valid, reason = await validator.validate_message("Normal message", -*************)
            
            assert is_valid is True
            assert reason == "Valid"
    
    @pytest.mark.asyncio
    async def test_validate_unmonitored_group(self, validator):
        """Test validation of message from unmonitored group."""
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.is_monitored_group.return_value = False
            
            is_valid, reason = await validator.validate_message("Test message", -999999)
            
            assert is_valid is False
            assert reason == "Group not monitored"
    
    @pytest.mark.asyncio
    async def test_validate_too_long_message(self, validator):
        """Test validation of overly long message."""
        long_message = "x" * 15000
        
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.is_monitored_group.return_value = True
            
            is_valid, reason = await validator.validate_message(long_message, -*************)
            
            assert is_valid is False
            assert reason == "Message too long"
    
    @pytest.mark.asyncio
    async def test_validate_too_many_links(self, validator):
        """Test validation of message with too many links."""
        spam_message = "Check out " + "http://example.com " * 15
        
        with patch('src.message_processor.group_manager') as mock_group_manager:
            mock_group_manager.is_monitored_group.return_value = True
            
            is_valid, reason = await validator.validate_message(spam_message, -*************)
            
            assert is_valid is False
            assert reason == "Too many links"

class TestMessageRouter:
    """Test cases for MessageRouter."""
    
    @pytest.fixture
    def router(self):
        """Create MessageRouter instance."""
        return MessageRouter()
    
    @pytest.mark.asyncio
    async def test_route_high_volume_message(self, router):
        """Test routing of high-volume group message."""
        route = await router.route_message("Test message", -*************, "HIGH-VOLUME")
        
        assert route == "trending_analysis"
        assert router.stats['high_volume_routed'] == 1
    
    @pytest.mark.asyncio
    async def test_route_low_volume_message(self, router):
        """Test routing of low-volume group message."""
        route = await router.route_message("Test message", -*************, "LOW-VOLUME")
        
        assert route == "direct_forwarding"
        assert router.stats['low_volume_routed'] == 1
    
    @pytest.mark.asyncio
    async def test_route_unmonitored_message(self, router):
        """Test routing of unmonitored group message."""
        route = await router.route_message("Test message", -999999, "UNMONITORED")
        
        assert route == "ignore"
    
    def test_get_stats(self, router):
        """Test statistics retrieval."""
        stats = router.get_stats()
        
        assert isinstance(stats, dict)
        assert 'messages_routed' in stats
        assert 'high_volume_routed' in stats
        assert 'low_volume_routed' in stats

if __name__ == "__main__":
    pytest.main([__file__])
