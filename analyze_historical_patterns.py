"""Analyze historical CA detection patterns from logs to evaluate trending filter effectiveness."""

import re
import sys
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple

# Add src to path
sys.path.insert(0, 'src')

def parse_log_timestamp(log_line: str) -> datetime:
    """Parse timestamp from log line."""
    try:
        timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', log_line)
        if timestamp_match:
            return datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
    except:
        pass
    return None

def extract_ca_from_line(line: str) -> str:
    """Extract CA from log line."""
    # Look for CA patterns in various log formats
    ca_patterns = [
        r'CA=([A-Za-z0-9]{40,50})',
        r'New CA detected.*: ([A-Za-z0-9]{40,50})',
        r'CA ([A-Za-z0-9]{40,50})',
        r'([A-Za-z0-9]{40,50}bonk)',  # CAs ending with 'bonk'
    ]
    
    for pattern in ca_patterns:
        match = re.search(pattern, line)
        if match:
            return match.group(1)
    
    return None

def analyze_historical_patterns():
    """Analyze historical CA detection patterns from logs."""
    print("🔍 Historical High-Volume Group Pattern Analysis")
    print("=" * 70)
    
    try:
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Analyzing {len(lines)} log entries...")
        
        # Track high-volume group detections
        gmgn_detections = []  # (timestamp, ca, trending_result, mention_count)
        meme_detections = []  # (timestamp, ca, trending_result, mention_count)
        
        # Parse log lines for high-volume group activity
        for line in lines:
            timestamp = parse_log_timestamp(line)
            if not timestamp:
                continue
            
            # GMGN Featured Signals detections
            if "🧠 GMGN" in line or "GMGN Featured Signals" in line:
                if "HIGH-VOLUME RESULT:" in line:
                    ca = extract_ca_from_line(line)
                    if ca:
                        trending_match = re.search(r'Trending=(\w+)', line)
                        count_match = re.search(r'Count=(\d+)', line)
                        
                        trending = trending_match.group(1) == 'True' if trending_match else False
                        count = int(count_match.group(1)) if count_match else 0
                        
                        gmgn_detections.append((timestamp, ca, trending, count))
            
            # MEME 1000X detections
            elif "🚀 MEME" in line or "MEME 1000X" in line:
                if "HIGH-VOLUME RESULT:" in line:
                    ca = extract_ca_from_line(line)
                    if ca:
                        trending_match = re.search(r'Trending=(\w+)', line)
                        count_match = re.search(r'Count=(\d+)', line)
                        
                        trending = trending_match.group(1) == 'True' if trending_match else False
                        count = int(count_match.group(1)) if count_match else 0
                        
                        meme_detections.append((timestamp, ca, trending, count))
        
        print(f"📈 Found {len(gmgn_detections)} GMGN detections")
        print(f"📈 Found {len(meme_detections)} MEME 1000X detections")
        
        # Analyze cross-group patterns
        analyze_cross_group_patterns(gmgn_detections, meme_detections)
        
        # Analyze trending effectiveness
        analyze_trending_effectiveness(gmgn_detections, meme_detections)
        
        # Analyze timing patterns
        analyze_timing_patterns(gmgn_detections, meme_detections)
        
    except FileNotFoundError:
        print("❌ Log file not found. Make sure the bot has been running.")
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()

def analyze_cross_group_patterns(gmgn_detections: List, meme_detections: List):
    """Analyze cross-group CA appearance patterns."""
    print(f"\n🔄 Cross-Group Pattern Analysis")
    print("-" * 50)
    
    # Group detections by CA
    gmgn_cas = {ca: (timestamp, trending, count) for timestamp, ca, trending, count in gmgn_detections}
    meme_cas = {ca: (timestamp, trending, count) for timestamp, ca, trending, count in meme_detections}
    
    # Find CAs that appeared in both groups
    common_cas = set(gmgn_cas.keys()) & set(meme_cas.keys())
    
    print(f"📊 CAs appearing in both groups: {len(common_cas)}")
    
    if common_cas:
        timing_differences = []
        both_trending = 0
        either_trending = 0
        neither_trending = 0
        
        print(f"\n📋 Cross-Group CA Details:")
        for ca in sorted(common_cas):
            gmgn_time, gmgn_trending, gmgn_count = gmgn_cas[ca]
            meme_time, meme_trending, meme_count = meme_cas[ca]
            
            time_diff = (meme_time - gmgn_time).total_seconds()
            timing_differences.append(time_diff)
            
            if gmgn_trending and meme_trending:
                both_trending += 1
                status = "✅ BOTH TRENDING"
            elif gmgn_trending or meme_trending:
                either_trending += 1
                status = "⚠️ ONE TRENDING"
            else:
                neither_trending += 1
                status = "❌ BOTH FILTERED"
            
            print(f"   {ca[:25]}... | Delay: {time_diff:+6.0f}s | {status}")
            print(f"      GMGN: {gmgn_time.strftime('%H:%M:%S')} ({gmgn_count} mentions)")
            print(f"      MEME: {meme_time.strftime('%H:%M:%S')} ({meme_count} mentions)")
        
        # Timing analysis
        if timing_differences:
            avg_delay = sum(timing_differences) / len(timing_differences)
            print(f"\n⏰ Timing Analysis:")
            print(f"   Average MEME 1000X delay: {avg_delay:.1f} seconds")
            print(f"   Expected delay: 300 seconds (5 minutes)")
            print(f"   Variance: {avg_delay - 300:.1f} seconds")
            
            # Categorize delays
            within_expected = sum(1 for t in timing_differences if 240 <= t <= 360)  # 4-6 minutes
            print(f"   Within expected range (4-6min): {within_expected}/{len(timing_differences)} ({within_expected/len(timing_differences)*100:.1f}%)")
        
        # Trending analysis
        print(f"\n📊 Cross-Group Trending Results:")
        print(f"   Both groups trending: {both_trending}")
        print(f"   One group trending: {either_trending}")
        print(f"   Neither trending: {neither_trending}")
        
        if neither_trending > 0:
            print(f"   ⚠️ Potential missed opportunities: {neither_trending} CAs appeared in both groups but were filtered")

def analyze_trending_effectiveness(gmgn_detections: List, meme_detections: List):
    """Analyze trending filter effectiveness."""
    print(f"\n🎯 Trending Filter Effectiveness")
    print("-" * 50)
    
    all_detections = gmgn_detections + meme_detections
    
    if not all_detections:
        print("No detections found for analysis")
        return
    
    # Analyze trending vs filtered
    trending_qualified = sum(1 for _, _, trending, _ in all_detections if trending)
    noise_filtered = sum(1 for _, _, trending, _ in all_detections if not trending)
    total = len(all_detections)
    
    print(f"📊 Overall Filter Performance:")
    print(f"   Total detections: {total}")
    print(f"   Trending qualified: {trending_qualified} ({trending_qualified/total*100:.1f}%)")
    print(f"   Filtered as noise: {noise_filtered} ({noise_filtered/total*100:.1f}%)")
    
    # Analyze by group
    gmgn_trending = sum(1 for _, _, trending, _ in gmgn_detections if trending)
    gmgn_filtered = sum(1 for _, _, trending, _ in gmgn_detections if not trending)
    
    meme_trending = sum(1 for _, _, trending, _ in meme_detections if trending)
    meme_filtered = sum(1 for _, _, trending, _ in meme_detections if not trending)
    
    print(f"\n📈 Per-Group Performance:")
    if gmgn_detections:
        print(f"   🧠 GMGN Featured Signals:")
        print(f"      Trending: {gmgn_trending}/{len(gmgn_detections)} ({gmgn_trending/len(gmgn_detections)*100:.1f}%)")
        print(f"      Filtered: {gmgn_filtered}/{len(gmgn_detections)} ({gmgn_filtered/len(gmgn_detections)*100:.1f}%)")
    
    if meme_detections:
        print(f"   🚀 MEME 1000X:")
        print(f"      Trending: {meme_trending}/{len(meme_detections)} ({meme_trending/len(meme_detections)*100:.1f}%)")
        print(f"      Filtered: {meme_filtered}/{len(meme_detections)} ({meme_filtered/len(meme_detections)*100:.1f}%)")
    
    # Analyze mention count distribution
    mention_counts = [count for _, _, _, count in all_detections]
    if mention_counts:
        print(f"\n📊 Mention Count Distribution:")
        print(f"   Average mentions: {sum(mention_counts)/len(mention_counts):.1f}")
        print(f"   Max mentions: {max(mention_counts)}")
        print(f"   Min mentions: {min(mention_counts)}")
        
        # Count by mention ranges
        count_1_3 = sum(1 for c in mention_counts if 1 <= c <= 3)
        count_4_5 = sum(1 for c in mention_counts if 4 <= c <= 5)
        count_6_plus = sum(1 for c in mention_counts if c >= 6)
        
        print(f"   1-3 mentions: {count_1_3} ({count_1_3/len(mention_counts)*100:.1f}%)")
        print(f"   4-5 mentions: {count_4_5} ({count_4_5/len(mention_counts)*100:.1f}%)")
        print(f"   6+ mentions: {count_6_plus} ({count_6_plus/len(mention_counts)*100:.1f}%)")

def analyze_timing_patterns(gmgn_detections: List, meme_detections: List):
    """Analyze timing patterns in detections."""
    print(f"\n⏰ Timing Pattern Analysis")
    print("-" * 50)
    
    # Analyze detection frequency over time
    all_detections = [(t, 'GMGN') for t, _, _, _ in gmgn_detections] + [(t, 'MEME') for t, _, _, _ in meme_detections]
    all_detections.sort()
    
    if len(all_detections) < 2:
        print("Insufficient data for timing analysis")
        return
    
    # Calculate time gaps between detections
    time_gaps = []
    for i in range(1, len(all_detections)):
        gap = (all_detections[i][0] - all_detections[i-1][0]).total_seconds()
        time_gaps.append(gap)
    
    if time_gaps:
        avg_gap = sum(time_gaps) / len(time_gaps)
        print(f"📊 Detection Timing:")
        print(f"   Average time between detections: {avg_gap/60:.1f} minutes")
        print(f"   Shortest gap: {min(time_gaps)/60:.1f} minutes")
        print(f"   Longest gap: {max(time_gaps)/60:.1f} minutes")
    
    # Analyze activity by hour
    hourly_activity = defaultdict(int)
    for timestamp, _ in all_detections:
        hour = timestamp.hour
        hourly_activity[hour] += 1
    
    if hourly_activity:
        print(f"\n📈 Activity by Hour:")
        for hour in sorted(hourly_activity.keys()):
            count = hourly_activity[hour]
            bar = "█" * min(count, 20)  # Visual bar
            print(f"   {hour:2d}:00 | {count:2d} detections {bar}")

def main():
    """Main analysis function."""
    print("🔍 Historical High-Volume Group Pattern Analyzer")
    print("Analyzing GMGN Featured Signals and MEME 1000X patterns")
    print("Expected: MEME 1000X messages arrive ~5 minutes after GMGN")
    print()
    
    analyze_historical_patterns()
    
    print(f"\n💡 Analysis Recommendations:")
    print("1. Monitor cross-group CAs that were filtered in both groups")
    print("2. Consider adjusting threshold if many cross-group CAs are filtered")
    print("3. Verify MEME 1000X timing delay matches expectations")
    print("4. Track filtered CAs for potential price movement analysis")

if __name__ == "__main__":
    main()
