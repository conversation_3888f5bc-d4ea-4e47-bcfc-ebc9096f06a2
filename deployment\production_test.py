#!/usr/bin/env python3
"""Production deployment testing script for CLA v2.0 Bot."""

import os
import sys
import json
import time
import asyncio
import aiohttp
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Colors for output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    NC = '\033[0m'  # No Color

class ProductionTester:
    """Comprehensive production deployment testing."""
    
    def __init__(self):
        self.install_dir = Path("/opt/cla-bot")
        self.health_url = "http://127.0.0.1:8080"
        self.metrics_url = "http://127.0.0.1:8081"
        self.test_results = []
        
    def log(self, message: str, color: str = Colors.NC):
        """Log a message with color."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"{color}[{timestamp}] {message}{Colors.NC}")
    
    def success(self, message: str):
        """Log a success message."""
        self.log(f"✅ {message}", Colors.GREEN)
    
    def error(self, message: str):
        """Log an error message."""
        self.log(f"❌ {message}", Colors.RED)
    
    def warning(self, message: str):
        """Log a warning message."""
        self.log(f"⚠️ {message}", Colors.YELLOW)
    
    def info(self, message: str):
        """Log an info message."""
        self.log(f"ℹ️ {message}", Colors.BLUE)
    
    def add_result(self, test_name: str, passed: bool, details: str = ""):
        """Add a test result."""
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    async def run_all_tests(self):
        """Run all production tests."""
        self.log("🚀 Starting CLA v2.0 Bot Production Testing", Colors.CYAN)
        self.log("=" * 60, Colors.CYAN)
        
        # Test categories
        test_categories = [
            ("Environment & Configuration", self.test_environment),
            ("File System & Permissions", self.test_filesystem),
            ("Service & Process", self.test_service),
            ("Health Checks", self.test_health_checks),
            ("Database", self.test_database),
            ("Network & Security", self.test_network),
            ("Performance", self.test_performance),
            ("Backup System", self.test_backup),
            ("Monitoring", self.test_monitoring),
            ("Integration", self.test_integration)
        ]
        
        for category_name, test_func in test_categories:
            self.log(f"\n📋 Testing: {category_name}", Colors.PURPLE)
            self.log("-" * 40, Colors.PURPLE)
            
            try:
                await test_func()
            except Exception as e:
                self.error(f"Test category failed: {e}")
        
        # Generate report
        await self.generate_report()
    
    async def test_environment(self):
        """Test environment and configuration."""
        # Check if .env file exists
        env_file = self.install_dir / ".env"
        if env_file.exists():
            self.success("Environment file exists")
            self.add_result("env_file_exists", True)
        else:
            self.error("Environment file missing")
            self.add_result("env_file_exists", False)
        
        # Check environment variables
        required_vars = [
            'TELEGRAM_API_ID', 'TELEGRAM_API_HASH', 'TELEGRAM_PHONE',
            'TARGET_GROUP_ID', 'BONKBOT_USERNAME'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if not missing_vars:
            self.success("All required environment variables set")
            self.add_result("env_vars_complete", True)
        else:
            self.error(f"Missing environment variables: {missing_vars}")
            self.add_result("env_vars_complete", False, f"Missing: {missing_vars}")
        
        # Test configuration validation
        try:
            sys.path.append(str(self.install_dir))
            from src.config_validator import validate_production_config
            validate_production_config()
            self.success("Configuration validation passed")
            self.add_result("config_validation", True)
        except Exception as e:
            self.error(f"Configuration validation failed: {e}")
            self.add_result("config_validation", False, str(e))
    
    async def test_filesystem(self):
        """Test file system and permissions."""
        # Check directory structure
        required_dirs = ['data', 'logs', 'backups', 'src', 'scripts']
        for dir_name in required_dirs:
            dir_path = self.install_dir / dir_name
            if dir_path.exists():
                self.success(f"Directory exists: {dir_name}")
                self.add_result(f"dir_{dir_name}_exists", True)
            else:
                self.error(f"Directory missing: {dir_name}")
                self.add_result(f"dir_{dir_name}_exists", False)
        
        # Check file permissions
        env_file = self.install_dir / ".env"
        if env_file.exists():
            stat = env_file.stat()
            permissions = oct(stat.st_mode)[-3:]
            if permissions == '640':
                self.success("Environment file permissions correct (640)")
                self.add_result("env_permissions", True)
            else:
                self.warning(f"Environment file permissions: {permissions} (should be 640)")
                self.add_result("env_permissions", False, f"Permissions: {permissions}")
        
        # Check ownership
        try:
            import pwd
            stat = self.install_dir.stat()
            owner = pwd.getpwuid(stat.st_uid).pw_name
            if owner == 'cla-bot':
                self.success("Directory ownership correct (cla-bot)")
                self.add_result("directory_ownership", True)
            else:
                self.warning(f"Directory owner: {owner} (should be cla-bot)")
                self.add_result("directory_ownership", False, f"Owner: {owner}")
        except Exception as e:
            self.error(f"Could not check ownership: {e}")
            self.add_result("directory_ownership", False, str(e))
    
    async def test_service(self):
        """Test systemd service."""
        import subprocess
        
        # Check if service exists
        try:
            result = subprocess.run(['systemctl', 'status', 'cla-bot'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.success("Service is running")
                self.add_result("service_running", True)
            else:
                self.error("Service is not running")
                self.add_result("service_running", False, result.stderr)
        except Exception as e:
            self.error(f"Could not check service status: {e}")
            self.add_result("service_running", False, str(e))
        
        # Check if service is enabled
        try:
            result = subprocess.run(['systemctl', 'is-enabled', 'cla-bot'], 
                                  capture_output=True, text=True)
            if result.stdout.strip() == 'enabled':
                self.success("Service is enabled for auto-start")
                self.add_result("service_enabled", True)
            else:
                self.warning("Service is not enabled for auto-start")
                self.add_result("service_enabled", False)
        except Exception as e:
            self.error(f"Could not check service enabled status: {e}")
            self.add_result("service_enabled", False, str(e))
    
    async def test_health_checks(self):
        """Test health check endpoints."""
        async with aiohttp.ClientSession() as session:
            # Test basic health endpoint
            try:
                async with session.get(f"{self.health_url}/health", timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('status') == 'healthy':
                            self.success("Health check endpoint healthy")
                            self.add_result("health_endpoint", True)
                        else:
                            self.warning(f"Health check status: {data.get('status')}")
                            self.add_result("health_endpoint", False, data.get('status'))
                    else:
                        self.error(f"Health check returned status {response.status}")
                        self.add_result("health_endpoint", False, f"Status: {response.status}")
            except Exception as e:
                self.error(f"Health check endpoint failed: {e}")
                self.add_result("health_endpoint", False, str(e))
            
            # Test detailed health endpoint
            try:
                async with session.get(f"{self.health_url}/health/detailed", timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.success("Detailed health endpoint accessible")
                        self.add_result("detailed_health_endpoint", True)
                        
                        # Check specific health metrics
                        if 'memory_percent' in data:
                            memory = data['memory_percent']
                            if memory < 80:
                                self.success(f"Memory usage healthy: {memory:.1f}%")
                            else:
                                self.warning(f"High memory usage: {memory:.1f}%")
                        
                        if 'cpu_percent' in data:
                            cpu = data['cpu_percent']
                            if cpu < 50:
                                self.success(f"CPU usage healthy: {cpu:.1f}%")
                            else:
                                self.warning(f"High CPU usage: {cpu:.1f}%")
                    else:
                        self.error(f"Detailed health check returned status {response.status}")
                        self.add_result("detailed_health_endpoint", False, f"Status: {response.status}")
            except Exception as e:
                self.error(f"Detailed health check failed: {e}")
                self.add_result("detailed_health_endpoint", False, str(e))
            
            # Test status endpoint
            try:
                async with session.get(f"{self.health_url}/status", timeout=5) as response:
                    if response.status == 200:
                        text = await response.text()
                        if text.strip() == "OK":
                            self.success("Status endpoint OK")
                            self.add_result("status_endpoint", True)
                        else:
                            self.warning(f"Status endpoint response: {text}")
                            self.add_result("status_endpoint", False, text)
                    else:
                        self.error(f"Status endpoint returned status {response.status}")
                        self.add_result("status_endpoint", False, f"Status: {response.status}")
            except Exception as e:
                self.error(f"Status endpoint failed: {e}")
                self.add_result("status_endpoint", False, str(e))
    
    async def test_database(self):
        """Test database connectivity and integrity."""
        db_path = self.install_dir / "data" / "cla_bot.db"
        
        if not db_path.exists():
            self.error("Database file does not exist")
            self.add_result("database_exists", False)
            return
        
        self.success("Database file exists")
        self.add_result("database_exists", True)
        
        # Test database connectivity
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Test basic query
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if tables:
                self.success(f"Database accessible with {len(tables)} tables")
                self.add_result("database_accessible", True)
            else:
                self.warning("Database accessible but no tables found")
                self.add_result("database_accessible", True, "No tables")
            
            # Test integrity
            cursor.execute("PRAGMA integrity_check;")
            result = cursor.fetchone()
            
            if result and result[0] == "ok":
                self.success("Database integrity check passed")
                self.add_result("database_integrity", True)
            else:
                self.error(f"Database integrity check failed: {result}")
                self.add_result("database_integrity", False, str(result))
            
            conn.close()
            
        except Exception as e:
            self.error(f"Database test failed: {e}")
            self.add_result("database_accessible", False, str(e))
    
    async def test_network(self):
        """Test network and security configuration."""
        import subprocess
        
        # Test firewall status
        try:
            result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
            if 'Status: active' in result.stdout:
                self.success("Firewall is active")
                self.add_result("firewall_active", True)
            else:
                self.warning("Firewall is not active")
                self.add_result("firewall_active", False)
        except Exception as e:
            self.error(f"Could not check firewall status: {e}")
            self.add_result("firewall_active", False, str(e))
        
        # Test port accessibility
        import socket
        
        # Health check port should be accessible locally
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', 8080))
            sock.close()
            
            if result == 0:
                self.success("Health check port (8080) accessible")
                self.add_result("port_8080_accessible", True)
            else:
                self.error("Health check port (8080) not accessible")
                self.add_result("port_8080_accessible", False)
        except Exception as e:
            self.error(f"Port test failed: {e}")
            self.add_result("port_8080_accessible", False, str(e))
    
    async def test_performance(self):
        """Test performance metrics."""
        # Test response times
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{self.health_url}/health", timeout=5) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    if response_time < 100:
                        self.success(f"Health check response time: {response_time:.1f}ms")
                        self.add_result("health_response_time", True, f"{response_time:.1f}ms")
                    else:
                        self.warning(f"Slow health check response: {response_time:.1f}ms")
                        self.add_result("health_response_time", False, f"{response_time:.1f}ms")
            except Exception as e:
                self.error(f"Performance test failed: {e}")
                self.add_result("health_response_time", False, str(e))
    
    async def test_backup(self):
        """Test backup system."""
        backup_dir = self.install_dir / "backups"
        
        if backup_dir.exists():
            self.success("Backup directory exists")
            self.add_result("backup_dir_exists", True)
            
            # Check for recent backups
            backup_files = list(backup_dir.glob("cla_bot_backup_*.tar.gz"))
            if backup_files:
                self.success(f"Found {len(backup_files)} backup files")
                self.add_result("backup_files_exist", True, f"{len(backup_files)} files")
            else:
                self.warning("No backup files found")
                self.add_result("backup_files_exist", False)
        else:
            self.error("Backup directory does not exist")
            self.add_result("backup_dir_exists", False)
    
    async def test_monitoring(self):
        """Test monitoring and logging."""
        log_dir = self.install_dir / "logs"
        
        if log_dir.exists():
            self.success("Log directory exists")
            self.add_result("log_dir_exists", True)
            
            # Check for log files
            log_files = list(log_dir.glob("*.log"))
            if log_files:
                self.success(f"Found {len(log_files)} log files")
                self.add_result("log_files_exist", True, f"{len(log_files)} files")
                
                # Check recent log activity
                main_log = log_dir / "cla_bot.log"
                if main_log.exists():
                    stat = main_log.stat()
                    age_hours = (time.time() - stat.st_mtime) / 3600
                    
                    if age_hours < 1:
                        self.success("Recent log activity detected")
                        self.add_result("recent_log_activity", True)
                    else:
                        self.warning(f"No recent log activity ({age_hours:.1f}h ago)")
                        self.add_result("recent_log_activity", False, f"{age_hours:.1f}h ago")
            else:
                self.warning("No log files found")
                self.add_result("log_files_exist", False)
        else:
            self.error("Log directory does not exist")
            self.add_result("log_dir_exists", False)
    
    async def test_integration(self):
        """Test integration readiness."""
        # This would test actual Telegram connectivity in a real scenario
        # For now, we'll check configuration completeness
        
        required_integrations = [
            ('BONKBOT_USERNAME', 'BonkBot'),
            ('CLA_V2_GROUP_ID', 'CLA v2.0'),
            ('MONACO_PNL_GROUP_ID', 'Monaco PNL')
        ]
        
        for env_var, integration_name in required_integrations:
            if os.getenv(env_var):
                self.success(f"{integration_name} configuration present")
                self.add_result(f"integration_{env_var.lower()}", True)
            else:
                self.warning(f"{integration_name} configuration missing")
                self.add_result(f"integration_{env_var.lower()}", False)
    
    async def generate_report(self):
        """Generate test report."""
        self.log("\n" + "=" * 60, Colors.CYAN)
        self.log("📊 PRODUCTION TEST REPORT", Colors.CYAN)
        self.log("=" * 60, Colors.CYAN)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        self.log(f"\n📈 Test Summary:", Colors.BLUE)
        self.log(f"   Total Tests: {total_tests}")
        self.log(f"   Passed: {passed_tests}", Colors.GREEN)
        self.log(f"   Failed: {failed_tests}", Colors.RED if failed_tests > 0 else Colors.GREEN)
        self.log(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            self.log(f"\n❌ Failed Tests:", Colors.RED)
            for result in self.test_results:
                if not result['passed']:
                    details = f" - {result['details']}" if result['details'] else ""
                    self.log(f"   • {result['test']}{details}", Colors.RED)
        
        # Save detailed report
        report_file = self.install_dir / "logs" / f"production_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump({
                    'timestamp': datetime.now().isoformat(),
                    'summary': {
                        'total_tests': total_tests,
                        'passed_tests': passed_tests,
                        'failed_tests': failed_tests,
                        'success_rate': (passed_tests/total_tests)*100
                    },
                    'results': self.test_results
                }, f, indent=2)
            
            self.success(f"Detailed report saved: {report_file}")
        except Exception as e:
            self.error(f"Could not save report: {e}")
        
        # Final verdict
        if failed_tests == 0:
            self.log(f"\n🎉 ALL TESTS PASSED - PRODUCTION READY!", Colors.GREEN)
        elif failed_tests <= 2:
            self.log(f"\n⚠️ MOSTLY READY - {failed_tests} minor issues to address", Colors.YELLOW)
        else:
            self.log(f"\n❌ NOT READY - {failed_tests} issues need attention", Colors.RED)

async def main():
    """Main test runner."""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("CLA v2.0 Bot Production Testing Script")
        print("Usage: python3 deployment/production_test.py")
        print("\nThis script tests all aspects of the production deployment.")
        return
    
    tester = ProductionTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
