"""Simple test for exclusion logic."""

import sys
sys.path.insert(0, 'src')

def test_exclusion_simple():
    """Simple test for exclusion logic."""
    print("🧪 Simple Exclusion Test...")
    
    try:
        from src.trending_analyzer import Trending<PERSON><PERSON>yzer
        from config import config
        
        analyzer = TrendingAnalyzer()
        
        print(f"Excluded destinations: {config.trending.exclude_destinations}")
        
        # Test 1: Direct call
        print(f"About to call should_forward_to_destination...")
        result1 = analyzer.should_forward_to_destination("TestCA", "WINNERS", -1002380594298)
        print(f"Test 1 - WINNERS to low-volume group: {result1} (should be False)")
        
        # Test 2: Different destination
        result2 = analyzer.should_forward_to_destination("TestCA", "BONKBOT", -1002380594298)
        print(f"Test 2 - BONKBOT to low-volume group: {result2} (should be True)")
        
        # Test 3: Manual check
        excluded = config.trending.exclude_destinations
        destination = "WINNERS"
        is_excluded = destination.upper() in [dest.upper() for dest in excluded]
        print(f"Test 3 - Manual exclusion check: {is_excluded} (should be True)")
        
        # Test 4: Step by step
        print(f"\nStep by step debug:")
        print(f"  destination: '{destination}'")
        print(f"  excluded: {excluded}")
        print(f"  destination.upper(): '{destination.upper()}'")
        print(f"  [dest.upper() for dest in excluded]: {[dest.upper() for dest in excluded]}")
        print(f"  destination.upper() in [...]: {destination.upper() in [dest.upper() for dest in excluded]}")
        
        # Test the actual method logic manually
        print(f"\nManual method logic:")
        ca = "TestCA"
        destination = "WINNERS"
        source_group_id = -1002380594298
        
        # Step 1: Check exclusion
        excluded_destinations = getattr(config.trending, 'exclude_destinations', [])
        print(f"  excluded_destinations: {excluded_destinations}")
        
        is_excluded = destination.upper() in [dest.upper() for dest in excluded_destinations]
        print(f"  is_excluded: {is_excluded}")
        
        if is_excluded:
            print(f"  Should return False (excluded)")
            return
        
        # Step 2: Check group type
        is_high_volume = analyzer.is_high_volume_group(source_group_id)
        is_low_volume = analyzer.is_low_volume_group(source_group_id)
        print(f"  is_high_volume: {is_high_volume}")
        print(f"  is_low_volume: {is_low_volume}")
        
        if is_low_volume:
            print(f"  Should return True (low-volume direct forwarding)")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_exclusion_simple()
