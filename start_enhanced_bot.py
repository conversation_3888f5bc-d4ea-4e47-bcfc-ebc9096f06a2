"""Enhanced startup script for CLA v2.0 bot with RefactoredCLABot and all optimizations."""

import asyncio
import os
import sys
import time
import psutil
from datetime import datetime
from typing import List
from loguru import logger

# Add src to path
sys.path.insert(0, 'src')

async def clear_cached_data():
    """Clear any cached data or temporary states."""
    logger.info("🧹 Clearing cached data and temporary states...")
    
    try:
        # Clear any temporary cache files
        cache_files = [
            'data/ca_cache.json',
            'data/temp_stats.json',
            'data/message_cache.json'
        ]
        
        for cache_file in cache_files:
            if os.path.exists(cache_file):
                os.remove(cache_file)
                logger.info(f"   Removed: {cache_file}")
        
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
        os.makedirs('logs', exist_ok=True)
        
        logger.info("✅ Cache cleanup completed")
        
    except Exception as e:
        logger.error(f"Error clearing cached data: {e}")

async def verify_integrations():
    """Verify all integrations are working."""
    logger.info("🔍 Verifying integrations...")
    
    try:
        from config import config
        
        # Check configuration
        logger.info("📋 Configuration Status:")
        logger.info(f"   Trending enabled: {config.trending.enabled}")
        logger.info(f"   Selective protection: {config.trending.selective_protection}")
        logger.info(f"   High-volume groups: {len(config.trending.high_volume_groups)}")
        logger.info(f"   Low-volume groups: {len(config.trending.low_volume_groups)}")
        
        # Check database
        from src.database import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        logger.info("✅ Database connection verified")
        await db_manager.close()
        
        # Check enhanced stats tracker
        from src.enhanced_stats_tracker import enhanced_stats
        logger.info("✅ Enhanced stats tracker initialized")
        
        # Verify group configurations
        logger.info("📊 Group Configurations:")
        for group_id, stats in enhanced_stats.group_stats.items():
            group_type = "🔥 HIGH-VOLUME" if stats.is_high_volume else "⚡ LOW-VOLUME"
            logger.info(f"   {group_type} {stats.group_name} ({group_id})")
        
        # Verify destinations
        logger.info("📤 Forwarding Destinations:")
        for dest_name, stats in enhanced_stats.destination_stats.items():
            # Check if destination is excluded in configuration
            excluded_destinations = getattr(config.trending, 'exclude_destinations', [])
            is_excluded = dest_name.upper() in [dest.upper() for dest in excluded_destinations]

            # Show actual statistics instead of hardcoded status
            if is_excluded:
                status = f"EXCLUDED (CAs sent: {stats.cas_sent}, Last: {stats.last_activity.strftime('%H:%M:%S') if stats.last_activity else 'Never'})"
            else:
                status = f"ACTIVE (CAs sent: {stats.cas_sent}, Last: {stats.last_activity.strftime('%H:%M:%S') if stats.last_activity else 'Never'})"
            logger.info(f"   {dest_name}: {status}")
        
        # Initialize Phase 1 Slow Cook Tracking
        logger.info("🐌 Initializing Phase 1 Slow Cook Pattern Analysis...")
        try:
            from src.slow_cook_analytics import SlowCookAnalytics
            slow_cook_analytics = SlowCookAnalytics()
            logger.info("   ✅ Slow cook analytics initialized")
            logger.info("   📊 Phase 1 data collection: ACTIVE")
            logger.info("   🔍 Pattern tracking: Multi-mention filtering, cross-group detection")
            logger.info("   ⏱️ Time window analysis: 24-hour pattern memory")
        except Exception as e:
            logger.warning(f"   ⚠️ Slow cook analytics initialization failed: {e}")

        logger.info("✅ All integrations verified")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration verification failed: {e}")
        return False

async def start_enhanced_bot():
    """Start the enhanced CLA v2.0 bot with RefactoredCLABot and all optimizations."""
    logger.info("🚀 Starting Enhanced CLA v2.0 Bot with RefactoredCLABot...")

    startup_start = time.time()

    try:
        # Initialize dependency container
        logger.info("🔗 Initializing Dependency Container...")
        from src.dependency_container import DependencyContainer

        container = DependencyContainer()
        await container.initialize()
        logger.info("   ✅ Dependency container initialized")

        # Get enhanced components
        logger.info("📦 Loading Enhanced Components...")

        # Core components with dependency injection
        message_processor = container.get('message_processor')
        ca_analyzer = container.get('ca_analyzer')
        forwarding_manager = container.get('forwarding_manager')
        enhanced_stats_tracker = container.get('enhanced_stats_tracker')

        # Database with optimizations
        db_manager = container.get('db_manager')

        # Telegram client
        telegram_client = container.get('telegram_client')

        logger.info("   ✅ All enhanced components loaded")

        # Performance monitoring setup
        logger.info("📊 Setting up Performance Monitoring...")

        # Get system metrics
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024
        cpu_before = process.cpu_percent()

        logger.info(f"   📈 Initial Memory: {memory_before:.1f}MB")
        logger.info(f"   📈 Initial CPU: {cpu_before:.1f}%")

        # Verify all enhancements are active
        logger.info("🔍 Verifying Enhancements...")

        # Check race condition protection
        ca_rescue_tracker = container.get('ca_rescue_tracker')
        has_locks = hasattr(ca_rescue_tracker, 'rescue_lock')
        logger.info(f"   {'✅' if has_locks else '❌'} Race condition protection: {'ACTIVE' if has_locks else 'MISSING'}")

        # Check database optimizations
        has_batch_lookup = hasattr(db_manager, 'batch_ca_lookup')
        has_query_cache = hasattr(db_manager, 'get_cached_query_result')
        logger.info(f"   {'✅' if has_batch_lookup else '❌'} Batch database operations: {'ACTIVE' if has_batch_lookup else 'MISSING'}")
        logger.info(f"   {'✅' if has_query_cache else '❌'} Query result caching: {'ACTIVE' if has_query_cache else 'MISSING'}")

        # Check bounded caches
        has_bounded_caches = hasattr(ca_rescue_tracker, 'rescue_eligible_cache')
        logger.info(f"   {'✅' if has_bounded_caches else '❌'} Bounded caches: {'ACTIVE' if has_bounded_caches else 'MISSING'}")

        # Check structured logging
        try:
            from src.structured_logger import get_logger
            struct_logger = get_logger("startup")
            struct_logger.info("Enhanced bot startup verification", startup_time=time.time() - startup_start)
            logger.info(f"   ✅ Structured logging: ACTIVE")
        except Exception:
            logger.info(f"   ❌ Structured logging: ERROR")

        # Check error handling
        error_handler = container.get('error_handler')
        has_retry_logic = hasattr(error_handler, 'handle_error_with_retry')
        logger.info(f"   {'✅' if has_retry_logic else '❌'} Enhanced error handling: {'ACTIVE' if has_retry_logic else 'MISSING'}")

        startup_duration = time.time() - startup_start
        logger.info(f"⏱️ Enhanced startup completed in {startup_duration:.2f} seconds")

        # Start the RefactoredCLABot
        logger.info("🤖 Starting RefactoredCLABot...")

        # Import and start the original bot for now (RefactoredCLABot integration pending)
        from main import main as start_main_bot
        await start_main_bot()

    except Exception as e:
        logger.error(f"❌ Error starting enhanced bot: {e}")
        import traceback
        traceback.print_exc()
        raise

def display_startup_banner():
    """Display enhanced startup banner."""
    banner = """
🚀 CLA v2.0 Bot - Enhanced Monitoring Edition
═══════════════════════════════════════════════

📊 Enhanced Features:
✅ Comprehensive CA tracking per group
✅ Real-time performance monitoring  
✅ Automated hourly status reports
✅ Per-group statistics tracking
✅ Anti-pump protection metrics
✅ Forwarding destination analytics

🔥 High-Volume Groups (Trending Analysis):
   • 🧠 GMGN Featured Signals
   • 🚀 MEME 1000X

⚡ Low-Volume Groups (Direct Forwarding):
   • FREE WHALE SIGNALS
   • Solana Activity Tracker
   • 🌹 MANIFEST
   • 👤 Mark Degens
   • 💎 FINDERTRENDING

📤 Active Destinations:
   • BonkBot (Auto-trading)
   • CLA v2.0 (Signal forwarding)
   • Monaco PNL (PNL tracking)
   • WINNERS (REACTIVATED)

🛡️ Protection Features:
   • 6 mentions in 8 minutes (high-volume)
   • 120s spread, 3.0/min velocity limits
   • Global 7-day duplicate prevention
   • Race condition protection
   • Performance bottleneck detection

═══════════════════════════════════════════════
"""
    print(banner)

async def main():
    """Main startup function."""
    try:
        # Display startup banner
        display_startup_banner()
        
        # Clear cached data
        await clear_cached_data()
        
        # Verify integrations
        if not await verify_integrations():
            logger.error("❌ Integration verification failed. Aborting startup.")
            return False
        
        logger.info("🎯 All systems verified. Starting enhanced bot...")
        logger.info("📊 Hourly status reports will be generated automatically")
        logger.info("⚡ Performance monitoring is active")
        logger.info("🛡️ Anti-pump protection is enabled")
        
        # Start the enhanced bot
        await start_enhanced_bot()
        
        return True
        
    except KeyboardInterrupt:
        logger.info("🛑 Startup interrupted by user")
        return False
    except Exception as e:
        logger.error(f"❌ Startup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Configure logging
    logger.remove()
    logger.add(
        "logs/enhanced_bot_startup.log",
        rotation="1 day",
        retention="7 days",
        level="INFO",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    )
    logger.add(
        sys.stdout,
        level="INFO",
        format="{time:HH:mm:ss} | {level} | {message}"
    )
    
    # Run the startup
    success = asyncio.run(main())
    
    if success:
        logger.info("🎉 Enhanced CLA v2.0 Bot startup completed successfully!")
    else:
        logger.error("❌ Enhanced CLA v2.0 Bot startup failed!")
        sys.exit(1)
