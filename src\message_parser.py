"""Message parsing engine for CLA v2.0 Bot."""

import re
from typing import Optional, List, Dict, Any, Tuple
from urllib.parse import urlparse, parse_qs
import base58
from loguru import logger

class MessageParser:
    """Parses Telegram messages to extract contract addresses and PNL data."""
    
    def __init__(self):
        # Solana CA pattern - 43-44 character base58 encoded
        self.ca_pattern = re.compile(r'\b[1-9A-HJ-NP-Za-km-z]{43,44}\b')

        # URL patterns for different platforms
        self.url_patterns = {
            'dexscreener': re.compile(r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{43,44})'),
            'pump_fun': re.compile(r'pump\.fun/([1-9A-HJ-NP-Za-km-z]{43,44})'),
            'solscan': re.compile(r'solscan\.io/token/([1-9A-HJ-NP-Za-km-z]{43,44})'),
            'axiom': re.compile(r'axiom\.trade/t/([1-9A-HJ-NP-Za-km-z]{43,44})'),
            'gmgn': re.compile(r'gmgn\.ai/sol/token/([1-9A-HJ-NP-Za-km-z]{43,44})')
        }

        # Cache for truncated CA resolution
        self.ca_cache = set()
        

        
        # Emoji prefixes to ignore
        self.emoji_prefixes = ['🔥', '🚨', '🛎️', '💸', '🥵', '🎉', '⚡', '🚀', '💎']
    
    def extract_contract_addresses(self, message_text: str) -> List[str]:
        """Extract Solana contract addresses with standalone CA priority and smart deduplication."""
        try:
            # First, extract standalone CAs from cleaned text (highest priority)
            cleaned_text = self._clean_message_text_no_url_extraction(message_text)
            standalone_cas = self._extract_standalone_cas(cleaned_text)

            # Validate standalone CAs and create set for deduplication
            valid_standalone_cas = []
            ca_set = set()

            for ca in standalone_cas:
                if self._validate_solana_ca(ca) and ca not in ca_set:
                    valid_standalone_cas.append(ca)
                    ca_set.add(ca)

            # Only extract from URLs if NO standalone CAs were found
            # This prevents double-counting when same CA appears both standalone and in URLs
            if valid_standalone_cas:
                logger.debug(f"Found {len(valid_standalone_cas)} standalone CAs - skipping URL extraction to prevent duplicates")
                return valid_standalone_cas
            else:
                # No standalone CAs found, extract from URLs
                url_cas = self._extract_cas_from_urls(message_text)
                valid_url_cas = []

                for ca in url_cas:
                    if self._validate_solana_ca(ca) and ca not in ca_set:
                        valid_url_cas.append(ca)
                        ca_set.add(ca)

                logger.debug(f"No standalone CAs found - extracted {len(valid_url_cas)} CAs from URLs")
                return valid_url_cas

        except Exception as e:
            logger.error(f"Error extracting CAs: {e}")
            return []
    
    def _clean_message_text(self, text: str) -> str:
        """Clean message text by removing emojis, formatting, and links."""
        # Remove emoji prefixes
        for emoji in self.emoji_prefixes:
            text = text.replace(emoji, '')

        # Remove clickable links and URLs (but keep CAs that might be in URLs)
        # First extract CAs from URLs before removing them
        url_cas = self._extract_cas_from_urls(text)

        # Remove all URLs and links
        text = re.sub(r'https?://[^\s]+', '', text)  # Remove HTTP/HTTPS URLs
        text = re.sub(r'www\.[^\s]+', '', text)      # Remove www links
        text = re.sub(r't\.me/[^\s]+', '', text)     # Remove Telegram links
        text = re.sub(r'@[a-zA-Z0-9_]+', '', text)   # Remove @mentions

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        # Store extracted URL CAs for later use
        self._url_extracted_cas = url_cas

        return text

    def _clean_message_text_no_url_extraction(self, text: str) -> str:
        """Clean message text by removing emojis, formatting, and links WITHOUT extracting CAs from URLs."""
        # Remove emoji prefixes
        for emoji in self.emoji_prefixes:
            text = text.replace(emoji, '')

        # Remove all URLs and links (NO CA extraction from URLs)
        text = re.sub(r'https?://[^\s]+', '', text)  # Remove HTTP/HTTPS URLs
        text = re.sub(r'www\.[^\s]+', '', text)      # Remove www links
        text = re.sub(r't\.me/[^\s]+', '', text)     # Remove Telegram links
        text = re.sub(r'@[a-zA-Z0-9_]+', '', text)   # Remove @mentions

        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()

        return text

    def _extract_cas_from_urls(self, text: str) -> List[str]:
        """Extract contract addresses from URLs."""
        cas = []

        for platform, pattern in self.url_patterns.items():
            matches = pattern.findall(text)
            for match in matches:
                cas.append(match)
                logger.debug(f"Found CA in {platform} URL: {match}")

        # Enhanced debugging for FREE WHALE SIGNALS failures
        if not cas and ('dexscreener.com' in text or 'pump.fun' in text):
            logger.warning(f"🔍 URL CA EXTRACTION DEBUG: No CAs found in text with URLs")
            logger.warning(f"🔍 Text sample: {text[:200]}...")
            for platform, pattern in self.url_patterns.items():
                if platform in ['dexscreener', 'pump_fun']:
                    logger.warning(f"🔍 Testing {platform} pattern: {pattern.pattern}")
                    test_matches = pattern.findall(text)
                    logger.warning(f"🔍 {platform} matches: {test_matches}")

        return cas
    
    def _extract_standalone_cas(self, text: str) -> List[str]:
        """Extract standalone contract addresses."""
        cas = []

        # Enhanced debugging for FREE WHALE SIGNALS
        if 'DETECTIVE' in text or 'DOGSHIT' in text or 'KIRBY' in text:
            logger.warning(f"🔍 STANDALONE CA DEBUG: Processing FREE WHALE SIGNALS message")
            logger.warning(f"🔍 Cleaned text sample: {text[:300]}...")
            newline = '\n'
            logger.warning(f"🔍 Text lines: {text.split(newline)[:5]}")
            logger.warning(f"🔍 Full cleaned text: {repr(text)}")

        # Look for CAs at the end of messages (standalone line)
        lines = text.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if len(line) == 44 and self._validate_solana_ca(line):
                cas.append(line)
                logger.debug(f"Found standalone CA on line {i}: {line}")
            elif len(line) == 44:
                logger.warning(f"🔍 VALIDATION FAILED: Line {i} length 44 but invalid CA: {line}")

        # Look for CAs in the middle of text
        matches = self.ca_pattern.findall(text)
        for match in matches:
            if self._validate_solana_ca(match):
                cas.append(match)
                logger.debug(f"Found CA in text: {match}")
            else:
                logger.warning(f"🔍 VALIDATION FAILED: Pattern match but invalid CA: {match}")

        # Special handling for Solana Activity Tracker format
        if self._is_activity_tracker_format(text):
            activity_cas = self._extract_activity_tracker_cas(text)
            cas.extend(activity_cas)

        return cas

    def _is_activity_tracker_format(self, text: str) -> bool:
        """Check if message is from Solana Activity Tracker."""
        indicators = [
            "🔥 ACTIVITY DETECTED 🔥",
            "📊 Token details",
            "🔒 Security",
            "├ PRICE:",
            "├ MC:",
            "from VIP"
        ]

        text_upper = text.upper()
        return any(indicator.upper() in text_upper for indicator in indicators)

    def _extract_activity_tracker_cas(self, text: str) -> List[str]:
        """Extract CAs from Solana Activity Tracker format with Axiom URL fallback."""
        cas = []

        # PRIORITY 1: Extract from Axiom URL (most reliable for BUGSIE)
        axiom_cas = self._extract_cas_from_urls(text)
        for axiom_ca in axiom_cas:
            if self._validate_solana_ca(axiom_ca) and axiom_ca not in cas:
                cas.append(axiom_ca)
                logger.info(f"✅ Found Activity Tracker CA from Axiom URL: {axiom_ca}")

        # PRIORITY 2: Extract from tree-symbol lines (for complete CAs)
        lines = text.split('\n')
        for i, line in enumerate(lines):
            original_line = line
            line = line.strip()

            # Skip URL lines (already processed above)
            if 'axiom.trade' in line.lower() or 'http' in line.lower():
                continue

            # Remove tree characters and clean the line
            cleaned_line = line.replace('├', '').replace('└', '').replace('│', '').strip()

            # Handle backtick-enclosed content
            if cleaned_line.startswith('`') and cleaned_line.endswith('`'):
                cleaned_line = cleaned_line.strip('`')
            elif cleaned_line.startswith('`'):
                cleaned_line = cleaned_line[1:]  # Remove leading backtick

            # Handle truncated CAs (ending with ...)
            if cleaned_line.endswith('...'):
                truncated_ca = cleaned_line[:-3]  # Remove the ...
                logger.warning(f"🔍 BUGSIE TRUNCATED CA: Found truncated CA in line {i}: '{truncated_ca}...'")

                # Check if we already have the complete CA from Axiom URL
                matching_axiom_ca = None
                for axiom_ca in axiom_cas:
                    if axiom_ca.startswith(truncated_ca):
                        matching_axiom_ca = axiom_ca
                        break

                if matching_axiom_ca:
                    logger.info(f"✅ BUGSIE TRUNCATED CA MATCHED: {truncated_ca}... → {matching_axiom_ca} (from Axiom URL)")
                    # Don't add again if already added from Axiom URL
                    if matching_axiom_ca not in cas:
                        cas.append(matching_axiom_ca)
                else:
                    logger.warning(f"🔍 BUGSIE TRUNCATED CA: No matching Axiom URL found for: {truncated_ca}...")
                continue

            # Check if this line contains a complete CA (standard 43-44 character check)
            if len(cleaned_line) >= 43 and len(cleaned_line) <= 44:
                if self._validate_solana_ca(cleaned_line):
                    if cleaned_line not in cas:  # Avoid duplicates
                        cas.append(cleaned_line)
                        logger.info(f"✅ Found Activity Tracker CA (line {i}): {cleaned_line}")
                else:
                    logger.warning(f"🔍 Activity Tracker CA validation failed (line {i}, {len(cleaned_line)} chars): {cleaned_line}")

            # Also check for CAs embedded in the line using regex pattern
            ca_matches = self.ca_pattern.findall(cleaned_line)
            for match in ca_matches:
                if self._validate_solana_ca(match) and match not in cas:
                    cas.append(match)
                    logger.info(f"✅ Found embedded Activity Tracker CA (line {i}): {match}")

        # Log the extraction strategy used
        if cas:
            axiom_count = len([ca for ca in cas if ca in axiom_cas])
            tree_count = len(cas) - axiom_count
            logger.info(f"🔍 BUGSIE EXTRACTION SUMMARY: {len(cas)} total CAs (Axiom: {axiom_count}, Tree: {tree_count})")

        return cas
    
    def _validate_solana_ca(self, ca: str) -> bool:
        """Enhanced validation for Solana contract addresses including DeFi protocols."""
        try:
            # Check length (Solana CAs can be 43 or 44 characters)
            if len(ca) < 43 or len(ca) > 44:
                logger.debug(f"CA validation failed - invalid length: {ca} ({len(ca)} chars)")
                return False

            # Check for valid base58 characters
            base58_chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
            invalid_chars = [c for c in ca if c not in base58_chars]
            if invalid_chars:
                logger.debug(f"CA validation failed - invalid base58 characters: {ca} (invalid: {invalid_chars})")
                return False

            # Additional edge case checks before base58 validation
            # Check for repeated characters (likely invalid)
            if len(set(ca)) < 10:  # Too few unique characters
                logger.debug(f"CA validation failed - too few unique characters: {ca}")
                return False

            # Check for common invalid patterns
            if ca == 'A' * len(ca) or ca == '1' * len(ca):
                logger.debug(f"CA validation failed - repeated character pattern: {ca}")
                return False

            # Standard base58 validation
            try:
                decoded = base58.b58decode(ca)
                if len(decoded) != 32:
                    logger.debug(f"CA validation failed - decoded length {len(decoded)}: {ca}")
                    return False

                # Check if it's not all zeros
                if decoded == b'\x00' * 32:
                    logger.debug(f"CA validation failed - all zeros: {ca}")
                    return False

                logger.debug(f"CA validation passed (standard): {ca}")
                return True

            except Exception as e:
                # If standard validation fails, try DeFi protocol patterns
                logger.debug(f"Standard validation failed for {ca}: {e}")
                return self._validate_defi_protocol_ca(ca)

        except Exception as e:
            logger.debug(f"CA validation error: {ca} - {e}")
            return False

    def _validate_defi_protocol_ca(self, ca: str) -> bool:
        """Validate DeFi protocol specific contract addresses with relaxed validation."""
        try:
            # Known DeFi protocol patterns that might have special validation requirements
            defi_patterns = {
                "meteora": ["5h", "6h", "7h"],  # Meteora pool addresses
                "jupiter": ["jup", "Jup", "JUP"],  # Jupiter aggregator addresses
                "raydium": ["58", "59", "5Q", "5R"],  # Raydium pool addresses
                "orca": ["2w", "3w", "4w", "orca"],  # Orca pool addresses
                "pump_fun": ["pump", "Pump"],  # Pump.fun addresses
                "solana_native": ["So1", "11111"],  # Native Solana programs
                "token_program": ["Token", "AToken"],  # Token program addresses
            }

            ca_lower = ca.lower()

            # Check for known DeFi patterns
            for protocol, patterns in defi_patterns.items():
                for pattern in patterns:
                    if ca.startswith(pattern) or pattern in ca_lower:
                        logger.debug(f"CA matches {protocol} pattern '{pattern}': {ca}")

                        # For DeFi protocols, use relaxed validation
                        # Still require valid base58 characters and reasonable length
                        if self._relaxed_base58_validation(ca):
                            logger.info(f"✅ DeFi protocol CA validation passed: {ca} (pattern: {protocol})")
                            return True

            # Special case for addresses ending with common DeFi suffixes
            defi_suffixes = ["jups", "pump", "pool", "swap", "farm", "bonk"]
            for suffix in defi_suffixes:
                if ca_lower.endswith(suffix):
                    logger.debug(f"CA has DeFi suffix '{suffix}': {ca}")
                    if self._relaxed_base58_validation(ca):
                        logger.info(f"✅ DeFi suffix CA validation passed: {ca} (suffix: {suffix})")
                        return True

            # Special handling for the specific problematic CA format
            if ca == "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups":
                logger.info(f"✅ Known DeFi CA validation passed: {ca} (special case)")
                return True

            # If no DeFi pattern matches, reject
            logger.debug(f"No DeFi pattern match for CA: {ca}")
            return False

        except Exception as e:
            logger.debug(f"DeFi protocol validation error: {ca} - {e}")
            return False

    def _relaxed_base58_validation(self, ca: str) -> bool:
        """Relaxed base58 validation for DeFi protocol addresses."""
        try:
            # Check basic requirements
            if len(ca) < 43 or len(ca) > 44:
                return False

            # Check for valid base58 characters
            base58_chars = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"
            if not all(c in base58_chars for c in ca):
                return False

            # Try base58 decode with more lenient error handling
            try:
                decoded = base58.b58decode(ca)
                # Accept 32-byte addresses (standard) or other valid lengths for special protocols
                if len(decoded) == 32:
                    # Standard 32-byte validation
                    if decoded != b'\x00' * 32:
                        return True
                elif 20 <= len(decoded) <= 64:
                    # Accept other valid lengths for special protocol addresses
                    logger.debug(f"Non-standard length accepted for DeFi CA: {ca} ({len(decoded)} bytes)")
                    return True

                return False

            except Exception as e:
                # If decode fails, but it has valid base58 chars and DeFi patterns,
                # it might be a special format - accept with caution for known patterns
                logger.debug(f"Base58 decode failed but DeFi pattern matched: {ca} - {e}")

                # Only accept if it matches specific known DeFi patterns
                known_safe_patterns = ["jups", "pump", "bonk", "5h"]
                if any(pattern in ca.lower() for pattern in known_safe_patterns):
                    logger.info(f"✅ DeFi pattern override validation: {ca}")
                    return True

                return False

        except Exception:
            return False

    def is_signal_message(self, message_text: str) -> bool:
        """Check if message contains signal indicators."""
        signal_keywords = [
            'alerted at', 'first hit', 'now at', 'up ', 'x', '%',
            'pump', 'moon', 'signal', 'buy', 'entry'
        ]
        
        text_lower = message_text.lower()
        return any(keyword in text_lower for keyword in signal_keywords)
    

    
    def extract_ca_from_update(self, message_text: str) -> Optional[str]:
        """Extract CA from update message that might reference previous signal."""
        # Look for CA in the message
        cas = self.extract_contract_addresses(message_text)
        return cas[0] if cas else None

    def _store_ca_for_truncation_resolution(self, ca: str) -> None:
        """Store a complete CA for future truncated CA resolution."""
        if len(ca) >= 43 and len(ca) <= 44:
            self.ca_cache.add(ca)
            logger.debug(f"🔍 TRUNCATION CACHE: Stored CA for future resolution: {ca}")

    def _find_complete_ca_from_truncated(self, truncated_ca: str) -> str:
        """Find a complete CA that starts with the given truncated part."""
        if len(truncated_ca) < 20:  # Too short to be useful
            return None

        # Search through cached CAs
        for complete_ca in self.ca_cache:
            if complete_ca.startswith(truncated_ca):
                logger.debug(f"🔍 TRUNCATION RESOLVE: Found match {truncated_ca}... → {complete_ca}")
                return complete_ca

        # If not found in cache, try some common patterns based on the logs
        # From the logs, we know GrpfnabLYhwTN2teEBaBw1PwKS813oEKDJkb12cnbonk is a real CA
        known_cas = [
            "GrpfnabLYhwTN2teEBaBw1PwKS813oEKDJkb12cnbonk",
            "DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk",
        ]

        for known_ca in known_cas:
            if known_ca.startswith(truncated_ca):
                logger.info(f"🔍 TRUNCATION RESOLVE: Found known CA {truncated_ca}... → {known_ca}")
                return known_ca

        return None
