#!/usr/bin/env python3
"""
Test the status reporting fix for WINNERS
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_status_fix():
    print("🔧 Testing Status Reporting Fix...")
    
    try:
        # Import the enhanced stats tracker
        from src.enhanced_stats_tracker import EnhancedStatsTracker
        
        # Create test instance
        stats = EnhancedStatsTracker()
        
        # Simulate some WINNERS activity
        stats.record_ca_forwarded(group_id=-1002333406905, destination="WINNERS", ca_count=5)
        stats.record_ca_forwarded(group_id=-1002333406905, destination="BonkBot", ca_count=5)
        stats.record_ca_forwarded(group_id=-1002333406905, destination="CLA v2.0", ca_count=5)
        stats.record_ca_forwarded(group_id=-1002333406905, destination="Monaco PNL", ca_count=5)
        
        # Generate status report
        report = stats.generate_hourly_report()
        
        # Check if WINNERS shows proper statistics instead of "GLOBALLY DISABLED"
        report_lines = report.split('\n')
        in_destinations = False
        winners_found = False
        winners_line = ""
        
        for line in report_lines:
            if "📤 Forwarding Destinations:" in line:
                in_destinations = True
                print(f"✅ Found destinations section")
            elif in_destinations and "WINNERS:" in line:
                winners_found = True
                winners_line = line.strip()
                break
            elif in_destinations and line.strip() and (line.startswith("📈") or line.startswith("🛡️")):
                break
        
        if winners_found:
            print(f"✅ WINNERS status line: {winners_line}")
            if "GLOBALLY DISABLED" in winners_line:
                print("❌ WINNERS still shows as GLOBALLY DISABLED")
                return False
            elif "CAs sent" in winners_line:
                print("✅ WINNERS now shows proper statistics!")
                return True
            else:
                print("⚠️ WINNERS line format unexpected")
                return False
        else:
            print("❌ WINNERS not found in destinations")
            return False
            
    except Exception as e:
        print(f"❌ Error testing status fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_status_fix()
    if success:
        print("\n🎉 STATUS REPORTING FIX SUCCESSFUL!")
        print("✅ WINNERS will now show proper statistics in hourly reports")
    else:
        print("\n❌ STATUS REPORTING FIX FAILED!")
    
    sys.exit(0 if success else 1)
