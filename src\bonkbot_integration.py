"""BonkBot integration for automated trading via CLA v2.0 Bot."""

import asyncio
from typing import Optional, List
from datetime import datetime
from loguru import logger

from src.telegram_client import TelegramClientManager
from config import config

class BonkBotIntegration:
    """Handles integration with BonkBot for automated trading."""
    
    def __init__(self, telegram_client: TelegramClientManager):
        self.telegram_client = telegram_client
        self.bonkbot_entity = None
        self.message_queue = asyncio.Queue()
        self.processing_queue = False

        # Queue-level duplicate prevention
        self.queued_cas = set()  # Track CAs currently in queue
        self.recent_sent_cas = {}  # CA -> timestamp for recent sends
    
    async def initialize(self):
        """Initialize BonkBot integration."""
        try:
            # Get BonkBot entity
            if config.bonkbot.chat_id:
                self.bonkbot_entity = await self.telegram_client.get_entity(config.bonkbot.chat_id)
            else:
                self.bonkbot_entity = await self.telegram_client.get_entity(config.bonkbot.username)
            
            if not self.bonkbot_entity:
                logger.error("Failed to find BonkBot entity")
                return False
            
            logger.info(f"BonkBot integration initialized: {config.bonkbot.username}")
            
            # Start message queue processor
            asyncio.create_task(self._process_message_queue())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize BonkBot integration: {e}")
            return False
    
    async def _is_ca_recently_sent(self, ca: str) -> bool:
        """Check if CA was recently sent to prevent duplicates."""
        from datetime import datetime, timedelta

        now = datetime.now()
        # Clean up old entries (older than 5 minutes)
        cutoff_time = now - timedelta(minutes=5)
        expired_cas = [c for c, timestamp in self.recent_sent_cas.items() if timestamp < cutoff_time]
        for c in expired_cas:
            del self.recent_sent_cas[c]

        return ca in self.recent_sent_cas

    async def send_ca_to_bonkbot(self, ca: str, source_message: str = "") -> bool:
        """Send contract address to BonkBot for auto-trading."""
        try:
            # Check for queue-level duplicates
            if ca in self.queued_cas or await self._is_ca_recently_sent(ca):
                logger.debug(f"BonkBot: CA {ca} already queued or recently sent - skipping")
                return False

            # Format message for BonkBot
            formatted_message = self._format_ca_message(ca, source_message)

            # Add to queue for processing
            self.queued_cas.add(ca)
            await self.message_queue.put({
                'ca': ca,
                'message': formatted_message,
                'timestamp': datetime.now(),
                'source': source_message[:100] if source_message else ""
            })

            logger.info(f"CA queued for BonkBot: {ca}")
            return True

        except Exception as e:
            logger.error(f"Failed to queue CA for BonkBot: {e}")
            return False
    
    def _format_ca_message(self, ca: str, source_message: str = "") -> str:
        """Format contract address message for BonkBot - optimized for instant buy."""
        # BonkBot expects just the CA for instant buy (most efficient)
        return ca
    
    async def _process_message_queue(self):
        """Process queued messages to BonkBot."""
        self.processing_queue = True
        logger.info("Started BonkBot message queue processor")
        
        while self.processing_queue:
            try:
                # Get message from queue (wait up to 1 second)
                try:
                    message_data = await asyncio.wait_for(
                        self.message_queue.get(), 
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Send message to BonkBot
                success = await self._send_message_to_bonkbot(message_data)

                ca = message_data['ca']

                # Remove from queued set and update recent sent tracking
                self.queued_cas.discard(ca)
                if success:
                    self.recent_sent_cas[ca] = datetime.now()
                    logger.info(f"Successfully sent CA to BonkBot: {ca}")
                else:
                    logger.error(f"Failed to send CA to BonkBot: {ca}")

                # Rate limiting - wait between messages
                await asyncio.sleep(2)  # 2 second delay between messages
                
            except Exception as e:
                logger.error(f"Error in message queue processor: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _send_message_to_bonkbot(self, message_data: dict) -> bool:
        """Send individual message to BonkBot."""
        try:
            if not self.bonkbot_entity:
                logger.error("BonkBot entity not initialized")
                return False
            
            # Send the message
            success = await self.telegram_client.send_message(
                self.bonkbot_entity, 
                message_data['message']
            )
            
            if success:
                logger.debug(f"Message sent to BonkBot: {message_data['message']}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to BonkBot: {e}")
            return False
    
    async def send_multiple_cas(self, cas: List[str], source_message: str = "") -> int:
        """Send multiple contract addresses to BonkBot with optimized performance."""
        success_count = 0

        for ca in cas:
            if await self.send_ca_to_bonkbot(ca, source_message):
                success_count += 1

            # PERFORMANCE OPTIMIZATION: Reduced delay from 500ms to 50ms
            # Queue-based system doesn't need aggressive rate limiting
            await asyncio.sleep(0.05)  # 50ms instead of 500ms

        logger.info(f"Queued {success_count}/{len(cas)} CAs for BonkBot")
        return success_count
    
    async def test_bonkbot_connection(self) -> bool:
        """Test connection to BonkBot."""
        try:
            if not self.bonkbot_entity:
                logger.error("BonkBot entity not initialized")
                return False
            
            # Send a test message (you might want to use a specific test command)
            test_message = "/help"  # or whatever test command BonkBot supports
            
            success = await self.telegram_client.send_message(
                self.bonkbot_entity, 
                test_message
            )
            
            if success:
                logger.info("BonkBot connection test successful")
            else:
                logger.error("BonkBot connection test failed")
            
            return success
            
        except Exception as e:
            logger.error(f"BonkBot connection test error: {e}")
            return False
    
    async def get_queue_status(self) -> dict:
        """Get status of the message queue."""
        return {
            'queue_size': self.message_queue.qsize(),
            'processing': self.processing_queue,
            'bonkbot_connected': self.bonkbot_entity is not None
        }
    
    async def clear_queue(self):
        """Clear the message queue."""
        try:
            while not self.message_queue.empty():
                await self.message_queue.get()
            logger.info("BonkBot message queue cleared")
        except Exception as e:
            logger.error(f"Error clearing queue: {e}")
    
    async def pause_processing(self):
        """Pause message queue processing."""
        self.processing_queue = False
        logger.info("BonkBot message processing paused")
    
    async def resume_processing(self):
        """Resume message queue processing."""
        if not self.processing_queue:
            self.processing_queue = True
            asyncio.create_task(self._process_message_queue())
            logger.info("BonkBot message processing resumed")
    
    async def send_custom_message(self, message: str) -> bool:
        """Send a custom message to BonkBot."""
        try:
            if not self.bonkbot_entity:
                logger.error("BonkBot entity not initialized")
                return False
            
            success = await self.telegram_client.send_message(
                self.bonkbot_entity, 
                message
            )
            
            if success:
                logger.info(f"Custom message sent to BonkBot: {message}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending custom message to BonkBot: {e}")
            return False
    
    def get_bonkbot_info(self) -> dict:
        """Get BonkBot configuration info."""
        return {
            'username': config.bonkbot.username,
            'chat_id': config.bonkbot.chat_id,
            'entity_found': self.bonkbot_entity is not None,
            'queue_processing': self.processing_queue
        }
    
    async def shutdown(self):
        """Shutdown BonkBot integration."""
        logger.info("Shutting down BonkBot integration")
        self.processing_queue = False
        await self.clear_queue()
        logger.info("BonkBot integration shutdown complete")
