"""Unit tests for DependencyContainer."""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.dependency_container import DependencyContainer

class TestDependencyContainer:
    """Test cases for DependencyContainer."""
    
    @pytest.fixture
    def container(self):
        """Create DependencyContainer instance."""
        return DependencyContainer()
    
    def test_container_initialization(self, container):
        """Test container initialization."""
        assert container._instances == {}
        assert container._initialized is False
    
    @pytest.mark.asyncio
    async def test_initialize_dependencies(self, container):
        """Test dependency initialization."""
        with patch.multiple(
            container,
            _initialize_database=AsyncMock(),
            _initialize_telegram_client=AsyncMock(),
            _initialize_ca_detector=AsyncMock(),
            _initialize_trending_analyzer=AsyncMock(),
            _initialize_pnl_tracker=AsyncMock(),
            _initialize_integrations=AsyncMock(),
            _initialize_analyzers=AsyncMock(),
            _initialize_processors=AsyncMock(),
            _initialize_managers=AsyncMock()
        ):
            await container.initialize()
            
            assert container._initialized is True
            container._initialize_database.assert_called_once()
            container._initialize_telegram_client.assert_called_once()
            container._initialize_ca_detector.assert_called_once()
    
    def test_get_component_before_initialization(self, container):
        """Test getting component before initialization."""
        with pytest.raises(RuntimeError):
            container.get('non_existent_component')
    
    def test_get_component_after_initialization(self, container):
        """Test getting component after initialization."""
        # Simulate initialization
        container._initialized = True
        container._instances['test_component'] = Mock()
        
        component = container.get('test_component')
        assert component is not None
    
    def test_get_non_existent_component(self, container):
        """Test getting non-existent component."""
        container._initialized = True
        
        component = container.get('non_existent_component')
        assert component is None
    
    def test_get_all_components(self, container):
        """Test getting all components."""
        container._instances = {'comp1': Mock(), 'comp2': Mock()}
        
        components = container.get_all_components()
        assert len(components) == 2
        assert 'comp1' in components
        assert 'comp2' in components
    
    def test_is_initialized(self, container):
        """Test initialization status check."""
        assert container.is_initialized() is False
        
        container._initialized = True
        assert container.is_initialized() is True
    
    @pytest.mark.asyncio
    async def test_cleanup(self, container):
        """Test container cleanup."""
        # Mock components with cleanup methods
        mock_component1 = Mock()
        mock_component1.cleanup = AsyncMock()
        mock_component2 = Mock()
        # Component without cleanup method
        
        container._instances = {
            'component1': mock_component1,
            'component2': mock_component2,
            'db_manager': Mock()
        }
        container._instances['db_manager'].close = AsyncMock()
        container._initialized = True
        
        await container.cleanup()
        
        mock_component1.cleanup.assert_called_once()
        container._instances['db_manager'].close.assert_called_once()
        assert container._instances == {}
        assert container._initialized is False
    
    def test_get_component_stats(self, container):
        """Test getting component statistics."""
        mock_component1 = Mock()
        mock_component1.get_stats.return_value = {'stat1': 100}
        mock_component2 = Mock()
        # Component without get_stats method
        
        container._instances = {
            'component1': mock_component1,
            'component2': mock_component2
        }
        
        stats = container.get_component_stats()
        
        assert 'component1' in stats
        assert stats['component1']['stat1'] == 100
        assert 'component2' not in stats
    
    @pytest.mark.asyncio
    async def test_health_check(self, container):
        """Test health check functionality."""
        mock_component1 = Mock()
        mock_component1.health_check = AsyncMock(return_value=True)
        mock_component2 = Mock()
        # Component without health_check method
        
        container._instances = {
            'component1': mock_component1,
            'component2': mock_component2
        }
        
        health_status = await container.health_check()
        
        assert health_status['component1'] is True
        assert health_status['component2'] is True  # Basic check (not None)
    
    @pytest.mark.asyncio
    async def test_health_check_with_failure(self, container):
        """Test health check with component failure."""
        mock_component = Mock()
        mock_component.health_check = AsyncMock(side_effect=Exception("Health check failed"))
        
        container._instances = {'component': mock_component}
        
        health_status = await container.health_check()
        
        assert health_status['component'] is False

class TestDependencyInjection:
    """Integration tests for dependency injection."""
    
    @pytest.mark.asyncio
    async def test_component_dependencies(self):
        """Test that components receive correct dependencies."""
        container = DependencyContainer()
        
        # Mock the initialization methods to avoid actual initialization
        with patch.multiple(
            container,
            _initialize_database=AsyncMock(),
            _initialize_telegram_client=AsyncMock(),
            _initialize_ca_detector=AsyncMock(),
            _initialize_trending_analyzer=AsyncMock(),
            _initialize_pnl_tracker=AsyncMock(),
            _initialize_integrations=AsyncMock(),
            _initialize_analyzers=AsyncMock(),
            _initialize_processors=AsyncMock(),
            _initialize_managers=AsyncMock()
        ):
            # Mock some components
            container._instances = {
                'ca_detector': Mock(),
                'trending_analyzer': Mock(),
                'high_volume_ca_analyzer': Mock(),
                'enhanced_stats_tracker': Mock(),
                'ca_rescue_tracker': Mock(),
                'forwarding_manager': Mock()
            }
            
            await container.initialize()
            
            # Verify components are available
            assert container.get('ca_detector') is not None
            assert container.get('trending_analyzer') is not None
            assert container.get('enhanced_stats_tracker') is not None
    
    @pytest.mark.asyncio
    async def test_circular_dependency_prevention(self):
        """Test that circular dependencies are handled properly."""
        container = DependencyContainer()
        
        # This test ensures that the initialization order prevents circular dependencies
        with patch.multiple(
            container,
            _initialize_database=AsyncMock(),
            _initialize_telegram_client=AsyncMock(),
            _initialize_ca_detector=AsyncMock(),
            _initialize_trending_analyzer=AsyncMock(),
            _initialize_pnl_tracker=AsyncMock(),
            _initialize_integrations=AsyncMock(),
            _initialize_analyzers=AsyncMock(),
            _initialize_processors=AsyncMock(),
            _initialize_managers=AsyncMock()
        ):
            # Should not raise any exceptions
            await container.initialize()
            assert container.is_initialized()

if __name__ == "__main__":
    pytest.main([__file__])
