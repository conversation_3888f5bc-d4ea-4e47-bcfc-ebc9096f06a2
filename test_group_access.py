#!/usr/bin/env python3
"""
Test group access and message handler setup for inactive groups
"""
import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_group_access():
    """Test if the bot can access FINDERTRENDING and MANIFEST groups."""
    print("🔍 TESTING GROUP ACCESS...")
    
    try:
        from src.telegram_client import TelegramClientManager
        from config import config
        
        # Initialize telegram client
        telegram_client = TelegramClientManager()
        
        if not await telegram_client.initialize():
            print("❌ Failed to initialize Telegram client")
            return False
        
        if not await telegram_client.connect():
            print("❌ Failed to connect to Telegram")
            return False
        
        # Test access to specific groups
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"\n🔍 Testing group access...")
        
        # Test FINDERTRENDING
        try:
            entity = await telegram_client.client.get_entity(findertrending_id)
            print(f"✅ FINDERTRENDING access: SUCCESS")
            print(f"   Title: {entity.title}")
            print(f"   ID: {entity.id}")
            print(f"   Type: {type(entity).__name__}")
        except Exception as e:
            print(f"❌ FINDERTRENDING access: FAILED - {e}")
        
        # Test MANIFEST
        try:
            entity = await telegram_client.client.get_entity(manifest_id)
            print(f"✅ MANIFEST access: SUCCESS")
            print(f"   Title: {entity.title}")
            print(f"   ID: {entity.id}")
            print(f"   Type: {type(entity).__name__}")
        except Exception as e:
            print(f"❌ MANIFEST access: FAILED - {e}")
        
        # Check if groups are in active list
        print(f"\n📋 Configuration check:")
        print(f"FINDERTRENDING in active_group_ids: {findertrending_id in config.target_group.active_group_ids}")
        print(f"MANIFEST in active_group_ids: {manifest_id in config.target_group.active_group_ids}")
        
        await telegram_client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Group access test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n🔍 TESTING CONFIGURATION...")
    
    try:
        from config import config
        
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"All groups: {config.target_group.all_group_ids}")
        print(f"Active groups: {config.target_group.active_group_ids}")
        print(f"Group status: {config.target_group.group_status}")
        
        print(f"\nFINDERTRENDING ({findertrending_id}):")
        print(f"  - In all_group_ids: {findertrending_id in config.target_group.all_group_ids}")
        print(f"  - In active_group_ids: {findertrending_id in config.target_group.active_group_ids}")
        print(f"  - Status: {config.target_group.group_status.get(findertrending_id, 'NOT FOUND')}")
        
        print(f"\nMANIFEST ({manifest_id}):")
        print(f"  - In all_group_ids: {manifest_id in config.target_group.all_group_ids}")
        print(f"  - In active_group_ids: {manifest_id in config.target_group.active_group_ids}")
        print(f"  - Status: {config.target_group.group_status.get(manifest_id, 'NOT FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_group_manager():
    """Test group manager setup."""
    print("\n🔍 TESTING GROUP MANAGER...")
    
    try:
        from src.group_manager import group_manager
        
        findertrending_id = -1002139128702
        manifest_id = -1002064145465
        
        print(f"All monitored groups: {group_manager.get_all_monitored_groups()}")
        
        print(f"\nFINDERTRENDING ({findertrending_id}):")
        print(f"  - Is monitored: {group_manager.is_monitored_group(findertrending_id)}")
        print(f"  - Is low-volume: {group_manager.is_low_volume_group(findertrending_id)}")
        print(f"  - Group name: {group_manager.get_group_name(findertrending_id)}")
        print(f"  - Group type: {group_manager.get_group_type(findertrending_id)}")
        
        print(f"\nMANIFEST ({manifest_id}):")
        print(f"  - Is monitored: {group_manager.is_monitored_group(manifest_id)}")
        print(f"  - Is low-volume: {group_manager.is_low_volume_group(manifest_id)}")
        print(f"  - Group name: {group_manager.get_group_name(manifest_id)}")
        print(f"  - Group type: {group_manager.get_group_type(manifest_id)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Group manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("🚨 GROUP ACCESS AND CONFIGURATION TEST")
    print("=" * 50)
    
    success = True
    
    success &= test_configuration()
    success &= test_group_manager()
    success &= await test_group_access()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ ALL TESTS COMPLETED - Check results above")
    else:
        print("❌ SOME TESTS FAILED - Check errors above")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
