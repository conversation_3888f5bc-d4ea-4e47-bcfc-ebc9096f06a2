"""Forwarding management component for CLA v2.0 Bot."""

import asyncio
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

class ForwardingManager:
    """Manages forwarding of contract addresses to multiple destinations."""

    def __init__(self, bonkbot_integration=None, cla_v2_integration=None,
                 monaco_pnl_integration=None, enhanced_stats_tracker=None):
        """Initialize forwarding manager with destination integrations."""
        self.bonkbot_integration = bonkbot_integration
        self.cla_v2_integration = cla_v2_integration
        self.monaco_pnl_integration = monaco_pnl_integration
        self.enhanced_stats_tracker = enhanced_stats_tracker
        
        # Statistics
        self.stats = {
            'total_forwarding_attempts': 0,
            'successful_forwards': 0,
            'failed_forwards': 0,
            'bonkbot_forwards': 0,
            'cla_v2_forwards': 0,
            'monaco_pnl_forwards': 0,
            'rescue_forwards': 0,
            'forwarding_errors': 0
        }
        
        # Active destinations
        self.active_destinations = []
        if bonkbot_integration:
            self.active_destinations.append('BonkBot')
        if cla_v2_integration:
            self.active_destinations.append('CLA v2.0')
        if monaco_pnl_integration:
            self.active_destinations.append('Monaco PNL')
        
        logger.info(f"Forwarding Manager initialized with destinations: {self.active_destinations}")
    
    async def forward_trending_cas(self, trending_cas: List[str], message_text: str, 
                                 source_group: str, chat_id: int) -> Dict[str, Any]:
        """Forward trending CAs to all active destinations."""
        if not trending_cas:
            return {'forwarded_count': 0, 'destinations': []}
        
        forwarding_start = time.perf_counter()
        
        try:
            self.stats['total_forwarding_attempts'] += 1
            
            logger.info(f"📤 TRENDING FORWARDING: {len(trending_cas)} CAs from {source_group}")
            
            forwarded_count = 0
            successful_destinations = []
            
            # Forward to BonkBot
            if self.bonkbot_integration:
                try:
                    success = await self.bonkbot_integration.send_multiple_cas(trending_cas, message_text)
                    if success:
                        forwarded_count += len(trending_cas)
                        successful_destinations.append('BonkBot')
                        self.stats['bonkbot_forwards'] += len(trending_cas)
                        logger.info(f"📤 → BonkBot: {len(trending_cas)} CAs sent")
                        if self.enhanced_stats_tracker:
                            self.enhanced_stats_tracker.record_ca_forwarded(chat_id, "BonkBot", len(trending_cas))
                except Exception as e:
                    logger.error(f"Error forwarding to BonkBot: {e}")
                    self.stats['failed_forwards'] += 1
            
            # Forward to CLA v2.0
            if self.cla_v2_integration:
                try:
                    success = await self.cla_v2_integration.send_multiple_cas(trending_cas, message_text, source_group)
                    if success:
                        forwarded_count += len(trending_cas)
                        successful_destinations.append('CLA v2.0')
                        self.stats['cla_v2_forwards'] += len(trending_cas)
                        logger.info(f"📤 → CLA v2.0: {len(trending_cas)} CAs sent")
                        if self.enhanced_stats_tracker:
                            self.enhanced_stats_tracker.record_ca_forwarded(chat_id, "CLA v2.0", len(trending_cas))
                except Exception as e:
                    logger.error(f"Error forwarding to CLA v2.0: {e}")
                    self.stats['failed_forwards'] += 1
            
            # Forward to Monaco PNL
            if self.monaco_pnl_integration:
                try:
                    success = await self.monaco_pnl_integration.send_multiple_cas(trending_cas, message_text, source_group)
                    if success:
                        forwarded_count += len(trending_cas)
                        successful_destinations.append('Monaco PNL')
                        self.stats['monaco_pnl_forwards'] += len(trending_cas)
                        logger.info(f"📤 → Monaco PNL: {len(trending_cas)} CAs sent")
                        if self.enhanced_stats_tracker:
                            self.enhanced_stats_tracker.record_ca_forwarded(chat_id, "Monaco PNL", len(trending_cas))
                except Exception as e:
                    logger.error(f"Error forwarding to Monaco PNL: {e}")
                    self.stats['failed_forwards'] += 1
            
            if successful_destinations:
                self.stats['successful_forwards'] += 1
            
            forwarding_time = (time.perf_counter() - forwarding_start) * 1000
            
            logger.info(f"📤 TRENDING FORWARDING COMPLETE: {len(trending_cas)} CAs → "
                       f"{len(successful_destinations)} destinations | {forwarding_time:.1f}ms")
            
            return {
                'forwarded_count': forwarded_count,
                'destinations': successful_destinations,
                'forwarding_time_ms': forwarding_time
            }
            
        except Exception as e:
            self.stats['forwarding_errors'] += 1
            logger.error(f"Error in trending forwarding: {e}")
            return {'forwarded_count': 0, 'destinations': [], 'error': str(e)}
    
    async def forward_rescued_cas(self, rescue_cas: List[str], message_text: str, 
                                source_group: str, chat_id: int, ca_detector) -> Dict[str, Any]:
        """Forward rescued CAs to all active destinations with final validation."""
        if not rescue_cas:
            return {'forwarded_count': 0, 'destinations': []}
        
        try:
            logger.info(f"🚀 RESCUE FORWARDING: Processing {len(rescue_cas)} rescued CAs from {source_group}")
            
            # FINAL SAFETY CHECK: Validate each CA hasn't been processed since rescue decision
            validated_cas = []
            for ca in rescue_cas:
                # Check one more time against the CA detector cache
                if not await ca_detector.is_ca_in_cache(ca):
                    validated_cas.append(ca)
                    logger.info(f"✅ RESCUE VALIDATED: {ca} cleared for forwarding")
                else:
                    logger.warning(f"🚫 RESCUE BLOCKED: {ca} found in cache during final validation")
            
            if not validated_cas:
                logger.warning(f"🚫 RESCUE FORWARDING ABORTED: No CAs passed final validation")
                return {'forwarded_count': 0, 'destinations': []}
            
            logger.info(f"🚀 RESCUE FORWARDING: {len(validated_cas)}/{len(rescue_cas)} CAs validated for forwarding")
            
            # Forward validated CAs using same logic as trending
            result = await self.forward_trending_cas(validated_cas, message_text, source_group, chat_id)
            
            # Mark rescued CAs as processed to prevent future duplicates
            for ca in validated_cas:
                await ca_detector.db_manager.mark_ca_processed(ca)
                if hasattr(ca_detector, 'processed_cache'):
                    ca_detector.processed_cache.add(ca)
                logger.debug(f"🔒 RESCUE PROCESSED: {ca} marked as processed")
            
            self.stats['rescue_forwards'] += len(validated_cas)
            
            logger.info(f"🚀 RESCUE FORWARDING SUMMARY: {len(validated_cas)} CAs → {len(result['destinations'])} destinations")
            
            return result
            
        except Exception as e:
            self.stats['forwarding_errors'] += 1
            logger.error(f"Error in rescue forwarding: {e}")
            return {'forwarded_count': 0, 'destinations': [], 'error': str(e)}
    
    async def send_status_report(self, report: str) -> bool:
        """Send status report to CLA v2.0 channel."""
        try:
            if self.cla_v2_integration:
                # Format report for Telegram (split if too long)
                report_chunks = self._split_report_for_telegram(report)
                
                for chunk in report_chunks:
                    success = await self.cla_v2_integration.send_status_report(chunk)
                    if success:
                        logger.info("✅ Status report sent to CLA v2.0 channel")
                    else:
                        logger.warning("⚠️ Failed to send status report to CLA v2.0 channel")
                    
                    # Small delay between chunks
                    await asyncio.sleep(1)
                
                return True
            else:
                logger.warning("CLA v2.0 integration not available for status reports")
                return False
                
        except Exception as e:
            logger.error(f"Error sending status report: {e}")
            return False
    
    def _split_report_for_telegram(self, report: str, max_length: int = 4000) -> List[str]:
        """Split long report into chunks suitable for Telegram."""
        if len(report) <= max_length:
            return [report]
        
        chunks = []
        lines = report.split('\n')
        current_chunk = ""
        
        for line in lines:
            if len(current_chunk) + len(line) + 1 <= max_length:
                current_chunk += line + '\n'
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = line + '\n'
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def get_stats(self) -> Dict[str, Any]:
        """Get forwarding manager statistics."""
        return {
            **self.stats,
            'active_destinations': self.active_destinations.copy(),
            'success_rate': (self.stats['successful_forwards'] / max(1, self.stats['total_forwarding_attempts'])) * 100
        }
    
    async def test_destinations(self) -> Dict[str, bool]:
        """Test connectivity to all destinations."""
        results = {}
        
        try:
            # Test BonkBot
            if self.bonkbot_integration:
                try:
                    # Simple connectivity test
                    results['BonkBot'] = True  # Assume working if integration exists
                except Exception as e:
                    logger.error(f"BonkBot test failed: {e}")
                    results['BonkBot'] = False
            
            # Test CLA v2.0
            if self.cla_v2_integration:
                try:
                    results['CLA v2.0'] = True
                except Exception as e:
                    logger.error(f"CLA v2.0 test failed: {e}")
                    results['CLA v2.0'] = False
            
            # Test Monaco PNL
            if self.monaco_pnl_integration:
                try:
                    results['Monaco PNL'] = True
                except Exception as e:
                    logger.error(f"Monaco PNL test failed: {e}")
                    results['Monaco PNL'] = False
            
            logger.info(f"Destination test results: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error testing destinations: {e}")
            return {}

class ForwardingQueue:
    """Queue manager for forwarding operations with rate limiting."""
    
    def __init__(self, max_queue_size: int = 1000):
        """Initialize forwarding queue."""
        self.queue = asyncio.Queue(maxsize=max_queue_size)
        self.processing = False
        self.stats = {
            'items_queued': 0,
            'items_processed': 0,
            'queue_full_errors': 0
        }
        
        logger.info(f"Forwarding Queue initialized with max size: {max_queue_size}")
    
    async def add_to_queue(self, item: Dict[str, Any]) -> bool:
        """Add item to forwarding queue."""
        try:
            self.queue.put_nowait(item)
            self.stats['items_queued'] += 1
            return True
        except asyncio.QueueFull:
            self.stats['queue_full_errors'] += 1
            logger.warning("Forwarding queue is full, dropping item")
            return False
    
    async def process_queue(self, forwarding_manager):
        """Process items in the forwarding queue."""
        self.processing = True
        
        try:
            while self.processing:
                try:
                    # Wait for item with timeout
                    item = await asyncio.wait_for(self.queue.get(), timeout=1.0)
                    
                    # Process the item
                    await self._process_queue_item(item, forwarding_manager)
                    self.stats['items_processed'] += 1
                    
                    # Mark task as done
                    self.queue.task_done()
                    
                except asyncio.TimeoutError:
                    # No items in queue, continue
                    continue
                except Exception as e:
                    logger.error(f"Error processing queue item: {e}")
                    
        except Exception as e:
            logger.error(f"Error in queue processing: {e}")
        finally:
            self.processing = False
    
    async def _process_queue_item(self, item: Dict[str, Any], forwarding_manager):
        """Process a single queue item."""
        try:
            item_type = item.get('type')
            
            if item_type == 'trending':
                await forwarding_manager.forward_trending_cas(
                    item['cas'], item['message_text'], item['source_group'], item['chat_id']
                )
            elif item_type == 'rescue':
                await forwarding_manager.forward_rescued_cas(
                    item['cas'], item['message_text'], item['source_group'], 
                    item['chat_id'], item['ca_detector']
                )
            else:
                logger.warning(f"Unknown queue item type: {item_type}")
                
        except Exception as e:
            logger.error(f"Error processing queue item: {e}")
    
    def stop_processing(self):
        """Stop queue processing."""
        self.processing = False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        return {
            **self.stats,
            'queue_size': self.queue.qsize(),
            'processing': self.processing
        }
