#!/usr/bin/env python3
"""
Diagnostic Script for CLA v2 Bot Freezing Issue
Identifies where the bot freezes during startup on PythonAnywhere
"""

import asyncio
import sys
import time
import signal
import os
from datetime import datetime

# Add project root to path
sys.path.append('.')

def print_step(step, description):
    """Print diagnostic step with timestamp."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] STEP {step}: {description}")

def timeout_handler(signum, frame):
    """Handle timeout for hanging operations."""
    print(f"\n❌ TIMEOUT: Operation took too long (>30 seconds)")
    print("This indicates where the bot is freezing")
    sys.exit(1)

async def test_database_connection():
    """Test database connection with timeout."""
    print_step(1, "Testing Database Connection")
    try:
        from src.database import DatabaseManager
        
        db = DatabaseManager()
        print("   📊 DatabaseManager created")
        
        await db.connect()
        print("   ✅ Database connected successfully")
        
        await db.close()
        print("   ✅ Database closed successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Database connection failed: {e}")
        return False

async def test_telegram_connection():
    """Test Telegram connection with timeout."""
    print_step(2, "Testing Telegram Connection")
    try:
        from src.telegram_client import TelegramClientManager
        
        print("   📱 Creating TelegramClientManager...")
        client_manager = TelegramClientManager()
        print("   ✅ TelegramClientManager created")
        
        print("   📱 Connecting to Telegram...")
        # This is likely where it freezes
        await client_manager.connect()
        print("   ✅ Telegram connected successfully")
        
        print("   📱 Getting user info...")
        me = await client_manager.client.get_me()
        print(f"   ✅ Connected as: {me.first_name} (@{me.username})")
        
        print("   📱 Disconnecting...")
        await client_manager.disconnect()
        print("   ✅ Telegram disconnected successfully")
        return True
        
    except Exception as e:
        print(f"   ❌ Telegram connection failed: {e}")
        import traceback
        print(f"   📋 Traceback: {traceback.format_exc()}")
        return False

async def test_group_access():
    """Test access to monitored groups."""
    print_step(3, "Testing Group Access")
    try:
        from src.telegram_client import TelegramClientManager
        from config import config
        
        client_manager = TelegramClientManager()
        await client_manager.connect()
        
        # Test access to a few key groups
        test_groups = [
            -1002380594298,  # FREE WHALE SIGNALS
            -1002202241417,  # GMGN Featured Signals
            -1002333406905   # MEME 1000X
        ]
        
        for group_id in test_groups:
            try:
                entity = await client_manager.client.get_entity(group_id)
                print(f"   ✅ Access to {getattr(entity, 'title', 'Unknown')} ({group_id})")
            except Exception as e:
                print(f"   ❌ No access to group {group_id}: {e}")
        
        await client_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"   ❌ Group access test failed: {e}")
        return False

async def test_bot_initialization():
    """Test bot initialization step by step."""
    print_step(4, "Testing Bot Initialization")
    try:
        from src.database import DatabaseManager
        from src.bot import CLABot
        
        print("   📊 Creating DatabaseManager...")
        db = DatabaseManager()
        await db.connect()
        print("   ✅ Database connected")
        
        print("   🤖 Creating CLABot...")
        bot = CLABot(db)
        print("   ✅ CLABot created")
        
        print("   🔧 Testing bot initialization (without starting)...")
        # Don't actually initialize to avoid hanging
        print("   ⚠️ Skipping full initialization to avoid hang")
        
        await db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Bot initialization test failed: {e}")
        import traceback
        print(f"   📋 Traceback: {traceback.format_exc()}")
        return False

def test_session_file():
    """Test Telegram session file."""
    print_step(5, "Testing Session File")
    
    session_files = [f for f in os.listdir('.') if f.startswith('cla_bot_session.session')]
    
    if not session_files:
        print("   ❌ No session file found")
        print("   💡 Run: python authenticate_telegram.py")
        return False
    
    session_file = session_files[0]
    print(f"   ✅ Session file found: {session_file}")
    
    # Check file size
    file_size = os.path.getsize(session_file)
    print(f"   📊 Session file size: {file_size} bytes")
    
    if file_size < 100:
        print("   ⚠️ Session file seems too small - might be corrupted")
        return False
    
    print("   ✅ Session file appears valid")
    return True

def test_network_connectivity():
    """Test network connectivity to Telegram servers."""
    print_step(6, "Testing Network Connectivity")
    
    import subprocess
    
    # Test basic connectivity
    try:
        result = subprocess.run(['ping', '-c', '3', 'telegram.org'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("   ✅ Can reach telegram.org")
        else:
            print("   ❌ Cannot reach telegram.org")
            return False
    except subprocess.TimeoutExpired:
        print("   ❌ Ping to telegram.org timed out")
        return False
    except Exception as e:
        print(f"   ⚠️ Ping test failed: {e}")
    
    # Test DNS resolution
    try:
        import socket
        socket.gethostbyname('telegram.org')
        print("   ✅ DNS resolution working")
    except Exception as e:
        print(f"   ❌ DNS resolution failed: {e}")
        return False
    
    return True

async def run_step_by_step_diagnosis():
    """Run comprehensive step-by-step diagnosis."""
    print("🔍 CLA v2 BOT FREEZE DIAGNOSIS")
    print("=" * 60)
    print("This script will identify where the bot freezes during startup")
    print("=" * 60)
    
    # Set timeout for each test
    signal.signal(signal.SIGALRM, timeout_handler)
    
    tests = [
        ("Session File Check", test_session_file, False),
        ("Network Connectivity", test_network_connectivity, False),
        ("Database Connection", test_database_connection, True),
        ("Telegram Connection", test_telegram_connection, True),
        ("Group Access", test_group_access, True),
        ("Bot Initialization", test_bot_initialization, True)
    ]
    
    results = []
    
    for test_name, test_func, is_async in tests:
        print(f"\n🧪 Running: {test_name}")
        
        try:
            # Set 30-second timeout for each test
            signal.alarm(30)
            
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            signal.alarm(0)  # Cancel timeout
            results.append((test_name, result))
            
            if result:
                print(f"   ✅ {test_name}: PASSED")
            else:
                print(f"   ❌ {test_name}: FAILED")
                
        except Exception as e:
            signal.alarm(0)  # Cancel timeout
            print(f"   ❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    # Recommendations
    print("\n" + "=" * 60)
    print("💡 RECOMMENDATIONS")
    print("=" * 60)
    
    if passed == len(results):
        print("✅ All tests passed - the issue might be intermittent")
        print("Try running the bot again with: python main.py")
    else:
        print("❌ Issues found - follow these steps:")
        
        for test_name, result in results:
            if not result:
                if "Session File" in test_name:
                    print("🔧 Fix session: python authenticate_telegram.py")
                elif "Network" in test_name:
                    print("🔧 Check PythonAnywhere network status")
                elif "Database" in test_name:
                    print("🔧 Check database permissions: chmod 755 data/")
                elif "Telegram" in test_name:
                    print("🔧 This is likely where the bot freezes!")
                    print("   - Try re-authenticating: rm cla_bot_session.session*")
                    print("   - Then run: python authenticate_telegram.py")
                elif "Group" in test_name:
                    print("🔧 Check group access permissions")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        asyncio.run(run_step_by_step_diagnosis())
    except KeyboardInterrupt:
        print("\n⚠️ Diagnosis interrupted by user")
    except Exception as e:
        print(f"\n❌ Diagnosis failed: {e}")
        import traceback
        traceback.print_exc()
