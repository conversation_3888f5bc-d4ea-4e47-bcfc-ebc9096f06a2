"""Trending analysis engine for high-volume CA detection and noise reduction."""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from collections import defaultdict, deque
from dataclasses import dataclass
from loguru import logger

from config import config
import json

@dataclass
class CAMention:
    """Represents a single CA mention."""
    ca: str
    timestamp: datetime
    group_id: int
    group_name: str
    message_id: int

@dataclass
class TrendingResult:
    """Result of trending analysis."""
    ca: str
    is_trending: bool
    mention_count: int
    first_mention: datetime
    latest_mention: datetime
    time_to_trend: Optional[timedelta]
    mentions: List[CAMention]

@dataclass
class SlowCookPattern:
    """Tracking data for potential slow cook CAs."""
    ca: str
    total_mentions: int
    first_mention: datetime
    last_mention: datetime
    time_span_hours: float
    groups_appeared: Set[int]
    mention_history: List[datetime]
    filtered_count: int
    last_filtered_timestamp: datetime

class TrendingAnalyzer:
    """Analyzes CA mentions to detect trending patterns and reduce noise."""
    
    def __init__(self):
        self.enabled = config.trending.enabled
        self.time_window_minutes = config.trending.time_window_minutes
        self.min_mentions = config.trending.min_mentions
        self.high_volume_groups = set(config.trending.high_volume_groups)
        self.low_volume_groups = set(config.trending.low_volume_groups)
        self.selective_protection = config.trending.selective_protection

        # Anti-pump-and-dump configuration
        self.min_time_spread_seconds = config.trending.min_time_spread_seconds
        self.max_velocity_mentions_per_minute = config.trending.max_velocity_mentions_per_minute
        self.min_organic_growth_minutes = config.trending.min_organic_growth_minutes
        self.require_time_distribution = config.trending.require_time_distribution
        self.pump_detection_enabled = config.trending.pump_detection_enabled
        
        # Storage for CA mentions within time windows
        self.ca_mentions: Dict[str, deque] = defaultdict(deque)
        
        # Track trending CAs to avoid re-processing
        self.trending_cas: Set[str] = set()
        self.trending_timestamps: Dict[str, datetime] = {}  # Track when CA became trending
        
        # Statistics
        self.stats = {
            'total_mentions': 0,
            'trending_qualified': 0,
            'noise_filtered': 0,
            'pump_schemes_blocked': 0,
            'organic_trends_passed': 0,
            'avg_time_to_trend': 0,
            'mentions_per_minute': 0,
            'last_reset': datetime.now()
        }
        
        # Performance tracking
        self.recent_mentions = deque(maxlen=1000)  # Track recent activity

        # PHASE 1: Slow Cook Pattern Tracking
        self.slow_cook_patterns: Dict[str, SlowCookPattern] = {}  # 24-hour CA tracking
        self.slow_cook_stats = {
            'multi_mention_filtered': 0,
            'slow_cook_candidates': 0,
            'cross_group_patterns': 0,
            'filtered_by_mention_count': defaultdict(int),  # count -> frequency
            'time_span_distribution': defaultdict(int),     # hours -> frequency
            'rescue_eligible_multi_mention': 0
        }

        logger.info(f"Trending Analyzer initialized: enabled={self.enabled}, window={self.time_window_minutes}min, min_mentions={self.min_mentions}")
        logger.info(f"Anti-pump protection: min_spread={self.min_time_spread_seconds}s, max_velocity={self.max_velocity_mentions_per_minute}/min, organic_growth={self.min_organic_growth_minutes}min")
        logger.info(f"Selective protection: enabled={self.selective_protection}")
        logger.info(f"High-volume groups (trending+anti-pump): {list(self.high_volume_groups)}")
        logger.info(f"Low-volume groups (direct forwarding): {list(self.low_volume_groups)}")
        logger.info(f"🐌 SLOW COOK TRACKING: Phase 1 pattern analysis enabled")
    
    def requires_trending_analysis(self, group_id: int) -> bool:
        """Check if a group requires trending analysis and anti-pump protection."""
        if not self.selective_protection:
            # If selective protection is disabled, all groups use trending analysis
            return group_id in self.high_volume_groups

        # With selective protection enabled:
        # - High-volume groups require trending analysis
        # - Low-volume groups use direct forwarding
        return group_id in self.high_volume_groups

    def is_high_volume_group(self, group_id: int) -> bool:
        """Check if a group is classified as high-volume."""
        return group_id in self.high_volume_groups

    def is_low_volume_group(self, group_id: int) -> bool:
        """Check if a group is classified as low-volume."""
        return group_id in self.low_volume_groups

    def should_forward_to_destination(self, ca: str, destination: str, source_group_id: int) -> bool:
        """Determine if a CA should be forwarded to a destination based on selective protection."""
        # FIRST: Check if destination is excluded (overrides everything)
        excluded_destinations = getattr(config.trending, 'exclude_destinations', [])

        if destination.upper() in [dest.upper() for dest in excluded_destinations]:
            return False

        # SECOND: Apply group-specific forwarding logic
        # For high-volume groups: only forward trending CAs
        if self.is_high_volume_group(source_group_id):
            return ca in self.trending_cas

        # For low-volume groups: forward all CAs (direct forwarding)
        if self.is_low_volume_group(source_group_id):
            return True

        # For unclassified groups: use trending analysis
        return ca in self.trending_cas

    async def analyze_ca_mention(self, ca: str, group_id: int, group_name: str, message_id: int) -> TrendingResult:
        """Analyze a CA mention and determine if it's trending (selective protection)."""
        now = datetime.now()

        # Check if this group requires trending analysis
        if not self.requires_trending_analysis(group_id):
            # Low-volume group: Direct forwarding without trending analysis
            logger.debug(f"Low-volume group {group_name} ({group_id}): Direct forwarding CA {ca}")
            return TrendingResult(
                ca=ca,
                is_trending=True,  # Always trending for low-volume groups
                mention_count=1,
                first_mention=now,
                latest_mention=now,
                time_to_trend=timedelta(0),
                mentions=[CAMention(ca=ca, timestamp=now, group_id=group_id, group_name=group_name, message_id=message_id)]
            )

        # High-volume group: Apply full trending analysis and anti-pump protection
        logger.debug(f"High-volume group {group_name} ({group_id}): Applying trending analysis for CA {ca}")

        # Check if CA is already trending (prevent re-processing)
        if ca in self.trending_cas:
            # Return existing trending result without re-processing
            mentions = list(self.ca_mentions.get(ca, []))
            if mentions:
                return TrendingResult(
                    ca=ca,
                    is_trending=True,
                    mention_count=len(mentions),
                    first_mention=mentions[0].timestamp,
                    latest_mention=mentions[-1].timestamp,
                    time_to_trend=self.trending_timestamps.get(ca, now) - mentions[0].timestamp if mentions else None,
                    mentions=mentions
                )

        # Create mention record
        mention = CAMention(
            ca=ca,
            timestamp=now,
            group_id=group_id,
            group_name=group_name,
            message_id=message_id
        )

        # Update statistics
        self.stats['total_mentions'] += 1
        self.recent_mentions.append(now)

        # Add to mentions tracking
        self.ca_mentions[ca].append(mention)

        # Clean old mentions outside time window
        await self._cleanup_old_mentions(ca)

        # Analyze trending status
        result = await self._analyze_trending_status(ca)

        # Log group activity based on protection level
        if self.is_high_volume_group(group_id):
            logger.debug(f"High-volume group mention: {ca} | Count: {result.mention_count} | Trending: {result.is_trending}")
        elif self.is_low_volume_group(group_id):
            logger.debug(f"Low-volume group mention: {ca} | Direct forwarding: {result.is_trending}")

        return result
    
    async def _cleanup_old_mentions(self, ca: str):
        """Remove mentions older than the time window."""
        cutoff_time = datetime.now() - timedelta(minutes=self.time_window_minutes)
        mentions = self.ca_mentions[ca]
        
        # Remove old mentions
        while mentions and mentions[0].timestamp < cutoff_time:
            mentions.popleft()
        
        # Clean up empty entries
        if not mentions:
            del self.ca_mentions[ca]
            self.trending_cas.discard(ca)
            if ca in self.trending_timestamps:
                del self.trending_timestamps[ca]
    
    async def _detect_pump_and_dump_pattern(self, mentions: List[CAMention]) -> tuple[bool, str]:
        """Detect if mention pattern indicates pump-and-dump scheme."""
        if not self.pump_detection_enabled or len(mentions) < 2:
            return False, "insufficient_data"

        timestamps = [m.timestamp for m in mentions]
        first_time = timestamps[0]
        last_time = timestamps[-1]
        total_duration = (last_time - first_time).total_seconds()

        # Check 1: Too fast (mentions too close together)
        if total_duration < self.min_time_spread_seconds:
            return True, f"too_fast_spread_{total_duration:.1f}s"

        # Check 2: Velocity too high (too many mentions per minute)
        if total_duration > 0:
            velocity = (len(mentions) - 1) / (total_duration / 60)  # mentions per minute
            if velocity > self.max_velocity_mentions_per_minute:
                return True, f"high_velocity_{velocity:.1f}/min"

        # Check 3: Must span minimum organic growth time
        if total_duration < (self.min_organic_growth_minutes * 60):
            return True, f"insufficient_growth_time_{total_duration/60:.1f}min"

        # Check 4: Time distribution (mentions should be somewhat spread out)
        if self.require_time_distribution and len(mentions) >= 4:
            # Check if mentions are too clustered (more than 60% in first 30% of time window)
            early_window = total_duration * 0.3
            early_mentions = sum(1 for t in timestamps if (t - first_time).total_seconds() <= early_window)
            cluster_threshold = len(mentions) * 0.6
            if early_mentions > cluster_threshold:
                return True, f"clustered_mentions_{early_mentions}/{len(mentions)}_in_first_30pct"

        return False, "organic_pattern"

    async def _analyze_trending_status(self, ca: str) -> TrendingResult:
        """Analyze if a CA is trending based on mention frequency."""
        mentions = list(self.ca_mentions.get(ca, []))
        mention_count = len(mentions)
        
        if not mentions:
            return TrendingResult(
                ca=ca,
                is_trending=False,
                mention_count=0,
                first_mention=datetime.now(),
                latest_mention=datetime.now(),
                time_to_trend=None,
                mentions=[]
            )
        
        first_mention = mentions[0].timestamp
        latest_mention = mentions[-1].timestamp
        
        # Check if meets minimum mention threshold
        meets_threshold = (
            self.enabled and
            mention_count >= self.min_mentions and
            ca not in self.trending_cas
        )

        # Enhanced pump-and-dump detection
        is_pump_scheme = False
        pump_reason = "none"

        if meets_threshold:
            is_pump_scheme, pump_reason = await self._detect_pump_and_dump_pattern(mentions)

        # Final trending decision: meets threshold AND not a pump scheme
        is_trending = meets_threshold and not is_pump_scheme

        time_to_trend = None
        if meets_threshold and is_pump_scheme:
            # Blocked pump scheme
            self.stats['pump_schemes_blocked'] += 1
            logger.warning(f"🚫 PUMP SCHEME BLOCKED: {ca} | Mentions: {mention_count} | Reason: {pump_reason}")
        elif is_trending:
            # Legitimate trending
            time_to_trend = latest_mention - first_mention
            self.trending_cas.add(ca)
            self.trending_timestamps[ca] = latest_mention
            self.stats['trending_qualified'] += 1
            self.stats['organic_trends_passed'] += 1

            # Update average time to trend
            self._update_avg_time_to_trend(time_to_trend)

            logger.info(f"🔥 ORGANIC TRENDING DETECTED: {ca} | Mentions: {mention_count} | Time to trend: {time_to_trend} | Pattern: {pump_reason}")
        else:
            if mention_count < self.min_mentions:
                self.stats['noise_filtered'] += 1

            # PHASE 1: Enhanced logging for filtered CAs
            await self._track_slow_cook_pattern(ca, mentions, mention_count, first_mention, latest_mention)
        
        return TrendingResult(
            ca=ca,
            is_trending=is_trending,
            mention_count=mention_count,
            first_mention=first_mention,
            latest_mention=latest_mention,
            time_to_trend=time_to_trend,
            mentions=mentions.copy()
        )
    
    def _update_avg_time_to_trend(self, time_to_trend: timedelta):
        """Update average time to trend statistic."""
        current_avg = self.stats['avg_time_to_trend']
        trending_count = self.stats['trending_qualified']

        if trending_count == 1:
            self.stats['avg_time_to_trend'] = time_to_trend.total_seconds()
        else:
            # Running average
            total_seconds = current_avg * (trending_count - 1) + time_to_trend.total_seconds()
            self.stats['avg_time_to_trend'] = total_seconds / trending_count

    async def _track_slow_cook_pattern(self, ca: str, mentions: List[CAMention],
                                     mention_count: int, first_mention: datetime,
                                     latest_mention: datetime):
        """PHASE 1: Track slow cook patterns for filtered CAs."""
        try:
            # Calculate time span
            time_span = (latest_mention - first_mention).total_seconds() / 3600  # hours

            # Update slow cook statistics
            self.slow_cook_stats['filtered_by_mention_count'][mention_count] += 1
            time_span_bucket = int(time_span) if time_span < 24 else 24  # Cap at 24 hours
            self.slow_cook_stats['time_span_distribution'][time_span_bucket] += 1

            # Track multi-mention filtered CAs
            if mention_count > 1:
                self.slow_cook_stats['multi_mention_filtered'] += 1

                # Enhanced logging for multi-mention filtered CAs
                groups_appeared = set(m.group_id for m in mentions)
                group_names = [m.group_name for m in mentions]

                logger.info(f"🐌 SLOW COOK FILTERED: {ca} | Mentions: {mention_count} | "
                          f"Time span: {time_span:.1f}h | Groups: {len(groups_appeared)} | "
                          f"First: {first_mention.strftime('%H:%M:%S')} | "
                          f"Last: {latest_mention.strftime('%H:%M:%S')}")

                # Track cross-group patterns
                if len(groups_appeared) > 1:
                    self.slow_cook_stats['cross_group_patterns'] += 1
                    logger.info(f"🔄 CROSS-GROUP SLOW COOK: {ca} appeared in {len(groups_appeared)} groups: {group_names}")

                # Update or create slow cook pattern tracking
                await self._update_slow_cook_tracking(ca, mentions, time_span, groups_appeared)

                # Check for slow cook candidate criteria
                if mention_count >= 5 and time_span >= 1.0:  # 5+ mentions over 1+ hours
                    self.slow_cook_stats['slow_cook_candidates'] += 1
                    logger.warning(f"🎯 SLOW COOK CANDIDATE: {ca} | {mention_count} mentions over {time_span:.1f}h | "
                                 f"Potentially missed trend!")

        except Exception as e:
            logger.error(f"Error tracking slow cook pattern for {ca}: {e}")

    async def _update_slow_cook_tracking(self, ca: str, mentions: List[CAMention],
                                       time_span_hours: float, groups_appeared: Set[int]):
        """Update 24-hour slow cook pattern tracking."""
        try:
            now = datetime.now()
            mention_timestamps = [m.timestamp for m in mentions]

            if ca in self.slow_cook_patterns:
                # Update existing pattern
                pattern = self.slow_cook_patterns[ca]
                pattern.total_mentions += len(mentions)
                pattern.last_mention = max(mention_timestamps)
                pattern.time_span_hours = (pattern.last_mention - pattern.first_mention).total_seconds() / 3600
                pattern.groups_appeared.update(groups_appeared)
                pattern.mention_history.extend(mention_timestamps)
                pattern.filtered_count += 1
                pattern.last_filtered_timestamp = now
            else:
                # Create new pattern
                self.slow_cook_patterns[ca] = SlowCookPattern(
                    ca=ca,
                    total_mentions=len(mentions),
                    first_mention=min(mention_timestamps),
                    last_mention=max(mention_timestamps),
                    time_span_hours=time_span_hours,
                    groups_appeared=groups_appeared.copy(),
                    mention_history=mention_timestamps.copy(),
                    filtered_count=1,
                    last_filtered_timestamp=now
                )

            # Log pattern update
            pattern = self.slow_cook_patterns[ca]
            logger.debug(f"📊 SLOW COOK PATTERN UPDATED: {ca} | Total mentions: {pattern.total_mentions} | "
                        f"Span: {pattern.time_span_hours:.1f}h | Groups: {len(pattern.groups_appeared)} | "
                        f"Filtered: {pattern.filtered_count} times")

        except Exception as e:
            logger.error(f"Error updating slow cook tracking for {ca}: {e}")
    

    
    def get_trending_stats(self) -> Dict:
        """Get trending analysis statistics."""
        now = datetime.now()
        
        # Calculate mentions per minute
        recent_cutoff = now - timedelta(minutes=1)
        recent_count = sum(1 for mention_time in self.recent_mentions if mention_time > recent_cutoff)
        
        # Calculate noise reduction percentage
        total_mentions = self.stats['total_mentions']
        noise_filtered = self.stats['noise_filtered']
        noise_reduction_pct = (noise_filtered / total_mentions * 100) if total_mentions > 0 else 0
        
        # Calculate trending success rate
        trending_qualified = self.stats['trending_qualified']
        trending_rate = (trending_qualified / total_mentions * 100) if total_mentions > 0 else 0
        
        return {
            'enabled': self.enabled,
            'time_window_minutes': self.time_window_minutes,
            'min_mentions_threshold': self.min_mentions,
            'total_mentions': total_mentions,
            'trending_qualified': trending_qualified,
            'noise_filtered': noise_filtered,
            'noise_reduction_percentage': round(noise_reduction_pct, 2),
            'trending_rate_percentage': round(trending_rate, 2),
            'avg_time_to_trend_seconds': round(self.stats['avg_time_to_trend'], 2),
            'mentions_per_minute': recent_count,
            'active_cas_tracking': len(self.ca_mentions),
            'trending_cas_count': len(self.trending_cas),
            'high_volume_groups': list(self.high_volume_groups)
        }
    
    def get_ca_mention_history(self, ca: str) -> List[Dict]:
        """Get mention history for a specific CA."""
        mentions = self.ca_mentions.get(ca, [])
        return [
            {
                'timestamp': mention.timestamp.isoformat(),
                'group_id': mention.group_id,
                'group_name': mention.group_name,
                'message_id': mention.message_id
            }
            for mention in mentions
        ]
    
    async def cleanup_expired_data(self):
        """Clean up expired data and reset statistics periodically."""
        now = datetime.now()
        cutoff_time = now - timedelta(minutes=self.time_window_minutes)
        
        # SAFE ITERATION FIX: Clean up old mentions without modifying dict during iteration
        expired_cas = []

        # First pass: collect expired CAs and clean mentions
        for ca, mentions in list(self.ca_mentions.items()):
            while mentions and mentions[0].timestamp < cutoff_time:
                mentions.popleft()

            if not mentions:
                expired_cas.append(ca)

        # Second pass: safely remove expired CAs
        for ca in expired_cas:
            if ca in self.ca_mentions:
                del self.ca_mentions[ca]
            self.trending_cas.discard(ca)

        # PHASE 1: Clean up slow cook patterns older than 24 hours
        await self._cleanup_slow_cook_patterns()

        # Reset statistics hourly with enhanced slow cook reporting
        if now - self.stats['last_reset'] > timedelta(hours=1):
            await self._log_slow_cook_statistics()
            logger.info(f"📊 Trending Stats Reset - Processed: {self.stats['total_mentions']} mentions, Trending: {self.stats['trending_qualified']}")
            self.stats = {
                'total_mentions': 0,
                'trending_qualified': 0,
                'noise_filtered': 0,
                'avg_time_to_trend': 0,
                'mentions_per_minute': 0,
                'last_reset': now
            }
            # Reset slow cook stats but preserve patterns
            self.slow_cook_stats = {
                'multi_mention_filtered': 0,
                'slow_cook_candidates': 0,
                'cross_group_patterns': 0,
                'filtered_by_mention_count': defaultdict(int),
                'time_span_distribution': defaultdict(int),
                'rescue_eligible_multi_mention': 0
            }
    
    def is_high_volume_group(self, group_id: int) -> bool:
        """Check if a group is configured as high-volume."""
        return group_id in self.high_volume_groups
    
    def get_trending_cas(self) -> Set[str]:
        """Get currently trending CAs."""
        return self.trending_cas.copy()

    async def _cleanup_slow_cook_patterns(self):
        """Clean up slow cook patterns older than 24 hours."""
        try:
            now = datetime.now()
            cutoff_time = now - timedelta(hours=24)
            expired_patterns = []

            for ca, pattern in self.slow_cook_patterns.items():
                if pattern.last_filtered_timestamp < cutoff_time:
                    expired_patterns.append(ca)

            for ca in expired_patterns:
                del self.slow_cook_patterns[ca]

            if expired_patterns:
                logger.debug(f"🧹 SLOW COOK CLEANUP: Removed {len(expired_patterns)} expired patterns")

        except Exception as e:
            logger.error(f"Error cleaning up slow cook patterns: {e}")

    async def _log_slow_cook_statistics(self):
        """Log comprehensive slow cook statistics."""
        try:
            stats = self.slow_cook_stats
            patterns = self.slow_cook_patterns

            logger.info(f"🐌 SLOW COOK HOURLY REPORT:")
            logger.info(f"   📊 Multi-mention filtered: {stats['multi_mention_filtered']}")
            logger.info(f"   🎯 Slow cook candidates: {stats['slow_cook_candidates']}")
            logger.info(f"   🔄 Cross-group patterns: {stats['cross_group_patterns']}")
            logger.info(f"   📈 Active 24h patterns: {len(patterns)}")

            # Log mention count distribution
            if stats['filtered_by_mention_count']:
                mention_dist = dict(sorted(stats['filtered_by_mention_count'].items()))
                logger.info(f"   📋 Mention count distribution: {mention_dist}")

            # Log time span distribution
            if stats['time_span_distribution']:
                time_dist = dict(sorted(stats['time_span_distribution'].items()))
                logger.info(f"   ⏰ Time span distribution (hours): {time_dist}")

            # Log top slow cook patterns
            if patterns:
                sorted_patterns = sorted(patterns.values(),
                                       key=lambda p: p.total_mentions, reverse=True)[:5]
                logger.info(f"   🏆 Top slow cook patterns:")
                for i, pattern in enumerate(sorted_patterns, 1):
                    logger.info(f"      {i}. {pattern.ca[:8]}... | "
                              f"{pattern.total_mentions} mentions | "
                              f"{pattern.time_span_hours:.1f}h | "
                              f"{len(pattern.groups_appeared)} groups")

        except Exception as e:
            logger.error(f"Error logging slow cook statistics: {e}")

    def get_slow_cook_stats(self) -> Dict:
        """Get slow cook pattern statistics."""
        return {
            'slow_cook_stats': dict(self.slow_cook_stats),
            'active_patterns': len(self.slow_cook_patterns),
            'pattern_details': [
                {
                    'ca': pattern.ca,
                    'total_mentions': pattern.total_mentions,
                    'time_span_hours': pattern.time_span_hours,
                    'groups_count': len(pattern.groups_appeared),
                    'filtered_count': pattern.filtered_count,
                    'first_mention': pattern.first_mention.isoformat(),
                    'last_mention': pattern.last_mention.isoformat()
                }
                for pattern in sorted(self.slow_cook_patterns.values(),
                                    key=lambda p: p.total_mentions, reverse=True)[:10]
            ]
        }
