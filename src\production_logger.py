"""Production logging configuration for CLA v2.0 Bot."""

import os
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from loguru import logger

class ProductionLogger:
    """Production-ready logging configuration with structured logs and rotation."""
    
    def __init__(self):
        self.log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.log_format = os.getenv('LOG_FORMAT', 'structured')

        # Use local paths for development, production paths for production
        environment = os.getenv('ENVIRONMENT', 'development')
        if environment == 'production':
            self.log_file_path = os.getenv('LOG_FILE_PATH', '/opt/cla-bot/logs/cla_bot.log')
        else:
            self.log_file_path = os.getenv('LOG_FILE_PATH', './logs/cla_bot.log')

        self.log_max_size = f"{os.getenv('LOG_MAX_SIZE_MB', '100')} MB"
        self.log_backup_count = int(os.getenv('LOG_BACKUP_COUNT', '10'))
        self.log_rotation_when = os.getenv('LOG_ROTATION_WHEN', 'midnight')

        # Ensure log directory exists with proper error handling
        try:
            Path(self.log_file_path).parent.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            # Fall back to local directory if permission denied
            self.log_file_path = './logs/cla_bot.log'
            Path(self.log_file_path).parent.mkdir(parents=True, exist_ok=True)
        
    def setup_production_logging(self):
        """Configure production logging with structured format and rotation."""
        # Remove default logger
        logger.remove()
        
        # Console logging (human-readable for development, structured for production)
        environment = os.getenv('ENVIRONMENT', 'development')
        if environment == 'production' and self.log_format == 'structured':
            console_format = self._get_structured_format()
            use_serialize = True
            use_colorize = False
        else:
            console_format = self._get_human_readable_format()
            use_serialize = False
            use_colorize = True

        # Add console handler
        logger.add(
            sys.stdout,
            format=console_format,
            level=self.log_level,
            colorize=use_colorize,
            serialize=use_serialize
        )
        
        # Add file handler with rotation (always use human-readable in development)
        file_format = self._get_human_readable_format()
        logger.add(
            self.log_file_path,
            format=file_format,
            level=self.log_level,
            rotation=self.log_max_size,
            retention=self.log_backup_count,  # Just the number, not "files"
            compression="gz",
            serialize=False,  # No serialization in development
            enqueue=True  # Thread-safe logging
        )
        
        # Add error file handler
        error_log_path = str(Path(self.log_file_path).with_suffix('.error.log'))
        logger.add(
            error_log_path,
            format=file_format,
            level="ERROR",
            rotation=self.log_max_size,
            retention=self.log_backup_count,  # Just the number, not "files"
            compression="gz",
            serialize=False,  # No serialization in development
            enqueue=True
        )
        
        # Log startup information
        logger.info("Production logging configured", extra={
            "log_level": self.log_level,
            "log_format": self.log_format,
            "log_file": self.log_file_path,
            "max_size": self.log_max_size,
            "backup_count": self.log_backup_count
        })
    
    def _get_structured_format(self) -> str:
        """Get structured JSON format for production logs."""
        # Don't use json.dumps() as it adds extra quotes that break loguru formatting
        return (
            '{'
            '"timestamp": "{time:YYYY-MM-DD HH:mm:ss.SSS}", '
            '"level": "{level}", '
            '"logger": "{name}", '
            '"message": "{message}", '
            '"module": "{module}", '
            '"function": "{function}", '
            '"line": "{line}", '
            '"process_id": "{process}", '
            '"thread_id": "{thread}", '
            '"extra": "{extra}"'
            '}'
        )
    
    def _get_human_readable_format(self) -> str:
        """Get human-readable format for development."""
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    def add_performance_context(self, operation: str, duration_ms: float, **kwargs):
        """Add performance context to logs."""
        context = {
            "operation": operation,
            "duration_ms": round(duration_ms, 2),
            "performance_category": self._categorize_performance(duration_ms),
            **kwargs
        }
        return context
    
    def add_business_context(self, event_type: str, **kwargs):
        """Add business logic context to logs."""
        context = {
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        return context
    
    def _categorize_performance(self, duration_ms: float) -> str:
        """Categorize performance for monitoring."""
        if duration_ms < 10:
            return "excellent"
        elif duration_ms < 50:
            return "good"
        elif duration_ms < 100:
            return "acceptable"
        elif duration_ms < 500:
            return "slow"
        else:
            return "critical"

class StructuredLogger:
    """Wrapper for structured logging with business context."""
    
    def __init__(self):
        self.production_logger = ProductionLogger()
        
    def log_ca_detection(self, ca: str, source_group: str, detection_time_ms: float, **kwargs):
        """Log CA detection with structured context."""
        context = {
            "event_type": "ca_detection",
            "ca": ca,
            "source_group": source_group,
            "detection_time_ms": round(detection_time_ms, 2),
            **kwargs
        }
        logger.info("CA detected", extra=context)
    
    def log_message_processing(self, group_name: str, message_id: int, 
                             processing_time_ms: float, ca_count: int, **kwargs):
        """Log message processing with performance metrics."""
        context = {
            "event_type": "message_processing",
            "group_name": group_name,
            "message_id": message_id,
            "processing_time_ms": round(processing_time_ms, 2),
            "ca_count": ca_count,
            "performance_category": self.production_logger._categorize_performance(processing_time_ms),
            **kwargs
        }
        logger.info("Message processed", extra=context)
    
    def log_forwarding_result(self, destination: str, ca: str, success: bool, 
                            response_time_ms: float = None, **kwargs):
        """Log forwarding results with success metrics."""
        context = {
            "event_type": "ca_forwarding",
            "destination": destination,
            "ca": ca,
            "success": success,
            **kwargs
        }
        if response_time_ms is not None:
            context["response_time_ms"] = round(response_time_ms, 2)
        
        if success:
            logger.info("CA forwarded successfully", extra=context)
        else:
            logger.warning("CA forwarding failed", extra=context)
    
    def log_trending_analysis(self, ca: str, is_trending: bool, mention_count: int, 
                            analysis_time_ms: float, **kwargs):
        """Log trending analysis results."""
        context = {
            "event_type": "trending_analysis",
            "ca": ca,
            "is_trending": is_trending,
            "mention_count": mention_count,
            "analysis_time_ms": round(analysis_time_ms, 2),
            **kwargs
        }
        logger.info("Trending analysis completed", extra=context)
    
    def log_database_operation(self, operation: str, success: bool, 
                             duration_ms: float, affected_rows: int = None, **kwargs):
        """Log database operations with performance metrics."""
        context = {
            "event_type": "database_operation",
            "operation": operation,
            "success": success,
            "duration_ms": round(duration_ms, 2),
            **kwargs
        }
        if affected_rows is not None:
            context["affected_rows"] = affected_rows
        
        if success:
            logger.debug("Database operation completed", extra=context)
        else:
            logger.error("Database operation failed", extra=context)
    
    def log_system_health(self, component: str, status: str, metrics: Dict[str, Any] = None):
        """Log system health metrics."""
        context = {
            "event_type": "system_health",
            "component": component,
            "status": status,
            "timestamp": datetime.now().isoformat()
        }
        if metrics:
            context["metrics"] = metrics
        
        if status == "healthy":
            logger.info("System health check", extra=context)
        elif status == "degraded":
            logger.warning("System health degraded", extra=context)
        else:
            logger.error("System health critical", extra=context)
    
    def log_error_with_context(self, error: Exception, operation: str, **kwargs):
        """Log errors with full context for debugging."""
        context = {
            "event_type": "error",
            "operation": operation,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        logger.error(f"Error in {operation}: {error}", extra=context)

# Global structured logger instance
structured_logger = StructuredLogger()

def setup_production_logging():
    """Setup production logging configuration."""
    production_logger = ProductionLogger()
    production_logger.setup_production_logging()
    
    # Log environment information
    logger.info("Production environment initialized", extra={
        "environment": os.getenv('ENVIRONMENT', 'production'),
        "deployment_version": os.getenv('DEPLOYMENT_VERSION', 'unknown'),
        "working_directory": os.getcwd(),
        "python_version": sys.version,
        "log_configuration": {
            "level": production_logger.log_level,
            "format": production_logger.log_format,
            "file_path": production_logger.log_file_path
        }
    })

if __name__ == "__main__":
    # Test logging configuration
    setup_production_logging()
    
    # Test different log levels and contexts
    logger.debug("Debug message for development")
    logger.info("Info message for general information")
    logger.warning("Warning message for potential issues")
    logger.error("Error message for failures")
    
    # Test structured logging
    structured_logger.log_ca_detection(
        ca="7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk",
        source_group="BUGSIE",
        detection_time_ms=1.5,
        extraction_method="axiom_url"
    )
    
    structured_logger.log_system_health(
        component="telegram_client",
        status="healthy",
        metrics={"connection_count": 1, "message_rate": 15.2}
    )
    
    print("✅ Production logging test completed")
