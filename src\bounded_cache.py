"""Bounded cache implementations with LRU eviction to prevent memory leaks."""

import asyncio
from collections import OrderedDict
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Set, Tuple
from loguru import logger

class BoundedLRUCache:
    """Thread-safe bounded cache with LRU eviction policy."""
    
    def __init__(self, max_size: int, name: str = "Cache"):
        """Initialize bounded cache with maximum size."""
        self.max_size = max_size
        self.name = name
        self.cache = OrderedDict()
        self.lock = asyncio.Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size': 0
        }
        
        logger.info(f"Initialized {name} with max size: {max_size}")
    
    async def get(self, key: Any) -> Optional[Any]:
        """Get item from cache, moving it to end (most recently used)."""
        async with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                value = self.cache.pop(key)
                self.cache[key] = value
                self.stats['hits'] += 1
                return value
            else:
                self.stats['misses'] += 1
                return None
    
    async def put(self, key: Any, value: Any) -> bool:
        """Put item in cache, evicting LRU items if necessary."""
        async with self.lock:
            # If key exists, update and move to end
            if key in self.cache:
                self.cache.pop(key)
                self.cache[key] = value
                return True
            
            # If at capacity, remove LRU item
            if len(self.cache) >= self.max_size:
                lru_key = next(iter(self.cache))
                self.cache.pop(lru_key)
                self.stats['evictions'] += 1
                logger.debug(f"{self.name}: Evicted LRU item {lru_key}")
            
            # Add new item
            self.cache[key] = value
            self.stats['size'] = len(self.cache)
            return True
    
    async def contains(self, key: Any) -> bool:
        """Check if key exists in cache without updating LRU order."""
        async with self.lock:
            return key in self.cache
    
    async def remove(self, key: Any) -> bool:
        """Remove item from cache."""
        async with self.lock:
            if key in self.cache:
                self.cache.pop(key)
                self.stats['size'] = len(self.cache)
                return True
            return False
    
    async def clear(self):
        """Clear all items from cache."""
        async with self.lock:
            self.cache.clear()
            self.stats['size'] = 0
            logger.info(f"{self.name}: Cache cleared")
    
    async def size(self) -> int:
        """Get current cache size."""
        async with self.lock:
            return len(self.cache)
    
    async def get_stats(self) -> Dict:
        """Get cache statistics."""
        async with self.lock:
            hit_rate = (self.stats['hits'] / max(1, self.stats['hits'] + self.stats['misses'])) * 100
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'current_size': len(self.cache),
                'max_size': self.max_size,
                'utilization': (len(self.cache) / self.max_size) * 100
            }

class BoundedTimeCache:
    """Bounded cache with time-based expiration and LRU eviction."""
    
    def __init__(self, max_size: int, ttl_seconds: int, name: str = "TimeCache"):
        """Initialize bounded time cache."""
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.name = name
        self.cache = OrderedDict()  # key -> (value, timestamp)
        self.lock = asyncio.Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expirations': 0,
            'size': 0
        }
        
        logger.info(f"Initialized {name} with max size: {max_size}, TTL: {ttl_seconds}s")
    
    def _is_expired(self, timestamp: datetime) -> bool:
        """Check if timestamp is expired."""
        return (datetime.now() - timestamp).total_seconds() > self.ttl_seconds
    
    async def _cleanup_expired(self):
        """Remove expired entries."""
        expired_keys = []
        for key, (value, timestamp) in self.cache.items():
            if self._is_expired(timestamp):
                expired_keys.append(key)
        
        for key in expired_keys:
            self.cache.pop(key)
            self.stats['expirations'] += 1
        
        if expired_keys:
            logger.debug(f"{self.name}: Cleaned up {len(expired_keys)} expired entries")
    
    async def get(self, key: Any) -> Optional[Any]:
        """Get item from cache if not expired."""
        async with self.lock:
            await self._cleanup_expired()
            
            if key in self.cache:
                value, timestamp = self.cache[key]
                if not self._is_expired(timestamp):
                    # Move to end (most recently used)
                    self.cache.pop(key)
                    self.cache[key] = (value, timestamp)
                    self.stats['hits'] += 1
                    return value
                else:
                    # Remove expired item
                    self.cache.pop(key)
                    self.stats['expirations'] += 1
            
            self.stats['misses'] += 1
            return None
    
    async def put(self, key: Any, value: Any) -> bool:
        """Put item in cache with current timestamp."""
        async with self.lock:
            await self._cleanup_expired()
            
            timestamp = datetime.now()
            
            # If key exists, update
            if key in self.cache:
                self.cache.pop(key)
                self.cache[key] = (value, timestamp)
                return True
            
            # If at capacity, remove LRU item
            if len(self.cache) >= self.max_size:
                lru_key = next(iter(self.cache))
                self.cache.pop(lru_key)
                self.stats['evictions'] += 1
                logger.debug(f"{self.name}: Evicted LRU item {lru_key}")
            
            # Add new item
            self.cache[key] = (value, timestamp)
            self.stats['size'] = len(self.cache)
            return True
    
    async def contains(self, key: Any) -> bool:
        """Check if non-expired key exists in cache."""
        async with self.lock:
            if key in self.cache:
                value, timestamp = self.cache[key]
                if not self._is_expired(timestamp):
                    return True
                else:
                    # Remove expired item
                    self.cache.pop(key)
                    self.stats['expirations'] += 1
            return False
    
    async def cleanup(self):
        """Manual cleanup of expired entries."""
        async with self.lock:
            await self._cleanup_expired()
            self.stats['size'] = len(self.cache)

class BoundedSetCache:
    """Bounded set cache for tracking processed items."""
    
    def __init__(self, max_size: int, name: str = "SetCache"):
        """Initialize bounded set cache."""
        self.max_size = max_size
        self.name = name
        self.items = OrderedDict()  # Use OrderedDict to track insertion order
        self.lock = asyncio.Lock()
        self.stats = {
            'additions': 0,
            'evictions': 0,
            'size': 0
        }
        
        logger.info(f"Initialized {name} set with max size: {max_size}")
    
    async def add(self, item: Any) -> bool:
        """Add item to set, evicting oldest if necessary."""
        async with self.lock:
            # If item already exists, move to end
            if item in self.items:
                self.items.pop(item)
                self.items[item] = True
                return False  # Item already existed
            
            # If at capacity, remove oldest item
            if len(self.items) >= self.max_size:
                oldest_item = next(iter(self.items))
                self.items.pop(oldest_item)
                self.stats['evictions'] += 1
                logger.debug(f"{self.name}: Evicted oldest item {oldest_item}")
            
            # Add new item
            self.items[item] = True
            self.stats['additions'] += 1
            self.stats['size'] = len(self.items)
            return True  # New item added
    
    async def contains(self, item: Any) -> bool:
        """Check if item exists in set."""
        async with self.lock:
            return item in self.items
    
    async def remove(self, item: Any) -> bool:
        """Remove item from set."""
        async with self.lock:
            if item in self.items:
                self.items.pop(item)
                self.stats['size'] = len(self.items)
                return True
            return False
    
    async def size(self) -> int:
        """Get current set size."""
        async with self.lock:
            return len(self.items)
    
    async def get_stats(self) -> Dict:
        """Get set statistics."""
        async with self.lock:
            return {
                **self.stats,
                'current_size': len(self.items),
                'max_size': self.max_size,
                'utilization': (len(self.items) / self.max_size) * 100
            }
