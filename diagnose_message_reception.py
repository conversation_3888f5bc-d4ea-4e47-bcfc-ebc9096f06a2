#!/usr/bin/env python3
"""
Message Reception Diagnostic Tool
Checks if the bot is actually receiving messages from Telegram groups
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add project root to path
sys.path.append('.')

from config import *
from src.telegram_client import TelegramClient
from src.logger_setup import setup_logging

# Setup logging
logger = setup_logging()

class MessageReceptionDiagnostic:
    def __init__(self):
        self.message_count = 0
        self.group_messages = {}
        self.start_time = time.time()
        
    async def run_diagnostic(self):
        """Run message reception diagnostic"""
        print("=" * 80)
        print("🔍 MESSAGE RECEPTION DIAGNOSTIC")
        print("=" * 80)
        
        try:
            # Initialize Telegram client
            print("📱 Initializing Telegram client...")
            client = TelegramClient()
            await client.initialize()
            await client.connect()
            
            print(f"✅ Connected as: {client.client.get_me().first_name}")
            
            # Setup message handler for diagnostic
            print("🔧 Setting up diagnostic message handler...")
            
            # Get groups to monitor
            groups_to_monitor = [
                TELEGRAM_GROUPS['primary_group'],
                *TELEGRAM_GROUPS['additional_groups']
            ]
            
            print(f"📊 Monitoring {len(groups_to_monitor)} groups:")
            for group_id in groups_to_monitor:
                group_name = TELEGRAM_GROUPS.get('group_names', {}).get(str(group_id), f"Group {group_id}")
                print(f"   - {group_name} ({group_id})")
                self.group_messages[group_id] = 0
            
            # Setup message handler
            @client.client.on(client.events.NewMessage(chats=groups_to_monitor))
            async def diagnostic_message_handler(event):
                """Diagnostic message handler - just count messages"""
                self.message_count += 1
                group_id = event.chat_id
                
                if group_id in self.group_messages:
                    self.group_messages[group_id] += 1
                
                group_name = TELEGRAM_GROUPS.get('group_names', {}).get(str(group_id), f"Group {group_id}")
                
                # Get message preview
                message_text = event.message.text or "[Media/Sticker]"
                preview = message_text[:50] + "..." if len(message_text) > 50 else message_text
                
                elapsed = time.time() - self.start_time
                print(f"📨 [{elapsed:.1f}s] Message #{self.message_count} from {group_name}: {preview}")
                
                # Log to file as well
                logger.info(f"🔍 DIAGNOSTIC: Message received from {group_name} ({group_id}): {preview}")
            
            print("🎯 Starting message listener...")
            print("⏱️ Monitoring for 60 seconds...")
            print("💡 Send a test message in any monitored group to verify reception")
            print("-" * 80)
            
            # Start listening
            await client.start_listening()
            
            # Wait for messages
            await asyncio.sleep(60)
            
            # Results
            print("-" * 80)
            print("📊 DIAGNOSTIC RESULTS:")
            print(f"   Total messages received: {self.message_count}")
            print(f"   Monitoring duration: 60 seconds")
            
            if self.message_count > 0:
                print("✅ MESSAGE RECEPTION: WORKING")
                print("   Messages by group:")
                for group_id, count in self.group_messages.items():
                    if count > 0:
                        group_name = TELEGRAM_GROUPS.get('group_names', {}).get(str(group_id), f"Group {group_id}")
                        print(f"      - {group_name}: {count} messages")
            else:
                print("❌ MESSAGE RECEPTION: NO MESSAGES RECEIVED")
                print("   Possible issues:")
                print("   1. Groups are inactive during test period")
                print("   2. Bot doesn't have permission to read messages")
                print("   3. Message handler not properly configured")
                print("   4. Network connectivity issues")
            
            await client.disconnect()
            
        except Exception as e:
            print(f"❌ Diagnostic failed: {e}")
            import traceback
            traceback.print_exc()
        
        print("=" * 80)
        print("🎉 DIAGNOSTIC COMPLETE")
        print("=" * 80)

async def main():
    """Main diagnostic function"""
    diagnostic = MessageReceptionDiagnostic()
    await diagnostic.run_diagnostic()

if __name__ == "__main__":
    asyncio.run(main())
