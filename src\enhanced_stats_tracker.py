"""Enhanced statistics tracking and reporting system for CLA v2.0 bot."""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
from loguru import logger

@dataclass
class GroupStats:
    """Statistics for a specific group."""
    group_id: int
    group_name: str
    messages_received: int = 0
    cas_detected: int = 0
    cas_forwarded: int = 0
    cas_filtered_duplicate: int = 0
    cas_filtered_trending: int = 0
    trending_qualified: int = 0
    noise_filtered: int = 0
    last_activity: Optional[datetime] = None
    is_high_volume: bool = False

@dataclass
class DestinationStats:
    """Statistics for forwarding destinations."""
    destination_name: str
    total_cas_sent: int = 0
    success_rate: float = 100.0
    last_sent: Optional[datetime] = None
    queue_size: int = 0

@dataclass
class PerformanceStats:
    """Performance timing statistics."""
    message_handling_avg: float = 0.0
    ca_detection_avg: float = 0.0
    trending_analysis_avg: float = 0.0
    forwarding_avg: float = 0.0
    pipeline_total_avg: float = 0.0
    slow_operations_count: int = 0

@dataclass
class SystemStats:
    """Overall system statistics."""
    start_time: datetime
    uptime_hours: float = 0.0
    total_messages: int = 0
    total_cas_detected: int = 0
    total_cas_forwarded: int = 0
    total_duplicates_filtered: int = 0
    trending_qualified: int = 0
    pump_schemes_blocked: int = 0
    errors_count: int = 0
    warnings_count: int = 0

class EnhancedStatsTracker:
    """Enhanced statistics tracking and reporting system."""
    
    def __init__(self):
        self.system_stats = SystemStats(start_time=datetime.now())
        self.group_stats: Dict[int, GroupStats] = {}
        self.destination_stats: Dict[str, DestinationStats] = {}
        self.performance_stats = PerformanceStats()
        
        # Hourly tracking
        self.hourly_stats = defaultdict(dict)
        self.last_hourly_report = datetime.now()
        
        # Performance timing data
        self.timing_samples = {
            'message_handling': [],
            'ca_detection': [],
            'trending_analysis': [],
            'forwarding': [],
            'pipeline_total': []
        }
        
        # Initialize group configurations
        self._initialize_groups()
        self._initialize_destinations()
        
        logger.info("Enhanced statistics tracker initialized")
    
    def _initialize_groups(self):
        """Initialize group statistics."""
        groups_config = [
            (-1002380594298, "FREE WHALE SIGNALS", False),
            (-1002270988204, "Solana Activity Tracker", False),
            (-1002064145465, "🌹 MANIFEST", False),
            (-1001763265784, "👤 Mark Degens", False),
            (-1002139128702, "💎 FINDERTRENDING", False),
            (-1002202241417, "🧠 GMGN Featured Signals", True),
            (-1002333406905, "🚀 MEME 1000X", True)
        ]
        
        for group_id, group_name, is_high_volume in groups_config:
            self.group_stats[group_id] = GroupStats(
                group_id=group_id,
                group_name=group_name,
                is_high_volume=is_high_volume
            )
    
    def _initialize_destinations(self):
        """Initialize destination statistics."""
        destinations = ["BonkBot", "CLA v2.0", "Monaco PNL", "WINNERS"]
        
        for dest in destinations:
            self.destination_stats[dest] = DestinationStats(destination_name=dest)
    
    def record_message_received(self, group_id: int):
        """Record a message received from a group."""
        if group_id in self.group_stats:
            self.group_stats[group_id].messages_received += 1
            self.group_stats[group_id].last_activity = datetime.now()
            self.system_stats.total_messages += 1
            
            logger.debug(f"Message recorded for {self.group_stats[group_id].group_name}")
    
    def record_ca_detected(self, group_id: int, ca_count: int):
        """Record CAs detected from a group."""
        if group_id in self.group_stats:
            self.group_stats[group_id].cas_detected += ca_count
            self.system_stats.total_cas_detected += ca_count
            
            logger.debug(f"CAs detected: {ca_count} from {self.group_stats[group_id].group_name}")
    
    def record_ca_forwarded(self, group_id: int, destination: str, ca_count: int):
        """Record CAs forwarded to a destination."""
        if group_id in self.group_stats:
            self.group_stats[group_id].cas_forwarded += ca_count
            self.system_stats.total_cas_forwarded += ca_count
        
        if destination in self.destination_stats:
            self.destination_stats[destination].total_cas_sent += ca_count
            self.destination_stats[destination].last_sent = datetime.now()
            
            logger.debug(f"CAs forwarded: {ca_count} to {destination}")
    
    def record_ca_filtered_duplicate(self, group_id: int, ca_count: int):
        """Record CAs filtered due to duplicates."""
        if group_id in self.group_stats:
            self.group_stats[group_id].cas_filtered_duplicate += ca_count
            self.system_stats.total_duplicates_filtered += ca_count
    
    def record_ca_filtered_trending(self, group_id: int, ca_count: int):
        """Record CAs filtered due to trending requirements."""
        if group_id in self.group_stats:
            self.group_stats[group_id].cas_filtered_trending += ca_count
    
    def record_trending_qualified(self, group_id: int, ca_count: int):
        """Record CAs that qualified for trending."""
        if group_id in self.group_stats:
            self.group_stats[group_id].trending_qualified += ca_count
            self.system_stats.trending_qualified += ca_count
    
    def record_noise_filtered(self, group_id: int, ca_count: int):
        """Record CAs filtered as noise."""
        if group_id in self.group_stats:
            self.group_stats[group_id].noise_filtered += ca_count
    
    def record_pump_scheme_blocked(self):
        """Record a pump scheme that was blocked."""
        self.system_stats.pump_schemes_blocked += 1
        logger.info("Pump scheme blocked recorded")
    
    def record_performance_timing(self, operation: str, duration_ms: float):
        """Record performance timing for an operation."""
        if operation in self.timing_samples:
            self.timing_samples[operation].append(duration_ms)
            
            # Keep only last 100 samples
            if len(self.timing_samples[operation]) > 100:
                self.timing_samples[operation] = self.timing_samples[operation][-100:]
            
            # Update averages
            self._update_performance_averages()
            
            # Count slow operations
            thresholds = {
                'message_handling': 100,
                'ca_detection': 200,
                'trending_analysis': 300,
                'forwarding': 500,
                'pipeline_total': 1000
            }
            
            if duration_ms > thresholds.get(operation, 1000):
                self.performance_stats.slow_operations_count += 1
    
    def _update_performance_averages(self):
        """Update performance averages."""
        for operation, samples in self.timing_samples.items():
            if samples:
                avg = sum(samples) / len(samples)
                setattr(self.performance_stats, f"{operation}_avg", avg)
    
    def record_error(self):
        """Record an error."""
        self.system_stats.errors_count += 1
    
    def record_warning(self):
        """Record a warning."""
        self.system_stats.warnings_count += 1
    
    def update_uptime(self):
        """Update system uptime."""
        uptime = datetime.now() - self.system_stats.start_time
        self.system_stats.uptime_hours = uptime.total_seconds() / 3600
    
    def should_send_hourly_report(self) -> bool:
        """Check if it's time to send hourly report."""
        now = datetime.now()
        return (now - self.last_hourly_report).total_seconds() >= 3600
    
    def generate_hourly_report(self) -> str:
        """Generate comprehensive hourly status report."""
        self.update_uptime()
        now = datetime.now()
        
        report = []
        report.append("🤖 CLA v2.0 Bot - Hourly Status Report")
        report.append("=" * 50)
        report.append(f"📅 Report Time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"⏱️ Uptime: {self.system_stats.uptime_hours:.1f} hours")
        report.append("")
        
        # System Overview
        report.append("📊 System Overview:")
        report.append(f"   Messages Processed: {self.system_stats.total_messages}")
        report.append(f"   CAs Detected: {self.system_stats.total_cas_detected}")
        report.append(f"   CAs Forwarded: {self.system_stats.total_cas_forwarded}")
        report.append(f"   Duplicates Filtered: {self.system_stats.total_duplicates_filtered}")
        report.append(f"   Trending Qualified: {self.system_stats.trending_qualified}")
        report.append(f"   Pump Schemes Blocked: {self.system_stats.pump_schemes_blocked}")
        report.append("")
        
        # Per-Group Statistics
        report.append("📈 Per-Group Statistics:")
        for group_id, stats in self.group_stats.items():
            group_type = "🔥 HIGH-VOLUME" if stats.is_high_volume else "⚡ LOW-VOLUME"
            time_since = "Never"
            if stats.last_activity:
                time_since = f"{(now - stats.last_activity).total_seconds() / 60:.1f}min ago"
            
            report.append(f"   {group_type} {stats.group_name}:")
            report.append(f"      Messages: {stats.messages_received} | CAs: {stats.cas_detected}")
            report.append(f"      Forwarded: {stats.cas_forwarded} | Filtered: {stats.cas_filtered_duplicate + stats.cas_filtered_trending}")
            if stats.is_high_volume:
                report.append(f"      Trending: {stats.trending_qualified} | Noise: {stats.noise_filtered}")
            report.append(f"      Last Activity: {time_since}")
        report.append("")
        
        # Destination Statistics
        report.append("📤 Forwarding Destinations:")
        for dest_name, stats in self.destination_stats.items():
            last_sent = "Never"
            if stats.last_sent:
                last_sent = f"{(now - stats.last_sent).total_seconds() / 60:.1f}min ago"
            report.append(f"   {dest_name}: {stats.total_cas_sent} CAs sent | Last: {last_sent}")
        report.append("")
        
        # Performance Statistics
        report.append("⚡ Performance Statistics:")
        report.append(f"   Message Handling: {self.performance_stats.message_handling_avg:.1f}ms avg")
        report.append(f"   CA Detection: {self.performance_stats.ca_detection_avg:.1f}ms avg")
        report.append(f"   Trending Analysis: {self.performance_stats.trending_analysis_avg:.1f}ms avg")
        report.append(f"   Forwarding: {self.performance_stats.forwarding_avg:.1f}ms avg")
        report.append(f"   Total Pipeline: {self.performance_stats.pipeline_total_avg:.1f}ms avg")
        report.append(f"   Slow Operations: {self.performance_stats.slow_operations_count}")
        report.append("")
        
        # System Health
        report.append("🏥 System Health:")
        if self.system_stats.errors_count == 0 and self.performance_stats.slow_operations_count < 10:
            health_status = "🟢 EXCELLENT"
        elif self.system_stats.errors_count < 5 and self.performance_stats.slow_operations_count < 25:
            health_status = "🟡 GOOD"
        else:
            health_status = "🔴 NEEDS ATTENTION"
        
        report.append(f"   Status: {health_status}")
        report.append(f"   Errors: {self.system_stats.errors_count} | Warnings: {self.system_stats.warnings_count}")
        report.append("")
        
        # Anti-Pump Protection Status
        report.append("🛡️ Anti-Pump Protection:")
        report.append("   High-Volume Groups: 6 mentions in 8 minutes")
        report.append("   Enhanced Filters: 120s spread, 3.0/min velocity, 3min organic growth")
        report.append("   Low-Volume Groups: Direct forwarding with global duplicate prevention")
        report.append("")

        # High-Volume Pattern Analysis
        try:
            from src.high_volume_ca_analyzer import high_volume_analyzer

            report.append("🔍 High-Volume Pattern Analysis:")
            cross_group_cas = len([p for p in high_volume_analyzer.cross_group_patterns.values()
                                 if p.time_difference is not None])

            report.append(f"   Cross-group CAs detected: {cross_group_cas}")
            report.append(f"   GMGN detections: {high_volume_analyzer.stats['total_gmgn_detections']}")
            report.append(f"   MEME 1000X detections: {high_volume_analyzer.stats['total_meme_1000x_detections']}")

            # Calculate filter effectiveness
            total_hv_detections = (high_volume_analyzer.stats['total_gmgn_detections'] +
                                 high_volume_analyzer.stats['total_meme_1000x_detections'])
            if total_hv_detections > 0:
                filter_rate = (high_volume_analyzer.stats['noise_filtered'] / total_hv_detections) * 100
                report.append(f"   Filter effectiveness: {filter_rate:.1f}% noise filtered")

            report.append("")

        except Exception as e:
            report.append("🔍 High-Volume Pattern Analysis: Data not available")
            report.append("")

        # CA Rescue Mechanism Status
        try:
            from src.ca_rescue_tracker import ca_rescue_tracker

            rescue_stats = ca_rescue_tracker.get_rescue_statistics()
            report.append("🛡️ CA Rescue Mechanism:")
            report.append(f"   Rescue-eligible CAs: {rescue_stats['rescue_eligible_count']}")
            report.append(f"   Successful rescues: {rescue_stats['successful_rescues']}")
            report.append(f"   Rescue success rate: {rescue_stats['rescue_success_rate']:.1f}%")
            report.append(f"   Duplicate rescues prevented: {rescue_stats['duplicate_rescue_prevented']}")
            report.append("")

        except Exception as e:
            report.append("🛡️ CA Rescue Mechanism: Data not available")
            report.append("")

        report.append("🎯 Next Report: 1 hour")
        
        self.last_hourly_report = now
        return "\n".join(report)

    def generate_concise_summary(self) -> str:
        """Generate a concise one-line status summary."""
        self.update_uptime()

        # Calculate health status
        health = "🟢"
        if self.system_stats.errors_count > 0 or self.performance_stats.slow_operations_count > 10:
            health = "🔴"
        elif self.performance_stats.slow_operations_count > 5:
            health = "🟡"

        # Count active groups
        active_groups = sum(1 for stats in self.group_stats.values() if stats.last_activity)

        return (f"{health} Uptime: {self.system_stats.uptime_hours:.1f}h | "
                f"Messages: {self.system_stats.total_messages} | "
                f"CAs: {self.system_stats.total_cas_detected}/{self.system_stats.total_cas_forwarded} | "
                f"Active Groups: {active_groups}/{len(self.group_stats)} | "
                f"Errors: {self.system_stats.errors_count}")

    def should_log_detailed_report(self) -> bool:
        """Determine if detailed report should be logged (only when there are issues)."""
        return (self.system_stats.errors_count > 0 or
                self.performance_stats.slow_operations_count > 10 or
                self.system_stats.total_cas_detected > 50)  # High activity threshold

    def get_current_stats_summary(self) -> Dict:
        """Get current statistics summary."""
        self.update_uptime()
        
        return {
            'system': asdict(self.system_stats),
            'groups': {gid: asdict(stats) for gid, stats in self.group_stats.items()},
            'destinations': {name: asdict(stats) for name, stats in self.destination_stats.items()},
            'performance': asdict(self.performance_stats)
        }
    
    def save_stats_to_file(self, filepath: str):
        """Save statistics to JSON file."""
        try:
            stats_data = self.get_current_stats_summary()
            
            # Convert datetime objects to strings
            def convert_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: convert_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime(item) for item in obj]
                return obj
            
            stats_data = convert_datetime(stats_data)
            
            with open(filepath, 'w') as f:
                json.dump(stats_data, f, indent=2)
                
            logger.info(f"Statistics saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving statistics: {e}")
    
    def load_stats_from_file(self, filepath: str):
        """Load statistics from JSON file."""
        try:
            with open(filepath, 'r') as f:
                stats_data = json.load(f)
            
            # Restore statistics (implementation would depend on persistence requirements)
            logger.info(f"Statistics loaded from {filepath}")
            
        except FileNotFoundError:
            logger.info("No previous statistics file found, starting fresh")
        except Exception as e:
            logger.error(f"Error loading statistics: {e}")

# Global instance
enhanced_stats = EnhancedStatsTracker()
