#!/usr/bin/env python3
"""
Test Enhanced CA Validation for DeFi Protocol Addresses
Specifically tests the CA: 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups
"""

import asyncio
import sys
sys.path.append('.')

async def test_enhanced_validation():
    """Test the enhanced CA validation with the specific problematic CA."""
    print("🧪 TESTING ENHANCED CA VALIDATION")
    print("=" * 60)
    
    # Test cases including the problematic CA
    test_cases = [
        {
            "ca": "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups",
            "description": "Problematic DeFi CA from FREE WHALE SIGNALS",
            "expected": True,  # Should now pass with enhanced validation
            "protocol": "Jupiter/DeFi (ends with 'jups')"
        },
        {
            "ca": "So11111111111111111111111111111111111111112",
            "description": "Wrapped SOL (known valid)",
            "expected": True,
            "protocol": "Solana Native"
        },
        {
            "ca": "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
            "description": "Token Program (known valid)",
            "expected": True,
            "protocol": "Token Program"
        },
        {
            "ca": "11111111111111111111111111111112",
            "description": "System Program (known valid)",
            "expected": True,
            "protocol": "System Program"
        },
        {
            "ca": "invalid_ca_too_short",
            "description": "Invalid CA (too short)",
            "expected": False,
            "protocol": "None"
        },
        {
            "ca": "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups_too_long",
            "description": "Invalid CA (too long)",
            "expected": False,
            "protocol": "None"
        }
    ]
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        print("Testing enhanced validation logic...")
        print()
        
        passed_tests = 0
        total_tests = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            ca = test_case["ca"]
            expected = test_case["expected"]
            description = test_case["description"]
            protocol = test_case["protocol"]
            
            print(f"Test {i}: {description}")
            print(f"CA: {ca}")
            print(f"Protocol: {protocol}")
            print(f"Expected: {'✅ PASS' if expected else '❌ FAIL'}")
            
            # Test the validation
            result = parser._validate_solana_ca(ca)
            
            print(f"Result: {'✅ PASS' if result else '❌ FAIL'}")
            
            if result == expected:
                print("✅ TEST PASSED")
                passed_tests += 1
            else:
                print("❌ TEST FAILED")
            
            print("-" * 50)
        
        print(f"SUMMARY: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED - Enhanced validation working correctly!")
        else:
            print("⚠️ Some tests failed - validation needs adjustment")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_message_processing():
    """Test the full message processing pipeline with the enhanced validation."""
    print("\n🧪 TESTING FULL MESSAGE PROCESSING PIPELINE")
    print("=" * 60)
    
    # Test the exact message from FREE WHALE SIGNALS
    test_messages = [
        {
            "text": "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)",
            "expected_cas": ["5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"],
            "description": "FREE WHALE SIGNALS message with DeFi CA"
        },
        {
            "text": "Check out this token: 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups",
            "expected_cas": ["5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"],
            "description": "Standalone DeFi CA"
        },
        {
            "text": "Multiple CAs: So11111111111111111111111111111111111111112 and 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups",
            "expected_cas": ["So11111111111111111111111111111111111111112", "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"],
            "description": "Multiple CAs including DeFi CA"
        }
    ]
    
    try:
        from src.message_parser import MessageParser
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        parser = MessageParser()
        db_manager = DatabaseManager()
        await db_manager.initialize()
        ca_detector = CADetector(db_manager)
        
        print("Components initialized successfully")
        print()
        
        passed_tests = 0
        total_tests = len(test_messages)
        
        for i, test_case in enumerate(test_messages, 1):
            text = test_case["text"]
            expected_cas = test_case["expected_cas"]
            description = test_case["description"]
            
            print(f"Test {i}: {description}")
            print(f"Message: {text[:60]}{'...' if len(text) > 60 else ''}")
            print(f"Expected CAs: {expected_cas}")
            
            # Test message parser
            extracted_cas = parser.extract_contract_addresses(text)
            print(f"Parser result: {extracted_cas}")
            
            # Test CA detector (full pipeline)
            detected_cas = await ca_detector.process_message(text, 99999, -1002380594298)
            print(f"Detector result: {detected_cas}")
            
            # Check if expected CAs were found
            parser_success = all(ca in extracted_cas for ca in expected_cas)
            detector_success = all(ca in detected_cas for ca in expected_cas)
            
            print(f"Parser success: {'✅' if parser_success else '❌'}")
            print(f"Detector success: {'✅' if detector_success else '❌'}")
            
            if parser_success and detector_success:
                print("✅ TEST PASSED")
                passed_tests += 1
            else:
                print("❌ TEST FAILED")
            
            print("-" * 50)
        
        print(f"SUMMARY: {passed_tests}/{total_tests} pipeline tests passed")
        
        await db_manager.close()
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_forwarding_simulation():
    """Simulate the forwarding process with the enhanced validation."""
    print("\n🧪 TESTING FORWARDING SIMULATION")
    print("=" * 60)
    
    try:
        from src.database import DatabaseManager
        from src.trending_analyzer import TrendingAnalyzer
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        trending_analyzer = TrendingAnalyzer(db_manager)
        
        # Test the specific CA
        test_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
        free_whale_signals_id = -1002380594298
        
        print(f"Testing forwarding for CA: {test_ca}")
        print(f"Source group: FREE WHALE SIGNALS ({free_whale_signals_id})")
        print()
        
        # Check group classification
        is_low_volume = trending_analyzer.is_low_volume_group(free_whale_signals_id)
        print(f"Is low volume group: {is_low_volume}")
        
        # Test forwarding decisions
        destinations = ['BONKBOT', 'CLA_V2', 'MONACO_PNL']
        
        print("Forwarding decisions:")
        for dest in destinations:
            should_forward = trending_analyzer.should_forward_to_destination(test_ca, dest, free_whale_signals_id)
            print(f"  {dest}: {'✅ FORWARD' if should_forward else '❌ BLOCK'}")
        
        await db_manager.close()
        
        print("\n✅ Forwarding simulation completed")
        return True
        
    except Exception as e:
        print(f"❌ Forwarding simulation failed: {e}")
        return False

async def main():
    """Run all enhanced validation tests."""
    print("🚀 ENHANCED CA VALIDATION TEST SUITE")
    print("=" * 70)
    print("Testing enhanced validation for DeFi protocol addresses")
    print("Target CA: 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups")
    print("=" * 70)
    
    # Run all tests
    validation_success = await test_enhanced_validation()
    pipeline_success = await test_full_message_processing()
    forwarding_success = await test_forwarding_simulation()
    
    print("\n🎯 FINAL RESULTS")
    print("=" * 70)
    print(f"Enhanced Validation: {'✅ PASS' if validation_success else '❌ FAIL'}")
    print(f"Message Processing: {'✅ PASS' if pipeline_success else '❌ FAIL'}")
    print(f"Forwarding Simulation: {'✅ PASS' if forwarding_success else '❌ FAIL'}")
    
    if validation_success and pipeline_success and forwarding_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Enhanced validation successfully handles DeFi protocol CAs")
        print("✅ The problematic CA should now be forwarded correctly")
        print("✅ System is ready for production deployment")
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Review the failed tests and adjust validation logic as needed")
    
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
