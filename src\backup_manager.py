"""Backup and recovery system for CLA v2.0 Bot production deployment."""

import os
import shutil
import asyncio
import tarfile
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
from loguru import logger

class BackupManager:
    """Manages database backups and recovery for production deployment."""
    
    def __init__(self):
        self.enabled = os.getenv('BACKUP_ENABLED', 'true').lower() == 'true'
        self.backup_interval_hours = int(os.getenv('BACKUP_INTERVAL_HOURS', '6'))
        self.retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))

        # Use local paths for development, production paths for production
        environment = os.getenv('ENVIRONMENT', 'development')
        if environment == 'production':
            self.backup_path = Path(os.getenv('BACKUP_PATH', '/opt/cla-bot/backups'))
            self.db_path = Path(os.getenv('DATABASE_PATH', '/opt/cla-bot/data/cla_bot.db'))
            self.working_dir = Path(os.getenv('WORKING_DIRECTORY', '/opt/cla-bot'))
        else:
            self.backup_path = Path(os.getenv('BACKUP_PATH', './backups'))
            self.db_path = Path(os.getenv('DATABASE_PATH', './data/cla_bot.db'))
            self.working_dir = Path(os.getenv('WORKING_DIRECTORY', '.'))

        # Ensure backup directory exists with proper error handling
        try:
            self.backup_path.mkdir(parents=True, exist_ok=True)
        except PermissionError:
            # Fall back to local directory if permission denied
            self.backup_path = Path('./backups')
            self.backup_path.mkdir(parents=True, exist_ok=True)
        
        self.last_backup = None
        self.backup_stats = {
            'total_backups': 0,
            'successful_backups': 0,
            'failed_backups': 0,
            'last_backup_time': None,
            'last_backup_size_mb': 0
        }
    
    async def start_backup_scheduler(self):
        """Start the automatic backup scheduler."""
        if not self.enabled:
            logger.info("📦 Backup system disabled")
            return
        
        logger.info(f"📦 Starting backup scheduler - interval: {self.backup_interval_hours}h, retention: {self.retention_days}d")
        
        # Create initial backup
        await self.create_backup()
        
        # Schedule periodic backups
        while True:
            try:
                await asyncio.sleep(self.backup_interval_hours * 3600)  # Convert hours to seconds
                await self.create_backup()
                await self.cleanup_old_backups()
            except Exception as e:
                logger.error(f"❌ Backup scheduler error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    async def create_backup(self, backup_type: str = "scheduled") -> Optional[str]:
        """Create a comprehensive backup of the bot data."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"cla_bot_backup_{backup_type}_{timestamp}.tar.gz"
            backup_file_path = self.backup_path / backup_filename
            
            logger.info(f"📦 Creating {backup_type} backup: {backup_filename}")
            
            # Create temporary directory for backup preparation
            temp_backup_dir = self.backup_path / f"temp_{timestamp}"
            temp_backup_dir.mkdir(exist_ok=True)
            
            try:
                # Backup database with integrity check
                await self._backup_database(temp_backup_dir)
                
                # Backup configuration files
                await self._backup_configuration(temp_backup_dir)
                
                # Backup logs (recent only)
                await self._backup_logs(temp_backup_dir)
                
                # Create compressed archive
                with tarfile.open(backup_file_path, "w:gz") as tar:
                    tar.add(temp_backup_dir, arcname=f"cla_bot_backup_{timestamp}")
                
                # Calculate backup size
                backup_size_mb = backup_file_path.stat().st_size / (1024 * 1024)
                
                # Update statistics
                self.backup_stats['total_backups'] += 1
                self.backup_stats['successful_backups'] += 1
                self.backup_stats['last_backup_time'] = datetime.now()
                self.backup_stats['last_backup_size_mb'] = backup_size_mb
                
                logger.info(f"✅ Backup created successfully: {backup_filename} ({backup_size_mb:.2f} MB)")
                self.last_backup = backup_file_path
                
                return str(backup_file_path)
                
            finally:
                # Cleanup temporary directory
                if temp_backup_dir.exists():
                    shutil.rmtree(temp_backup_dir)
                    
        except Exception as e:
            self.backup_stats['failed_backups'] += 1
            logger.error(f"❌ Backup creation failed: {e}")
            return None
    
    async def _backup_database(self, backup_dir: Path):
        """Backup the SQLite database with integrity check."""
        try:
            if not self.db_path.exists():
                logger.warning(f"⚠️ Database file not found: {self.db_path}")
                return
            
            # Create database backup directory
            db_backup_dir = backup_dir / "database"
            db_backup_dir.mkdir(exist_ok=True)
            
            # Perform integrity check before backup
            if not await self._check_database_integrity():
                logger.warning("⚠️ Database integrity check failed - proceeding with backup anyway")
            
            # Create database backup using SQLite backup API
            backup_db_path = db_backup_dir / "cla_bot.db"
            
            # Use SQLite's backup API for consistent backup
            source_conn = sqlite3.connect(str(self.db_path))
            backup_conn = sqlite3.connect(str(backup_db_path))
            
            try:
                source_conn.backup(backup_conn)
                logger.debug(f"📊 Database backed up: {backup_db_path}")
            finally:
                source_conn.close()
                backup_conn.close()
            
            # Also create a SQL dump for recovery purposes
            sql_dump_path = db_backup_dir / "cla_bot_dump.sql"
            with open(sql_dump_path, 'w') as f:
                conn = sqlite3.connect(str(self.db_path))
                try:
                    for line in conn.iterdump():
                        f.write(f"{line}\n")
                finally:
                    conn.close()
            
            logger.debug(f"📄 SQL dump created: {sql_dump_path}")
            
        except Exception as e:
            logger.error(f"❌ Database backup failed: {e}")
            raise
    
    async def _backup_configuration(self, backup_dir: Path):
        """Backup configuration files."""
        try:
            config_backup_dir = backup_dir / "config"
            config_backup_dir.mkdir(exist_ok=True)
            
            # Backup .env file (without sensitive data in logs)
            env_file = self.working_dir / ".env"
            if env_file.exists():
                shutil.copy2(env_file, config_backup_dir / ".env")
                logger.debug("📋 Configuration file backed up")
            
            # Backup any other config files
            config_files = [
                "config.py",
                ".env.production",
                "requirements.txt"
            ]
            
            for config_file in config_files:
                source_file = self.working_dir / config_file
                if source_file.exists():
                    shutil.copy2(source_file, config_backup_dir / config_file)
                    logger.debug(f"📋 {config_file} backed up")
            
        except Exception as e:
            logger.error(f"❌ Configuration backup failed: {e}")
            raise
    
    async def _backup_logs(self, backup_dir: Path):
        """Backup recent log files."""
        try:
            logs_backup_dir = backup_dir / "logs"
            logs_backup_dir.mkdir(exist_ok=True)
            
            logs_dir = self.working_dir / "logs"
            if not logs_dir.exists():
                return
            
            # Backup only recent log files (last 7 days)
            cutoff_date = datetime.now() - timedelta(days=7)
            
            for log_file in logs_dir.glob("*.log*"):
                if log_file.is_file():
                    file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                    if file_mtime > cutoff_date:
                        shutil.copy2(log_file, logs_backup_dir / log_file.name)
                        logger.debug(f"📝 Log file backed up: {log_file.name}")
            
        except Exception as e:
            logger.error(f"❌ Logs backup failed: {e}")
            # Don't raise - logs backup failure shouldn't fail entire backup
    
    async def _check_database_integrity(self) -> bool:
        """Check database integrity before backup."""
        try:
            conn = sqlite3.connect(str(self.db_path))
            try:
                cursor = conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                return result and result[0] == "ok"
            finally:
                conn.close()
        except Exception as e:
            logger.error(f"❌ Database integrity check failed: {e}")
            return False
    
    async def cleanup_old_backups(self):
        """Remove old backup files based on retention policy."""
        try:
            cutoff_date = datetime.now() - timedelta(days=self.retention_days)
            removed_count = 0
            total_size_removed = 0
            
            for backup_file in self.backup_path.glob("cla_bot_backup_*.tar.gz"):
                if backup_file.is_file():
                    file_mtime = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_mtime < cutoff_date:
                        file_size = backup_file.stat().st_size
                        backup_file.unlink()
                        removed_count += 1
                        total_size_removed += file_size
                        logger.debug(f"🗑️ Removed old backup: {backup_file.name}")
            
            if removed_count > 0:
                size_mb = total_size_removed / (1024 * 1024)
                logger.info(f"🗑️ Cleaned up {removed_count} old backups ({size_mb:.2f} MB freed)")
            
        except Exception as e:
            logger.error(f"❌ Backup cleanup failed: {e}")
    
    async def restore_from_backup(self, backup_file_path: str) -> bool:
        """Restore bot data from a backup file."""
        try:
            backup_path = Path(backup_file_path)
            if not backup_path.exists():
                logger.error(f"❌ Backup file not found: {backup_file_path}")
                return False
            
            logger.info(f"🔄 Starting restore from backup: {backup_path.name}")
            
            # Create temporary extraction directory
            temp_restore_dir = self.backup_path / f"restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            temp_restore_dir.mkdir(exist_ok=True)
            
            try:
                # Extract backup
                with tarfile.open(backup_path, "r:gz") as tar:
                    tar.extractall(temp_restore_dir)
                
                # Find the extracted backup directory
                extracted_dirs = list(temp_restore_dir.glob("cla_bot_backup_*"))
                if not extracted_dirs:
                    logger.error("❌ Invalid backup file structure")
                    return False
                
                backup_content_dir = extracted_dirs[0]
                
                # Restore database
                await self._restore_database(backup_content_dir)
                
                # Restore configuration (optional - be careful with this)
                # await self._restore_configuration(backup_content_dir)
                
                logger.info("✅ Restore completed successfully")
                return True
                
            finally:
                # Cleanup temporary directory
                if temp_restore_dir.exists():
                    shutil.rmtree(temp_restore_dir)
                    
        except Exception as e:
            logger.error(f"❌ Restore failed: {e}")
            return False
    
    async def _restore_database(self, backup_content_dir: Path):
        """Restore database from backup."""
        try:
            db_backup_dir = backup_content_dir / "database"
            backup_db_file = db_backup_dir / "cla_bot.db"
            
            if not backup_db_file.exists():
                logger.error("❌ Database backup file not found in backup")
                return
            
            # Create backup of current database
            if self.db_path.exists():
                current_backup = self.db_path.with_suffix(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
                shutil.copy2(self.db_path, current_backup)
                logger.info(f"📦 Current database backed up to: {current_backup}")
            
            # Restore database
            shutil.copy2(backup_db_file, self.db_path)
            logger.info("✅ Database restored successfully")
            
        except Exception as e:
            logger.error(f"❌ Database restore failed: {e}")
            raise
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get backup system status and statistics."""
        try:
            # Get list of available backups
            backups = []
            for backup_file in sorted(self.backup_path.glob("cla_bot_backup_*.tar.gz"), reverse=True):
                stat = backup_file.stat()
                backups.append({
                    'filename': backup_file.name,
                    'size_mb': stat.st_size / (1024 * 1024),
                    'created': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'age_hours': (datetime.now() - datetime.fromtimestamp(stat.st_mtime)).total_seconds() / 3600
                })
            
            return {
                'enabled': self.enabled,
                'backup_interval_hours': self.backup_interval_hours,
                'retention_days': self.retention_days,
                'backup_path': str(self.backup_path),
                'statistics': self.backup_stats,
                'available_backups': backups[:10],  # Show last 10 backups
                'total_backup_count': len(backups),
                'total_backup_size_mb': sum(b['size_mb'] for b in backups)
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting backup status: {e}")
            return {'error': str(e)}

# Global backup manager instance
backup_manager = None

async def start_backup_system():
    """Start the backup system."""
    global backup_manager
    backup_manager = BackupManager()
    if backup_manager.enabled:
        asyncio.create_task(backup_manager.start_backup_scheduler())
        logger.info("📦 Backup system started")
    else:
        logger.info("📦 Backup system disabled")

async def create_manual_backup() -> Optional[str]:
    """Create a manual backup."""
    global backup_manager
    if backup_manager:
        return await backup_manager.create_backup("manual")
    return None

def get_backup_status() -> Dict[str, Any]:
    """Get backup system status."""
    global backup_manager
    if backup_manager:
        return backup_manager.get_backup_status()
    return {'enabled': False}

if __name__ == "__main__":
    # Test backup system
    async def test_backup():
        await start_backup_system()
        backup_path = await create_manual_backup()
        if backup_path:
            print(f"✅ Test backup created: {backup_path}")
        else:
            print("❌ Test backup failed")
        
        status = get_backup_status()
        print(f"📊 Backup status: {status}")
    
    asyncio.run(test_backup())
