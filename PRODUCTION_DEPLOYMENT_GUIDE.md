# CLA v2.0 Telegram Bot - Production Deployment Guide

## 🚀 **P<PERSON><PERSON><PERSON> HOST VPS DEPLOYMENT**

This guide provides step-by-step instructions for deploying the CLA v2.0 Telegram Bot to a Pebble Host VPS server with production-ready configuration.

---

## 📋 **PRE-DEPLOYMENT CHECKLIST**

### **Server Requirements**
- ✅ **OS**: Ubuntu 20.04+ or Debian 11+
- ✅ **RAM**: Minimum 2GB (4GB recommended)
- ✅ **Storage**: Minimum 20GB SSD
- ✅ **CPU**: 2+ cores recommended
- ✅ **Network**: Stable internet connection

### **Required Credentials**
- ✅ **Telegram API ID & Hash**: From https://my.telegram.org/apps
- ✅ **Telegram Phone Number**: For bot authentication
- ✅ **BonkBot Chat ID**: Target chat for forwarding
- ✅ **Group IDs**: All Telegram groups to monitor
- ✅ **VPS Access**: SSH credentials for your Pebble Host server

---

## 🔧 **INSTALLATION PROCESS**

### **Step 1: Connect to Your VPS**
```bash
# Connect via SSH
ssh root@your-pebble-host-ip

# Update system
apt update && apt upgrade -y
```

### **Step 2: Download and Prepare Bot Files**
```bash
# Create working directory
mkdir -p /opt/cla-bot
cd /opt/cla-bot

# Upload your bot files (use scp, rsync, or git)
# Example with git:
git clone https://github.com/your-repo/cla-v2-bot.git .

# Or upload via SCP from your local machine:
# scp -r "C:\Users\<USER>\Desktop\NEW BOTS 4 27\CLA v2\*" root@your-server:/opt/cla-bot/
```

### **Step 3: Run Automated Installation**
```bash
# Make installation script executable
chmod +x deployment/install.sh

# Run installation (as root)
sudo bash deployment/install.sh
```

The installation script will:
- ✅ Install Python 3.11 and dependencies
- ✅ Create dedicated bot user and group
- ✅ Set up virtual environment
- ✅ Configure systemd service
- ✅ Set up log rotation
- ✅ Configure firewall
- ✅ Create backup and monitoring scripts

### **Step 4: Configure Environment Variables**
```bash
# Edit the production configuration
sudo nano /opt/cla-bot/.env
```

**Required Configuration** (replace with your actual values):
```bash
# Telegram API (REQUIRED)
TELEGRAM_API_ID=12345678
TELEGRAM_API_HASH=your_32_character_api_hash
TELEGRAM_PHONE=+1234567890

# BonkBot Integration (REQUIRED)
BONKBOT_USERNAME=@BonkBot_bot
BONKBOT_CHAT_ID=your_bonkbot_chat_id

# Group IDs (Update with your actual group IDs)
TARGET_GROUP_ID=-1002380594298
ADDITIONAL_GROUP_IDS=-1002356333152,-1002139128702,-1002064145465,-1001763265784,-1002270988204,-1002202241417,-1002333406905

# Integration Channels
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
```

### **Step 5: Start the Bot**
```bash
# Start the service
sudo systemctl start cla-bot

# Enable auto-start on boot
sudo systemctl enable cla-bot

# Check status
sudo systemctl status cla-bot
```

---

## 📊 **MONITORING & VERIFICATION**

### **Health Check Verification**
```bash
# Check bot health
curl http://127.0.0.1:8080/health

# Detailed health information
curl http://127.0.0.1:8080/health/detailed

# Prometheus metrics
curl http://127.0.0.1:8080/metrics
```

### **Log Monitoring**
```bash
# Real-time logs
sudo journalctl -u cla-bot -f

# Recent logs
sudo journalctl -u cla-bot --since "1 hour ago"

# Application logs
tail -f /opt/cla-bot/logs/cla_bot.log
```

### **Performance Verification**
```bash
# Check bot status
cla-bot-status

# Monitor system resources
htop

# Check disk usage
df -h /opt/cla-bot
```

---

## 🔒 **SECURITY CONFIGURATION**

### **Firewall Setup**
```bash
# Check firewall status
sudo ufw status

# The installation script configures:
# - SSH (port 22)
# - Health check (port 8080)
# - Metrics (port 8081)
```

### **File Permissions**
```bash
# Verify secure permissions
ls -la /opt/cla-bot/
# Should show cla-bot:cla-bot ownership

# Check .env file permissions (should be 640)
ls -la /opt/cla-bot/.env
```

### **SSL/TLS (Optional)**
For external monitoring, consider setting up nginx with SSL:
```bash
# Install nginx (already done by install script)
sudo apt install nginx certbot python3-certbot-nginx

# Configure reverse proxy for health checks
sudo nano /etc/nginx/sites-available/cla-bot
```

---

## 💾 **BACKUP & RECOVERY**

### **Automatic Backups**
The system automatically creates backups every 6 hours:
```bash
# Check backup status
ls -la /opt/cla-bot/backups/

# Manual backup
sudo -u cla-bot /opt/cla-bot/scripts/backup.sh

# Backup retention: 30 days (configurable)
```

### **Database Backup Verification**
```bash
# Check database integrity
sqlite3 /opt/cla-bot/data/cla_bot.db "PRAGMA integrity_check;"

# View recent backups
ls -lah /opt/cla-bot/backups/ | head -10
```

### **Recovery Procedure**
```bash
# Stop the bot
sudo systemctl stop cla-bot

# Restore from backup (replace with actual backup file)
cd /opt/cla-bot/backups
tar -xzf cla_bot_backup_YYYYMMDD_HHMMSS.tar.gz

# Copy database back
sudo cp extracted_backup/database/cla_bot.db /opt/cla-bot/data/

# Restart bot
sudo systemctl start cla-bot
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues**

#### **Bot Won't Start**
```bash
# Check configuration
sudo -u cla-bot python3 /opt/cla-bot/src/config_validator.py

# Check logs for errors
sudo journalctl -u cla-bot --since "10 minutes ago"

# Verify permissions
sudo chown -R cla-bot:cla-bot /opt/cla-bot
```

#### **Health Check Fails**
```bash
# Check if health server is running
netstat -tlnp | grep 8080

# Test health endpoint locally
curl -v http://127.0.0.1:8080/health

# Check firewall
sudo ufw status numbered
```

#### **High Memory Usage**
```bash
# Monitor memory usage
free -h
ps aux | grep python

# Check for memory leaks in logs
grep -i "memory\|leak" /opt/cla-bot/logs/cla_bot.log
```

#### **Database Issues**
```bash
# Check database file
ls -la /opt/cla-bot/data/cla_bot.db

# Test database connectivity
sqlite3 /opt/cla-bot/data/cla_bot.db ".tables"

# Check database integrity
sqlite3 /opt/cla-bot/data/cla_bot.db "PRAGMA integrity_check;"
```

### **Performance Issues**
```bash
# Check processing times in logs
grep "SLOW PROCESSING" /opt/cla-bot/logs/cla_bot.log

# Monitor system resources
htop
iotop
nethogs
```

---

## 📈 **PERFORMANCE OPTIMIZATION**

### **Expected Performance Metrics**
- ✅ **CA Detection**: < 5ms average
- ✅ **Message Processing**: < 10ms average
- ✅ **Memory Usage**: < 1GB under normal load
- ✅ **CPU Usage**: < 20% average

### **Optimization Tips**
1. **Database**: Regular VACUUM operations
2. **Logs**: Proper rotation to prevent disk filling
3. **Cache**: Monitor cache hit rates
4. **Network**: Ensure stable connection to Telegram

---

## 🔄 **MAINTENANCE PROCEDURES**

### **Regular Maintenance**
```bash
# Weekly system updates
sudo apt update && sudo apt upgrade -y

# Monthly log cleanup
sudo journalctl --vacuum-time=30d

# Check backup integrity
sudo -u cla-bot /opt/cla-bot/scripts/backup.sh
```

### **Bot Updates**
```bash
# Stop bot
sudo systemctl stop cla-bot

# Backup current version
sudo cp -r /opt/cla-bot /opt/cla-bot.backup.$(date +%Y%m%d)

# Update code (git pull or file upload)
cd /opt/cla-bot
git pull origin main

# Update dependencies
sudo -u cla-bot /opt/cla-bot/.venv/bin/pip install -r requirements.txt

# Restart bot
sudo systemctl start cla-bot

# Verify update
cla-bot-status
```

---

## 📞 **SUPPORT & MONITORING**

### **Useful Commands**
```bash
# Quick status check
cla-bot-status

# Service management
sudo systemctl {start|stop|restart|status} cla-bot

# Log monitoring
sudo journalctl -u cla-bot -f

# Health monitoring
watch -n 5 'curl -s http://127.0.0.1:8080/health | jq'
```

### **Alert Setup**
Consider setting up external monitoring:
- **Uptime monitoring**: Ping health endpoint
- **Log monitoring**: Parse error logs
- **Resource monitoring**: CPU, memory, disk usage

---

## ✅ **DEPLOYMENT VERIFICATION CHECKLIST**

After deployment, verify:

- [ ] ✅ Bot service is running: `systemctl status cla-bot`
- [ ] ✅ Health check responds: `curl http://127.0.0.1:8080/health`
- [ ] ✅ All 8 groups are connected (check logs)
- [ ] ✅ CA extraction is working (monitor logs)
- [ ] ✅ Forwarding to all 3 destinations works
- [ ] ✅ Database is accessible and healthy
- [ ] ✅ Backups are being created automatically
- [ ] ✅ Log rotation is configured
- [ ] ✅ Firewall is properly configured
- [ ] ✅ Performance metrics are within expected ranges

---

## 🎯 **SUCCESS CRITERIA**

Your deployment is successful when:
- 🎉 **Bot processes messages in < 10ms average**
- 🎉 **All integrations (BonkBot, CLA v2.0, Monaco PNL) receive CAs**
- 🎉 **Health check returns "healthy" status**
- 🎉 **No errors in logs for 1+ hours**
- 🎉 **Automatic backups are working**
- 🎉 **System resources are stable**

**Your CLA v2.0 Telegram Bot is now ready for production use!** 🚀
