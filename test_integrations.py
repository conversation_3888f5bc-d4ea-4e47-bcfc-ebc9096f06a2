#!/usr/bin/env python3
"""
Integration testing script for CLA v2.0 Telegram Bot
Tests forwarding destinations and group access
"""

import asyncio
from config import config

async def test_forwarding_destinations():
    """Test all configured forwarding destinations"""
    print("🔍 Testing forwarding destinations...")

    destinations = []
    issues = []

    # Check BonkBot
    if config.bonkbot.chat_id:
        destinations.append(f"BonkBot: {config.bonkbot.chat_id}")
    else:
        issues.append("❌ BonkBot chat ID is not configured")

    # Check CLA v2.0 channel
    if config.cla_v2_group.group_id:
        destinations.append(f"CLA v2.0: {config.cla_v2_group.group_id}")
    else:
        issues.append("❌ CLA v2.0 group ID is not configured")

    # Check Monaco PNL
    if config.monaco_pnl.group_id:
        destinations.append(f"Monaco PNL: {config.monaco_pnl.group_id}")
    else:
        issues.append("❌ Monaco PNL group ID is not configured")

    # Check WINNERS group
    if config.winners_group.group_id:
        destinations.append(f"WINNERS: {config.winners_group.group_id}")
    else:
        issues.append("❌ WINNERS group ID is not configured")

    print(f"📊 Found {len(destinations)} configured destinations:")
    for dest in destinations:
        print(f"   ✅ {dest}")

    if issues:
        print(f"\n⚠️ Found {len(issues)} configuration issues:")
        for issue in issues:
            print(f"   {issue}")
        return False

    return True

async def test_group_configuration():
    """Test group configuration"""
    print("\n🔍 Testing group configuration...")

    # Test high-volume groups
    high_volume = config.trending.high_volume_groups
    print(f"📊 High-volume groups ({len(high_volume)}):")
    for group_id in high_volume:
        print(f"   🔥 {group_id}")

    # Test low-volume groups
    low_volume = config.trending.low_volume_groups
    print(f"📊 Low-volume groups ({len(low_volume)}):")
    for group_id in low_volume:
        print(f"   ⚡ {group_id}")

    # Test active groups
    active_groups = config.target_group.active_group_ids
    print(f"📊 Total active groups: {len(active_groups)}")

    # Validate no overlaps
    high_set = set(high_volume)
    low_set = set(low_volume)
    overlap = high_set & low_set

    if overlap:
        print(f"❌ Found overlapping groups: {overlap}")
        return False

    print("✅ No overlapping groups found")
    return True

async def test_trending_configuration():
    """Test trending analysis configuration"""
    print("\n🔍 Testing trending configuration...")

    trending = config.trending
    print(f"📊 Trending enabled: {trending.enabled}")
    print(f"📊 Time window: {trending.time_window_minutes} minutes")
    print(f"📊 Min mentions: {trending.min_mentions}")
    print(f"📊 Selective protection: {trending.selective_protection}")

    # Check anti-pump settings
    print(f"📊 Min time spread: {trending.min_time_spread_seconds} seconds")
    print(f"📊 Max velocity: {trending.max_velocity_mentions_per_minute} mentions/min")
    print(f"📊 Pump detection: {trending.pump_detection_enabled}")

    if trending.min_mentions < 3:
        print("⚠️ Very low mention threshold - may cause spam")

    if trending.time_window_minutes < 5:
        print("⚠️ Very short time window - may miss trends")

    print("✅ Trending configuration looks reasonable")
    return True

async def test_database_connection():
    """Test database connection and basic operations"""
    print("\n🔍 Testing database connection...")

    try:
        from src.database import DatabaseManager

        db = DatabaseManager()
        await db.initialize()
        print("✅ Database connection successful")

        # Test basic operations
        test_ca = "11111111111111111111111111111111111111111111"

        # Test adding a CA
        result = await db.add_contract_address(test_ca, 12345, -1001234567890)
        print(f"✅ Test CA add operation: {result}")

        # Test checking if CA is processed
        is_processed = await db.is_ca_processed(test_ca)
        print(f"✅ Test CA check operation: {is_processed}")

        # Get stats
        stats = await db.get_stats()
        print(f"✅ Database stats retrieved: {len(stats)} metrics")

        await db.close()
        return True

    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

async def test_ca_detection():
    """Test contract address detection"""
    print("\n🔍 Testing CA detection...")

    try:
        from src.message_parser import MessageParser

        parser = MessageParser()

        # Test messages with CAs
        test_messages = [
            "Check out this token: 11111111111111111111111111111111111111111111",
            "New gem found! CA: 22222222222222222222222222222222222222222222",
            "🚀 PUMP incoming: 33333333333333333333333333333333333333333333",
            "No CA in this message",
            "Multiple CAs: 44444444444444444444444444444444444444444444 and 55555555555555555555555555555555555555555555"
        ]

        for i, message in enumerate(test_messages):
            cas = parser.extract_contract_addresses(message)
            print(f"   Test {i+1}: Found {len(cas)} CAs - {cas}")

        print("✅ CA detection working")
        return True

    except Exception as e:
        print(f"❌ CA detection test failed: {e}")
        return False

async def main():
    """Run all pre-deployment validation tests"""
    print("🚀 CLA v2.0 Bot - Pre-Deployment Validation")
    print("=" * 60)

    tests = [
        ("Forwarding Destinations", test_forwarding_destinations),
        ("Group Configuration", test_group_configuration),
        ("Trending Configuration", test_trending_configuration),
        ("Database Connection", test_database_connection),
        ("CA Detection", test_ca_detection),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            results[test_name] = await test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False

    print("\n" + "=" * 60)
    print("📊 PRE-DEPLOYMENT VALIDATION RESULTS")
    print("=" * 60)

    passed = 0
    total = len(tests)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 Pre-deployment validation passed! Ready for AWS deployment.")
        return True
    else:
        print("⚠️ Some validation tests failed. Please fix issues before deployment.")
        print("\n📋 Next Steps:")
        print("1. Fix any configuration issues identified above")
        print("2. Ensure all forwarding destinations are properly configured")
        print("3. Test Telegram authentication if needed")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)