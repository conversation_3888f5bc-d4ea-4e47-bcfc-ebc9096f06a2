"""Simple test for MEME 1000X CA extraction."""

import sys
sys.path.insert(0, 'src')

def test_meme_ca_extraction():
    """Test CA extraction from MEME 1000X message."""
    print("🧪 Testing MEME 1000X CA Extraction...")
    
    # Actual MEME 1000X message format
    meme_message = """🌞6 Smart Money Buying Now!🌞

🟢🟢🟢🟢🟢🟢

Token: mrmeme (Market Cap: 12.76K)
Buy Price💵    $0.00001276
Amount💳    0.141 SOL【Added】

Contract Address (Click to Copy): Ev8seguhxJiauV69or1SG9imY9iYNvyeNEQH8Vsybonk

#1 🕐 1076m ago【19.84 SOL】➕
#2 🕐 1075m ago【1.7 SOL】
#3 🕐 1053m ago【6.5 SOL】
#4 🕐 1053m ago【2.37 SOL】
#5 🕐 829m ago【3.96 SOL】
#6 🕐 335m ago【6.93 SOL】

Twitter Search🔎： 1️⃣$mrmeme (https://x.com/search?q=$mrmeme)   2️⃣Contract (https://x.com/search?q=Ev8seguhxJiauV69or1SG9imY9iYNvyeNEQH8Vsybonk)

Group delayed by 5 minutes"""
    
    try:
        from src.message_parser import MessageParser
        
        parser = MessageParser()
        
        print(f"\n📋 MEME 1000X Message Analysis:")
        print(f"   Message length: {len(meme_message)} characters")
        print(f"   Expected CA: Ev8seguhxJiauV69or1SG9imY9iYNvyeNEQH8Vsybonk")
        
        # Extract CAs
        extracted_cas = parser.extract_contract_addresses(meme_message)
        
        print(f"\n🔍 CA Extraction Results:")
        print(f"   CAs found: {len(extracted_cas)}")
        for i, ca in enumerate(extracted_cas, 1):
            print(f"   CA {i}: {ca}")
        
        expected_ca = "Ev8seguhxJiauV69or1SG9imY9iYNvyeNEQH8Vsybonk"
        
        if expected_ca in extracted_cas:
            print(f"\n   ✅ Expected CA correctly extracted!")
            print(f"   ✅ MEME 1000X message format supported")
        else:
            print(f"\n   ❌ Expected CA not found")
            print(f"   ❌ MEME 1000X message format may need adjustment")
        
        # Check for Twitter link filtering
        twitter_links = [line for line in meme_message.split('\n') if 'https://x.com' in line]
        print(f"\n🔗 Link Filtering Check:")
        print(f"   Twitter links found: {len(twitter_links)}")
        
        for link in twitter_links:
            print(f"   Link: {link.strip()}")
            # Verify the CA in the link is not extracted separately
            if expected_ca in link:
                print(f"   ✅ CA found in Twitter link (should be filtered)")
        
        return len(extracted_cas) > 0 and expected_ca in extracted_cas
        
    except Exception as e:
        print(f"❌ CA extraction failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cross_group_logic():
    """Test cross-group forwarding logic."""
    print(f"\n🧪 Testing Cross-Group Logic...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        test_ca = "Ev8seguhxJiauV69or1SG9imY9iYNvyeNEQH8Vsybonk"
        
        # Group IDs
        meme_1000x_id = -1002333406905  # High-volume
        whale_signals_id = -1002380594298  # Low-volume
        
        print(f"\n📊 Cross-Group Scenario Test:")
        print(f"   CA: {test_ca}")
        print(f"   MEME 1000X (High-Volume): {meme_1000x_id}")
        print(f"   FREE WHALE SIGNALS (Low-Volume): {whale_signals_id}")
        
        # Test 1: High-volume group (non-trending)
        print(f"\n🔥 High-Volume Group Test:")
        meme_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", meme_1000x_id)
        print(f"   Should forward (non-trending): {meme_forward}")
        print(f"   Expected: False (requires trending)")
        
        # Test 2: Low-volume group (direct forwarding)
        print(f"\n⚡ Low-Volume Group Test:")
        whale_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", whale_signals_id)
        print(f"   Should forward (direct): {whale_forward}")
        print(f"   Expected: True (direct forwarding)")
        
        # Test 3: Make CA trending and test high-volume again
        print(f"\n🔥 High-Volume Group Test (Trending):")
        analyzer.trending_cas.add(test_ca)
        meme_forward_trending = analyzer.should_forward_to_destination(test_ca, "BONKBOT", meme_1000x_id)
        print(f"   Should forward (trending): {meme_forward_trending}")
        print(f"   Expected: True (trending qualified)")
        
        # Test 4: WINNERS exclusion
        print(f"\n❌ WINNERS Exclusion Test:")
        meme_winners = analyzer.should_forward_to_destination(test_ca, "WINNERS", meme_1000x_id)
        whale_winners = analyzer.should_forward_to_destination(test_ca, "WINNERS", whale_signals_id)
        print(f"   High-volume to WINNERS: {meme_winners}")
        print(f"   Low-volume to WINNERS: {whale_winners}")
        print(f"   Expected: Both False (globally excluded)")
        
        # Verify results
        success = (
            not meme_forward and  # High-volume non-trending should not forward
            whale_forward and     # Low-volume should forward
            meme_forward_trending and  # High-volume trending should forward
            not meme_winners and  # WINNERS should be excluded
            not whale_winners     # WINNERS should be excluded
        )
        
        if success:
            print(f"\n   ✅ All cross-group logic working correctly!")
        else:
            print(f"\n   ❌ Cross-group logic has issues")
        
        return success
        
    except Exception as e:
        print(f"❌ Cross-group logic test failed: {e}")
        return False

def main():
    """Run MEME 1000X tests."""
    print("🚀 MEME 1000X CA Reception & Cross-Group Testing\n")
    
    try:
        # Test 1: CA extraction
        ca_extraction_success = test_meme_ca_extraction()
        
        # Test 2: Cross-group logic
        cross_group_success = test_cross_group_logic()
        
        if ca_extraction_success and cross_group_success:
            print(f"\n🎉 All MEME 1000X tests passed!")
            
            print(f"\n📋 Summary:")
            print(f"✅ MEME 1000X CA Extraction: Working")
            print(f"✅ Cross-Group Logic: Working")
            print(f"✅ High-Volume Filtering: Working")
            print(f"✅ Low-Volume Direct Forwarding: Working")
            print(f"✅ WINNERS Exclusion: Working")
            
            print(f"\n💡 Answer to Your Question:")
            print(f"When a coin doesn't meet trending threshold in MEME 1000X")
            print(f"but appears again from another group:")
            print(f"")
            print(f"🔥 MEME 1000X (High-Volume):")
            print(f"   - Single mention → Not trending → Not forwarded")
            print(f"   - Needs 6 mentions in 8 minutes to trend")
            print(f"")
            print(f"⚡ Low-Volume Group (e.g., FREE WHALE SIGNALS):")
            print(f"   - Same CA → Direct forwarding → Forwarded immediately")
            print(f"   - No trending requirement")
            print(f"")
            print(f"🔄 Global Duplicate Prevention:")
            print(f"   - CA only processed once across all groups")
            print(f"   - 7-day cache prevents reprocessing")
            print(f"")
            print(f"🎯 Result: Low-volume groups can 'rescue' CAs that")
            print(f"   didn't meet high-volume trending thresholds!")
            
        else:
            print(f"\n❌ Some tests failed")
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
