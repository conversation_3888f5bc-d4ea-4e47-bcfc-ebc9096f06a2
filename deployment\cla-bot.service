# CLA v2.0 Telegram Bot - Systemd Service File
# =============================================
# Installation: sudo cp deployment/cla-bot.service /etc/systemd/system/
# Enable: sudo systemctl enable cla-bot
# Start: sudo systemctl start cla-bot
# Status: sudo systemctl status cla-bot
# Logs: sudo journalctl -u cla-bot -f

[Unit]
Description=CLA v2.0 Telegram Bot - Memecoin Signal Monitoring
Documentation=https://github.com/your-repo/cla-v2-bot
After=network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=cla-bot
Group=cla-bot
WorkingDirectory=/opt/cla-bot
ExecStart=/opt/cla-bot/.venv/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# Environment
Environment=PYTHONPATH=/opt/cla-bot
Environment=ENVIRONMENT=production
EnvironmentFile=/opt/cla-bot/.env

# Restart policy
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/cla-bot/data /opt/cla-bot/logs /opt/cla-bot/backups
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cla-bot

# Health check
ExecStartPost=/bin/sleep 10
ExecStartPost=/bin/bash -c 'curl -f http://127.0.0.1:8080/status || exit 1'

[Install]
WantedBy=multi-user.target
