"""Interactive configuration script for CLA v2.0 Bot."""

import os
import re

def get_user_input():
    """Get configuration from user input."""
    print("🔧 CLA v2.0 Bot Configuration")
    print("=" * 40)
    print()
    print("Please enter your Telegram API credentials:")
    print("(Get them from: https://my.telegram.org/apps)")
    print()
    
    # Get API ID
    while True:
        api_id = input("Enter your API ID (numeric): ").strip()
        if api_id.isdigit():
            break
        print("❌ API ID must be a number. Please try again.")
    
    # Get API Hash
    while True:
        api_hash = input("Enter your API Hash: ").strip()
        if len(api_hash) >= 32:
            break
        print("❌ API Hash seems too short. Please check and try again.")
    
    # Get phone number
    while True:
        phone = input("Enter your phone number (with country code, e.g., +1234567890): ").strip()
        if re.match(r'^\+\d{10,15}$', phone):
            break
        print("❌ Phone number must start with + and contain 10-15 digits. Please try again.")
    
    # Optional: BonkBot chat ID
    print()
    print("Optional: BonkBot Integration (for auto-trading)")
    bonkbot_chat_id = input("Enter BonkBot chat ID (or press Enter to skip): ").strip()
    
    return {
        'api_id': api_id,
        'api_hash': api_hash,
        'phone': phone,
        'bonkbot_chat_id': bonkbot_chat_id if bonkbot_chat_id else 'your_bonkbot_chat_id'
    }

def update_env_file(config):
    """Update .env file with user configuration."""
    try:
        # Read current .env file
        with open('.env', 'r') as f:
            content = f.read()
        
        # Replace placeholders
        content = content.replace('your_api_id_here', config['api_id'])
        content = content.replace('your_api_hash_here', config['api_hash'])
        content = content.replace('your_phone_number_here', config['phone'])
        content = content.replace('your_bonkbot_chat_id', config['bonkbot_chat_id'])
        
        # Write updated content
        with open('.env', 'w') as f:
            f.write(content)
        
        print("✅ Configuration saved to .env file")
        return True
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")
        return False

def validate_configuration():
    """Validate the configuration."""
    print("\n🔍 Validating configuration...")
    
    # Check if .env exists and has valid values
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    with open('.env', 'r') as f:
        content = f.read()
    
    # Check for placeholder values
    if 'your_api_id_here' in content:
        print("❌ API ID not configured")
        return False
    
    if 'your_api_hash_here' in content:
        print("❌ API Hash not configured")
        return False
    
    if 'your_phone_number_here' in content:
        print("❌ Phone number not configured")
        return False
    
    print("✅ Configuration looks good!")
    return True

def main():
    """Main configuration function."""
    print("🚀 Welcome to CLA v2.0 Bot Configuration!")
    print()
    
    # Check if already configured
    if validate_configuration():
        print("✅ Bot is already configured!")
        choice = input("Do you want to reconfigure? (y/N): ").lower().strip()
        if choice != 'y':
            print("Configuration skipped.")
            return True
    
    # Get user input
    try:
        config = get_user_input()
    except KeyboardInterrupt:
        print("\n\nConfiguration cancelled.")
        return False
    
    # Update .env file
    if not update_env_file(config):
        return False
    
    # Validate configuration
    if not validate_configuration():
        return False
    
    print("\n🎉 Configuration Complete!")
    print("\n📋 Next Steps:")
    print("1. Run: python main.py")
    print("2. Enter verification code from Telegram")
    print("3. Bot will start monitoring automatically")
    
    # Ask if user wants to start the bot now
    print()
    start_now = input("Do you want to start the bot now? (Y/n): ").lower().strip()
    if start_now != 'n':
        print("\n🚀 Starting CLA v2.0 Bot...")
        return 'start_bot'
    
    return True

if __name__ == "__main__":
    result = main()
    if result == 'start_bot':
        # Start the bot
        import subprocess
        subprocess.run(['python', 'main.py'])
    elif not result:
        input("\nPress Enter to exit...")
        exit(1)
