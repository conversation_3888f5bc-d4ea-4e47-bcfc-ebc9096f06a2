#!/usr/bin/env python3
"""
Test BUGSIE CA Extraction - Corrected Analysis
Tests the exact BUGSIE message format with the correct 44-character CA
"""

import asyncio
import sys
sys.path.append('.')

async def test_bugsie_ca_extraction():
    """Test BUGSIE CA extraction with the corrected analysis."""
    print("🧪 TESTING BUGSIE CA EXTRACTION (CORRECTED)")
    print("=" * 60)
    
    # The exact BUGSIE message format
    bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472

📊 Token details
├ PRICE:    $0
├ MC:       $71.5K /3.5X from VIP/
├ Vol:      $3.2K


🔒 Security
├ Dev S:    🟢
├ Dex P:    🟢
├ Risk:     only available for Premium users
├ Bundled:  only available for Premium users


🤖 Bots
└ only available for premium users

📈 Charts + Exchanges
└ only available for premium users
├ Axiom (https://axiom.trade/t/GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

💰 JOIN PREMIUM  TO CATCH THE NEXT 3.5X! 🔥🚀"""
    
    # The expected CA (verified as 44 characters)
    expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
    
    print(f"Expected CA: {expected_ca}")
    print(f"Verified Length: {len(expected_ca)} characters")
    print()
    
    try:
        from src.message_parser import MessageParser
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        parser = MessageParser()
        db_manager = DatabaseManager()
        await db_manager.initialize()
        ca_detector = CADetector(db_manager)
        
        print("✅ Components initialized")
        print()
        
        # Test 1: Direct CA validation
        print("🧪 TEST 1: CA VALIDATION")
        print("-" * 30)
        is_valid = parser._validate_solana_ca(expected_ca)
        print(f"CA validation result: {'✅ PASS' if is_valid else '❌ FAIL'}")
        
        if not is_valid:
            print("❌ CA validation failed - investigating...")
            
            # Test base58 validation
            try:
                import base58
                decoded = base58.b58decode(expected_ca)
                print(f"Base58 decode: ✅ Success ({len(decoded)} bytes)")
                print(f"32-byte format: {'✅' if len(decoded) == 32 else '❌'}")
                print(f"Not all zeros: {'✅' if decoded != b'\\x00' * 32 else '❌'}")
            except Exception as e:
                print(f"Base58 decode: ❌ Failed - {e}")
        
        print()
        
        # Test 2: Activity Tracker format detection
        print("🧪 TEST 2: ACTIVITY TRACKER FORMAT DETECTION")
        print("-" * 30)
        is_activity_format = parser._is_activity_tracker_format(bugsie_message)
        print(f"Activity Tracker format: {'✅ DETECTED' if is_activity_format else '❌ NOT DETECTED'}")
        
        if is_activity_format:
            print("✅ Message correctly identified as Activity Tracker format")
        else:
            print("❌ Message not identified as Activity Tracker format")
            
            # Check individual indicators
            indicators = [
                "🔥 ACTIVITY DETECTED 🔥",
                "📊 Token details", 
                "🔒 Security",
                "├ PRICE:",
                "├ MC:",
                "from VIP"
            ]
            
            print("Checking individual indicators:")
            for indicator in indicators:
                found = indicator.upper() in bugsie_message.upper()
                print(f"  '{indicator}': {'✅' if found else '❌'}")
        
        print()
        
        # Test 3: Activity Tracker CA extraction
        print("🧪 TEST 3: ACTIVITY TRACKER CA EXTRACTION")
        print("-" * 30)
        
        if is_activity_format:
            activity_cas = parser._extract_activity_tracker_cas(bugsie_message)
            print(f"Activity Tracker CAs: {activity_cas}")
            
            if expected_ca in activity_cas:
                print("✅ CA successfully extracted by Activity Tracker method")
            else:
                print("❌ CA not extracted by Activity Tracker method")
                
                # Debug the specific line
                lines = bugsie_message.split('\\n')
                for i, line in enumerate(lines):
                    if expected_ca in line:
                        print(f"Found CA in line {i}: '{line}'")
                        cleaned = line.replace('├', '').replace('└', '').replace('│', '').strip()
                        print(f"After cleaning: '{cleaned}'")
                        print(f"Cleaned length: {len(cleaned)}")
                        print(f"Matches expected: {'✅' if cleaned == expected_ca else '❌'}")
        
        print()
        
        # Test 4: Full message parser
        print("🧪 TEST 4: FULL MESSAGE PARSER")
        print("-" * 30)
        extracted_cas = parser.extract_contract_addresses(bugsie_message)
        print(f"Parser extracted CAs: {extracted_cas}")
        
        if expected_ca in extracted_cas:
            print("✅ CA successfully extracted by full parser")
        else:
            print("❌ CA not extracted by full parser")
            
            # Test individual extraction methods
            print("\\nTesting individual extraction methods:")
            
            # Standalone extraction
            cleaned_text = parser._clean_message_text_no_url_extraction(bugsie_message)
            standalone_cas = parser._extract_standalone_cas(cleaned_text)
            print(f"Standalone CAs: {standalone_cas}")
            
            # URL extraction
            url_cas = parser._extract_cas_from_urls(bugsie_message)
            print(f"URL CAs: {url_cas}")
        
        print()
        
        # Test 5: Full CA detector pipeline
        print("🧪 TEST 5: FULL CA DETECTOR PIPELINE")
        print("-" * 30)
        detected_cas = await ca_detector.process_message(bugsie_message, 4856, -1002064145465)
        print(f"CA Detector result: {detected_cas}")
        
        if expected_ca in detected_cas:
            print("✅ CA successfully detected by full pipeline")
        else:
            print("❌ CA not detected by full pipeline")
        
        await db_manager.close()
        
        # Summary
        print()
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        
        tests = [
            ("CA Validation", is_valid),
            ("Activity Format Detection", is_activity_format),
            ("Activity CA Extraction", expected_ca in (activity_cas if is_activity_format else [])),
            ("Full Parser Extraction", expected_ca in extracted_cas),
            ("Full Pipeline Detection", expected_ca in detected_cas)
        ]
        
        passed = sum(1 for _, result in tests if result)
        total = len(tests)
        
        for test_name, result in tests:
            print(f"{test_name}: {'✅ PASS' if result else '❌ FAIL'}")
        
        print(f"\\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED - CA extraction working correctly!")
        else:
            print("⚠️ SOME TESTS FAILED - investigation needed")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_message_processing_timing():
    """Test message processing timing and batching behavior."""
    print("\\n🧪 TESTING MESSAGE PROCESSING TIMING")
    print("=" * 60)
    
    print("SIMULATING HIGH-VOLUME MESSAGE PROCESSING:")
    print("- Testing rapid message succession")
    print("- Measuring processing times")
    print("- Detecting potential batching behavior")
    print()
    
    try:
        from src.message_parser import MessageParser
        import time
        
        parser = MessageParser()
        
        # Simulate rapid messages
        test_messages = [
            f"Message {i}: GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk" 
            for i in range(10)
        ]
        
        processing_times = []
        
        for i, message in enumerate(test_messages):
            start_time = time.perf_counter()
            
            # Process message
            extracted_cas = parser.extract_contract_addresses(message)
            
            end_time = time.perf_counter()
            processing_time = (end_time - start_time) * 1000
            processing_times.append(processing_time)
            
            print(f"Message {i+1}: {processing_time:.2f}ms | CAs: {len(extracted_cas)}")
            
            # Small delay to simulate real message timing
            time.sleep(0.001)  # 1ms delay
        
        # Analyze timing
        avg_time = sum(processing_times) / len(processing_times)
        max_time = max(processing_times)
        min_time = min(processing_times)
        
        print(f"\\nTiming Analysis:")
        print(f"Average: {avg_time:.2f}ms")
        print(f"Maximum: {max_time:.2f}ms")
        print(f"Minimum: {min_time:.2f}ms")
        
        if avg_time < 1.0:
            print("✅ Processing time excellent (<1ms average)")
        elif avg_time < 10.0:
            print("✅ Processing time good (<10ms average)")
        else:
            print("⚠️ Processing time may be slow for high-volume scenarios")
        
        return True
        
    except Exception as e:
        print(f"❌ Timing test failed: {e}")
        return False

async def main():
    """Run all corrected tests."""
    print("🚀 BUGSIE CA EXTRACTION - CORRECTED ANALYSIS")
    print("=" * 80)
    print("CA: GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk (44 chars)")
    print("=" * 80)
    
    # Run tests
    extraction_success = await test_bugsie_ca_extraction()
    timing_success = await test_message_processing_timing()
    
    print("\\n🎯 FINAL RESULTS")
    print("=" * 80)
    print(f"CA Extraction Tests: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    print(f"Timing Tests: {'✅ PASS' if timing_success else '❌ FAIL'}")
    
    if extraction_success and timing_success:
        print("\\n🎉 ALL TESTS PASSED!")
        print("✅ BUGSIE CA extraction should work correctly")
        print("✅ Message processing timing is optimal")
    else:
        print("\\n⚠️ ISSUES IDENTIFIED")
        print("Further investigation and fixes needed")
    
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(main())
