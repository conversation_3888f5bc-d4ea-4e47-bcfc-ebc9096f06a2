"""Monitor bot startup and track performance metrics."""

import asyncio
import time
import sys
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def monitor_startup():
    """Monitor bot startup process."""
    print("🔍 MONITORING BOT STARTUP")
    print("=" * 40)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Test configuration loading
        print("1️⃣ Testing configuration...")
        from config import config
        print(f"   ✅ Configuration loaded")
        print(f"   📊 Monitored groups: {len(config.target_group.additional_group_ids) + 1}")
        print(f"   📤 BonkBot username: {config.bonkbot.username}")
        print(f"   📤 BonkBot chat_id: {config.bonkbot.chat_id or 'Using username'}")
        
        # Test database
        print("\n2️⃣ Testing database...")
        from src.database import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        print(f"   ✅ Database initialized")
        await db_manager.close()
        
        # Test telegram client
        print("\n3️⃣ Testing telegram client...")
        from src.telegram_client import TelegramClientManager
        telegram_client = TelegramClientManager()
        print(f"   ✅ Telegram client created")
        
        # Test bot initialization
        print("\n4️⃣ Testing bot initialization...")
        from src.bot import CLABot
        bot = CLABot()
        print(f"   ✅ Bot instance created")
        
        # Test dependency container
        print("\n5️⃣ Testing dependency container...")
        from src.dependency_container import DependencyContainer
        container = DependencyContainer()
        print(f"   ✅ Dependency container created")
        
        print("\n✅ All components can be imported and initialized")
        print("🚀 Bot should be able to start successfully")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Startup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_bot_with_monitoring():
    """Run the bot with startup monitoring."""
    print("\n🚀 STARTING BOT WITH MONITORING")
    print("=" * 40)
    
    try:
        # Import and run the main bot
        from main import CLABotRunner
        
        runner = CLABotRunner()
        
        print("📊 Initializing bot components...")
        await runner.initialize()
        print("✅ Bot initialized successfully")
        
        print("🔄 Starting bot...")
        await runner.start()
        print("✅ Bot started successfully")
        
        print("🎯 Bot is now running and monitoring for messages...")
        
        # Keep running
        while runner.running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot error: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main monitoring function."""
    # First test if components can be loaded
    startup_ok = await monitor_startup()
    
    if startup_ok:
        print("\n" + "="*50)
        input("Press Enter to start the bot with monitoring...")
        await run_bot_with_monitoring()
    else:
        print("\n❌ Startup tests failed. Please fix the issues before running the bot.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Monitoring stopped by user")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        sys.exit(1)
