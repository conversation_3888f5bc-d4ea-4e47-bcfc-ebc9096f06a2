"""CLA v2.0 channel integration for CLA v2.0 Bot."""

import asyncio
from typing import Optional, List
from datetime import datetime
from loguru import logger

from src.telegram_client import TelegramClientManager
from config import config

class CLAv2Integration:
    """Handles integration with CLA v2.0 channel for CA forwarding."""
    
    def __init__(self, telegram_client: TelegramClientManager):
        self.telegram_client = telegram_client
        self.cla_v2_entity = None
        self.message_queue = asyncio.Queue()
        self.processing_queue = False

        # Queue-level duplicate prevention
        self.queued_cas = set()  # Track CAs currently in queue
        self.recent_sent_cas = {}  # CA -> timestamp for recent sends
    
    async def initialize(self):
        """Initialize CLA v2.0 channel integration."""
        try:
            # Get CLA v2.0 channel entity
            self.cla_v2_entity = await self.telegram_client.get_entity(config.cla_v2_group.group_id)
            
            if not self.cla_v2_entity:
                logger.error(f"Failed to find CLA v2.0 channel: {config.cla_v2_group.group_id}")
                return False
            
            logger.info(f"CLA v2.0 integration initialized: {config.cla_v2_group.group_name} ({config.cla_v2_group.group_id})")
            
            # Start message queue processor
            asyncio.create_task(self._process_message_queue())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize CLA v2.0 integration: {e}")
            return False
    
    async def _is_ca_recently_sent(self, ca: str) -> bool:
        """Check if CA was recently sent to prevent duplicates."""
        from datetime import datetime, timedelta

        now = datetime.now()
        # Clean up old entries (older than 5 minutes)
        cutoff_time = now - timedelta(minutes=5)
        expired_cas = [c for c, timestamp in self.recent_sent_cas.items() if timestamp < cutoff_time]
        for c in expired_cas:
            del self.recent_sent_cas[c]

        return ca in self.recent_sent_cas

    async def send_ca_to_cla_v2(self, ca: str, source_message: str = "", source_group: str = "") -> bool:
        """Send contract address to CLA v2.0 channel."""
        try:
            # Check for queue-level duplicates
            if ca in self.queued_cas or await self._is_ca_recently_sent(ca):
                logger.debug(f"CLA v2.0: CA {ca} already queued or recently sent - skipping")
                return False

            # Format message for CLA v2.0 channel
            formatted_message = self._format_ca_message(ca, source_message, source_group)

            # Add to queue for processing
            self.queued_cas.add(ca)
            await self.message_queue.put({
                'ca': ca,
                'message': formatted_message,
                'timestamp': datetime.now(),
                'source': source_message[:100] if source_message else "",
                'source_group': source_group
            })

            logger.info(f"CA queued for CLA v2.0 channel: {ca}")
            return True

        except Exception as e:
            logger.error(f"Failed to queue CA for CLA v2.0 channel: {e}")
            return False
    
    def _format_ca_message(self, ca: str, source_message: str = "", source_group: str = "") -> str:
        """Format contract address message for CLA v2.0 channel - optimized concise format."""
        # Ultra-concise format: just the CA (most efficient)
        return ca
    
    async def _process_message_queue(self):
        """Process queued messages to CLA v2.0 channel."""
        self.processing_queue = True
        logger.info("Started CLA v2.0 channel message queue processor")
        
        while self.processing_queue:
            try:
                # Get message from queue (wait up to 1 second)
                try:
                    message_data = await asyncio.wait_for(
                        self.message_queue.get(), 
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Send message to CLA v2.0 channel
                success = await self._send_message_to_cla_v2(message_data)
                
                if success:
                    logger.info(f"Successfully sent CA to CLA v2.0 channel: {message_data['ca']}")
                else:
                    logger.error(f"Failed to send CA to CLA v2.0 channel: {message_data['ca']}")
                
                # Rate limiting - wait between messages
                await asyncio.sleep(2)  # 2 second delay between messages
                
            except Exception as e:
                logger.error(f"Error in CLA v2.0 message queue processor: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _send_message_to_cla_v2(self, message_data: dict) -> bool:
        """Send individual message to CLA v2.0 channel."""
        try:
            if not self.cla_v2_entity:
                logger.error("CLA v2.0 channel entity not initialized")
                return False
            
            # Send the message
            success = await self.telegram_client.send_message(
                self.cla_v2_entity, 
                message_data['message']
            )
            
            if success:
                logger.debug(f"Message sent to CLA v2.0 channel: {message_data['message']}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to CLA v2.0 channel: {e}")
            return False
    
    async def send_multiple_cas(self, cas: List[str], source_message: str = "", source_group: str = "") -> int:
        """Send multiple contract addresses to CLA v2.0 channel with optimized performance."""
        success_count = 0

        for ca in cas:
            if await self.send_ca_to_cla_v2(ca, source_message, source_group):
                success_count += 1

            # PERFORMANCE OPTIMIZATION: Reduced delay from 500ms to 50ms
            # Queue-based system doesn't need aggressive rate limiting
            await asyncio.sleep(0.05)  # 50ms instead of 500ms

        logger.info(f"Queued {success_count}/{len(cas)} CAs for CLA v2.0 channel")
        return success_count
    
    async def test_cla_v2_connection(self) -> bool:
        """Test connection to CLA v2.0 channel."""
        try:
            if not self.cla_v2_entity:
                logger.error("CLA v2.0 channel entity not initialized")
                return False
            
            logger.info("CLA v2.0 channel connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"CLA v2.0 channel connection test error: {e}")
            return False
    
    async def get_queue_status(self) -> dict:
        """Get status of the message queue."""
        return {
            'queue_size': self.message_queue.qsize(),
            'processing': self.processing_queue,
            'cla_v2_connected': self.cla_v2_entity is not None
        }
    
    async def clear_queue(self):
        """Clear the message queue."""
        try:
            while not self.message_queue.empty():
                await self.message_queue.get()
            logger.info("CLA v2.0 channel message queue cleared")
        except Exception as e:
            logger.error(f"Error clearing CLA v2.0 queue: {e}")
    
    async def pause_processing(self):
        """Pause message queue processing."""
        self.processing_queue = False
        logger.info("CLA v2.0 channel message processing paused")
    
    async def resume_processing(self):
        """Resume message queue processing."""
        if not self.processing_queue:
            self.processing_queue = True
            asyncio.create_task(self._process_message_queue())
            logger.info("CLA v2.0 channel message processing resumed")
    
    async def send_custom_message(self, message: str) -> bool:
        """Send a custom message to CLA v2.0 channel."""
        try:
            if not self.cla_v2_entity:
                logger.error("CLA v2.0 channel entity not initialized")
                return False
            
            success = await self.telegram_client.send_message(
                self.cla_v2_entity, 
                message
            )
            
            if success:
                logger.info(f"Custom message sent to CLA v2.0 channel: {message}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending custom message to CLA v2.0 channel: {e}")
            return False

    async def send_status_report(self, report: str) -> bool:
        """Send concise hourly status report to CLA v2.0 channel."""
        try:
            if not self.cla_v2_entity:
                logger.error("CLA v2.0 entity not initialized for status report")
                return False

            # Create concise status report (shortened format)
            concise_report = self._create_concise_status_report(report)

            success = await self.telegram_client.send_message(
                self.cla_v2_entity,
                concise_report
            )

            if success:
                logger.info("✅ Concise status report sent to CLA v2.0 channel")
            else:
                logger.warning("⚠️ Failed to send status report to CLA v2.0 channel")

            return success

        except Exception as e:
            logger.error(f"Error sending status report to CLA v2.0 channel: {e}")
            return False

    def _create_concise_status_report(self, full_report: str) -> str:
        """Create a concise version of the status report."""
        try:
            # Extract key metrics from the full report
            lines = full_report.split('\n')

            # Find key metrics
            uptime = "N/A"
            messages = "0"
            cas_detected = "0"
            cas_forwarded = "0"
            errors = "0"

            for line in lines:
                if "Uptime:" in line:
                    uptime = line.split("Uptime:")[-1].strip()
                elif "Messages:" in line and "total_messages" not in line:
                    messages = line.split("Messages:")[-1].strip()
                elif "CAs Detected:" in line:
                    cas_detected = line.split("CAs Detected:")[-1].strip()
                elif "CAs Forwarded:" in line:
                    cas_forwarded = line.split("CAs Forwarded:")[-1].strip()
                elif "Errors:" in line:
                    errors = line.split("Errors:")[-1].strip().split("|")[0].strip()

            # Create concise single-line status
            concise_report = (
                f"🤖 CLA v2 Status: "
                f"⏱️ {uptime} | "
                f"📨 {messages} msgs | "
                f"🔍 {cas_detected} CAs | "
                f"📤 {cas_forwarded} sent | "
                f"❌ {errors} errors"
            )

            return concise_report

        except Exception as e:
            logger.error(f"Error creating concise status report: {e}")
            # Fallback to simple status
            return "🤖 CLA v2 Bot: Active and monitoring"

    def get_cla_v2_info(self) -> dict:
        """Get CLA v2.0 channel configuration info."""
        return {
            'group_id': config.cla_v2_group.group_id,
            'group_name': config.cla_v2_group.group_name,
            'entity_found': self.cla_v2_entity is not None,
            'queue_processing': self.processing_queue
        }
    
    async def shutdown(self):
        """Shutdown CLA v2.0 channel integration."""
        logger.info("Shutting down CLA v2.0 channel integration")
        self.processing_queue = False
        await self.clear_queue()
        logger.info("CLA v2.0 channel integration shutdown complete")
