#!/bin/bash
# CLA v2.0 Telegram Bot - Security Hardening Script
# =================================================
# This script applies security hardening measures for production deployment
# Run as root: sudo bash deployment/security_hardening.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BOT_USER="cla-bot"
BOT_GROUP="cla-bot"
INSTALL_DIR="/opt/cla-bot"

echo -e "${BLUE}🔒 CLA v2.0 Telegram Bot - Security Hardening${NC}"
echo -e "${BLUE}=============================================${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
   exit 1
fi

echo -e "${YELLOW}🔍 Applying security hardening measures...${NC}"

# 1. File Permissions and Ownership
echo -e "${BLUE}📁 Setting secure file permissions...${NC}"

# Set ownership
chown -R "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR"

# Set directory permissions
chmod 755 "$INSTALL_DIR"
chmod 750 "$INSTALL_DIR/data"
chmod 750 "$INSTALL_DIR/logs"
chmod 750 "$INSTALL_DIR/backups"
chmod 755 "$INSTALL_DIR/src"

# Set file permissions
find "$INSTALL_DIR" -type f -name "*.py" -exec chmod 644 {} \;
chmod +x "$INSTALL_DIR/main.py"
chmod +x "$INSTALL_DIR/scripts/"*.sh

# Secure sensitive files
chmod 640 "$INSTALL_DIR/.env"
chmod 640 "$INSTALL_DIR/.env.production"

# Database permissions
if [[ -f "$INSTALL_DIR/data/cla_bot.db" ]]; then
    chmod 640 "$INSTALL_DIR/data/cla_bot.db"
fi

echo -e "${GREEN}✅ File permissions configured${NC}"

# 2. System Security Updates
echo -e "${BLUE}🔄 Updating system packages...${NC}"
apt update && apt upgrade -y

# 3. Firewall Configuration
echo -e "${BLUE}🔥 Configuring firewall...${NC}"

# Enable UFW
ufw --force enable

# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (be careful with this)
ufw allow ssh

# Allow health check and metrics (local only)
ufw allow from 127.0.0.1 to any port 8080
ufw allow from 127.0.0.1 to any port 8081

# Allow specific monitoring IPs if needed
# ufw allow from YOUR_MONITORING_IP to any port 8080

echo -e "${GREEN}✅ Firewall configured${NC}"

# 4. Fail2Ban Configuration
echo -e "${BLUE}🛡️ Configuring Fail2Ban...${NC}"

# Install fail2ban if not already installed
apt install -y fail2ban

# Create custom jail for bot logs
cat > /etc/fail2ban/jail.d/cla-bot.conf << 'EOF'
[cla-bot-auth]
enabled = true
port = ssh
filter = cla-bot-auth
logpath = /opt/cla-bot/logs/cla_bot.log
maxretry = 3
bantime = 3600
findtime = 600

[cla-bot-errors]
enabled = true
port = ssh
filter = cla-bot-errors
logpath = /opt/cla-bot/logs/cla_bot.log
maxretry = 10
bantime = 1800
findtime = 300
EOF

# Create custom filters
mkdir -p /etc/fail2ban/filter.d

cat > /etc/fail2ban/filter.d/cla-bot-auth.conf << 'EOF'
[Definition]
failregex = ^.*SECURITY.*authentication.*failed.*from.*<HOST>.*$
            ^.*ALERT.*unauthorized.*access.*from.*<HOST>.*$
ignoreregex =
EOF

cat > /etc/fail2ban/filter.d/cla-bot-errors.conf << 'EOF'
[Definition]
failregex = ^.*ERROR.*suspicious.*activity.*from.*<HOST>.*$
            ^.*WARNING.*rate.*limit.*exceeded.*from.*<HOST>.*$
ignoreregex =
EOF

# Restart fail2ban
systemctl restart fail2ban
systemctl enable fail2ban

echo -e "${GREEN}✅ Fail2Ban configured${NC}"

# 5. SSH Hardening
echo -e "${BLUE}🔐 Hardening SSH configuration...${NC}"

# Backup original SSH config
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# Apply SSH hardening
cat >> /etc/ssh/sshd_config << 'EOF'

# CLA Bot Security Hardening
Protocol 2
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys
PermitEmptyPasswords no
ChallengeResponseAuthentication no
UsePAM yes
X11Forwarding no
PrintMotd no
ClientAliveInterval 300
ClientAliveCountMax 2
MaxAuthTries 3
MaxSessions 2
LoginGraceTime 60
EOF

# Restart SSH (be careful!)
echo -e "${YELLOW}⚠️ Restarting SSH service...${NC}"
systemctl restart sshd

echo -e "${GREEN}✅ SSH hardened${NC}"

# 6. System Limits and Security
echo -e "${BLUE}⚙️ Configuring system security limits...${NC}"

# Set limits for bot user
cat > /etc/security/limits.d/cla-bot.conf << EOF
$BOT_USER soft nofile 65536
$BOT_USER hard nofile 65536
$BOT_USER soft nproc 4096
$BOT_USER hard nproc 4096
$BOT_USER soft memlock unlimited
$BOT_USER hard memlock unlimited
EOF

# Kernel security parameters
cat > /etc/sysctl.d/99-cla-bot-security.conf << 'EOF'
# Network security
net.ipv4.ip_forward = 0
net.ipv4.conf.all.send_redirects = 0
net.ipv4.conf.default.send_redirects = 0
net.ipv4.conf.all.accept_redirects = 0
net.ipv4.conf.default.accept_redirects = 0
net.ipv4.conf.all.accept_source_route = 0
net.ipv4.conf.default.accept_source_route = 0
net.ipv4.conf.all.log_martians = 1
net.ipv4.conf.default.log_martians = 1
net.ipv4.icmp_echo_ignore_broadcasts = 1
net.ipv4.icmp_ignore_bogus_error_responses = 1
net.ipv4.tcp_syncookies = 1

# Memory protection
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
kernel.yama.ptrace_scope = 1

# File system security
fs.protected_hardlinks = 1
fs.protected_symlinks = 1
fs.suid_dumpable = 0
EOF

# Apply sysctl settings
sysctl -p /etc/sysctl.d/99-cla-bot-security.conf

echo -e "${GREEN}✅ System security configured${NC}"

# 7. Log Security
echo -e "${BLUE}📝 Securing log files...${NC}"

# Set log file permissions
find "$INSTALL_DIR/logs" -type f -exec chmod 640 {} \;
chown -R "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/logs"

# Configure rsyslog for bot logs
cat > /etc/rsyslog.d/50-cla-bot.conf << EOF
# CLA Bot log forwarding
if \$programname == 'cla-bot' then /var/log/cla-bot.log
& stop
EOF

# Restart rsyslog
systemctl restart rsyslog

echo -e "${GREEN}✅ Log security configured${NC}"

# 8. Process Security
echo -e "${BLUE}🔒 Configuring process security...${NC}"

# Create systemd override for additional security
mkdir -p /etc/systemd/system/cla-bot.service.d

cat > /etc/systemd/system/cla-bot.service.d/security.conf << 'EOF'
[Service]
# Additional security measures
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectHome=true
ProtectSystem=strict
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Working directory restrictions
ReadWritePaths=/opt/cla-bot/data /opt/cla-bot/logs /opt/cla-bot/backups
ReadOnlyPaths=/opt/cla-bot
EOF

# Reload systemd
systemctl daemon-reload

echo -e "${GREEN}✅ Process security configured${NC}"

# 9. Network Security
echo -e "${BLUE}🌐 Configuring network security...${NC}"

# Install and configure iptables-persistent
apt install -y iptables-persistent

# Basic iptables rules
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X

# Default policies
iptables -P INPUT DROP
iptables -P FORWARD DROP
iptables -P OUTPUT ACCEPT

# Allow loopback
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# Allow established connections
iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT

# Allow SSH
iptables -A INPUT -p tcp --dport 22 -m conntrack --ctstate NEW,ESTABLISHED -j ACCEPT

# Allow health check (local only)
iptables -A INPUT -p tcp -s 127.0.0.1 --dport 8080 -j ACCEPT
iptables -A INPUT -p tcp -s 127.0.0.1 --dport 8081 -j ACCEPT

# Rate limiting for SSH
iptables -A INPUT -p tcp --dport 22 -m recent --name ssh --set
iptables -A INPUT -p tcp --dport 22 -m recent --name ssh --rcheck --seconds 60 --hitcount 4 -j DROP

# Save iptables rules
iptables-save > /etc/iptables/rules.v4

echo -e "${GREEN}✅ Network security configured${NC}"

# 10. Monitoring and Alerting
echo -e "${BLUE}📊 Setting up security monitoring...${NC}"

# Create security monitoring script
cat > "$INSTALL_DIR/scripts/security_monitor.sh" << 'EOF'
#!/bin/bash
# Security monitoring script for CLA Bot

LOG_FILE="/opt/cla-bot/logs/security_monitor.log"
BOT_LOG="/opt/cla-bot/logs/cla_bot.log"

# Check for suspicious activities
check_security() {
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Check for failed authentication attempts
    if grep -q "authentication.*failed" "$BOT_LOG" 2>/dev/null; then
        echo "$timestamp: WARNING - Authentication failures detected" >> "$LOG_FILE"
    fi
    
    # Check for unusual resource usage
    local memory_usage=$(ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem -C python3 | head -2 | tail -1 | awk '{print $4}')
    if (( $(echo "$memory_usage > 80" | bc -l) )); then
        echo "$timestamp: WARNING - High memory usage: $memory_usage%" >> "$LOG_FILE"
    fi
    
    # Check for disk space
    local disk_usage=$(df /opt/cla-bot | tail -1 | awk '{print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 85 ]]; then
        echo "$timestamp: WARNING - High disk usage: $disk_usage%" >> "$LOG_FILE"
    fi
    
    # Check for network connections
    local connections=$(netstat -tn | grep :8080 | wc -l)
    if [[ $connections -gt 10 ]]; then
        echo "$timestamp: WARNING - High number of connections: $connections" >> "$LOG_FILE"
    fi
}

check_security
EOF

chmod +x "$INSTALL_DIR/scripts/security_monitor.sh"
chown "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/scripts/security_monitor.sh"

# Add to crontab
(crontab -u "$BOT_USER" -l 2>/dev/null; echo "*/10 * * * * $INSTALL_DIR/scripts/security_monitor.sh") | crontab -u "$BOT_USER" -

echo -e "${GREEN}✅ Security monitoring configured${NC}"

# 11. Final Security Checks
echo -e "${BLUE}🔍 Running final security checks...${NC}"

# Check file permissions
echo "Checking file permissions..."
ls -la "$INSTALL_DIR/.env" | grep -q "640" && echo "✅ .env permissions correct" || echo "❌ .env permissions incorrect"

# Check service status
systemctl is-active --quiet cla-bot && echo "✅ Service is running" || echo "⚠️ Service is not running"

# Check firewall status
ufw status | grep -q "Status: active" && echo "✅ Firewall is active" || echo "❌ Firewall is not active"

# Check fail2ban status
systemctl is-active --quiet fail2ban && echo "✅ Fail2Ban is running" || echo "❌ Fail2Ban is not running"

echo -e "${GREEN}✅ Security hardening completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Security Summary:${NC}"
echo -e "   🔒 File permissions secured"
echo -e "   🔥 Firewall configured and active"
echo -e "   🛡️ Fail2Ban protection enabled"
echo -e "   🔐 SSH hardened"
echo -e "   ⚙️ System security limits applied"
echo -e "   📝 Log security configured"
echo -e "   🔒 Process security enhanced"
echo -e "   🌐 Network security implemented"
echo -e "   📊 Security monitoring active"
echo ""
echo -e "${RED}⚠️ IMPORTANT REMINDERS:${NC}"
echo -e "   1. Test SSH access before logging out"
echo -e "   2. Keep system packages updated regularly"
echo -e "   3. Monitor security logs in /opt/cla-bot/logs/"
echo -e "   4. Review firewall rules periodically"
echo -e "   5. Backup security configurations"
echo ""
echo -e "${GREEN}🎉 Your CLA v2.0 Bot is now security hardened!${NC}"
