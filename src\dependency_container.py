"""Dependency injection container for CLA v2.0 Bot components."""

from typing import Optional, Dict, Any
from loguru import logger

from src.database import DatabaseManager
from src.telegram_client import TelegramClientManager
from src.ca_detector import CADetector
from src.pnl_tracker import P<PERSON><PERSON>racker
from src.trending_analyzer import <PERSON>ren<PERSON><PERSON><PERSON><PERSON>zer
from src.bonkbot_integration import BonkBotIntegration
from src.winners_integration import WinnersIntegration
from src.cla_v2_integration import CLAv2Integration
from src.monaco_pnl_integration import MonacoPNLIntegration
from src.high_volume_ca_analyzer import HighVolumeCAAnalyzer
from src.enhanced_stats_tracker import EnhancedStatsTracker
from src.ca_rescue_tracker import CARescueTracker
from src.message_processor import MessageProcessor, MessageValidator, MessageRouter
from src.ca_analyzer import CAAnalyzer, TrendingPatternDetector
from src.forwarding_manager import ForwardingManager, ForwardingQueue
from src.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from config import config

class DependencyContainer:
    """Manages dependency injection for all bot components."""
    
    def __init__(self):
        """Initialize dependency container."""
        self._instances: Dict[str, Any] = {}
        self._initialized = False
        
        logger.info("Dependency Container initialized")
    
    async def initialize(self):
        """Initialize all dependencies in correct order."""
        if self._initialized:
            logger.warning("Dependency container already initialized")
            return
        
        try:
            logger.info("🔧 Initializing bot dependencies...")
            
            # Core infrastructure
            await self._initialize_database()
            await self._initialize_telegram_client()
            
            # Detection and analysis components
            await self._initialize_ca_detector()
            await self._initialize_trending_analyzer()
            await self._initialize_pnl_tracker()
            
            # Integration components
            await self._initialize_integrations()
            
            # High-level components
            await self._initialize_analyzers()
            await self._initialize_processors()
            await self._initialize_managers()
            
            self._initialized = True
            logger.info("✅ All dependencies initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize dependencies: {e}")
            raise
    
    async def _initialize_database(self):
        """Initialize database manager."""
        db_manager = DatabaseManager()
        await db_manager.initialize()
        self._instances['db_manager'] = db_manager
        logger.info("✅ Database manager initialized")
    
    async def _initialize_telegram_client(self):
        """Initialize Telegram client manager."""
        telegram_client = TelegramClientManager()
        self._instances['telegram_client'] = telegram_client
        logger.info("✅ Telegram client manager initialized")
    
    async def _initialize_ca_detector(self):
        """Initialize CA detector."""
        db_manager = self.get('db_manager')
        ca_detector = CADetector(db_manager)
        self._instances['ca_detector'] = ca_detector
        logger.info("✅ CA detector initialized")
    
    async def _initialize_trending_analyzer(self):
        """Initialize trending analyzer."""
        trending_analyzer = TrendingAnalyzer()
        self._instances['trending_analyzer'] = trending_analyzer
        logger.info("✅ Trending analyzer initialized")
    
    async def _initialize_pnl_tracker(self):
        """Initialize PNL tracker."""
        db_manager = self.get('db_manager')
        pnl_tracker = PNLTracker(db_manager)
        self._instances['pnl_tracker'] = pnl_tracker
        logger.info("✅ PNL tracker initialized")
    
    async def _initialize_integrations(self):
        """Initialize external integrations."""
        # BonkBot integration
        bonkbot_integration = None
        if config.bonkbot.username and config.bonkbot.chat_id:
            telegram_client = self.get('telegram_client')
            bonkbot_integration = BonkBotIntegration(telegram_client)
            logger.info("✅ BonkBot integration initialized")
        else:
            logger.warning("⚠️ BonkBot integration disabled (missing configuration)")
        
        # Winners integration
        winners_integration = None
        if hasattr(config, 'winners_group'):
            telegram_client = self.get('telegram_client')
            winners_integration = WinnersIntegration(telegram_client)
            logger.info("✅ Winners integration initialized")
        else:
            logger.warning("⚠️ Winners integration disabled")
        
        # CLA v2.0 integration
        cla_v2_integration = None
        if hasattr(config, 'cla_v2_group'):
            telegram_client = self.get('telegram_client')
            cla_v2_integration = CLAv2Integration(telegram_client)
            logger.info("✅ CLA v2.0 integration initialized")
        else:
            logger.warning("⚠️ CLA v2.0 integration disabled")
        
        # Monaco PNL integration
        monaco_pnl_integration = None
        if hasattr(config, 'monaco_pnl'):
            telegram_client = self.get('telegram_client')
            monaco_pnl_integration = MonacoPNLIntegration(telegram_client)
            logger.info("✅ Monaco PNL integration initialized")
        else:
            logger.warning("⚠️ Monaco PNL integration disabled")
        
        self._instances['bonkbot_integration'] = bonkbot_integration
        self._instances['winners_integration'] = winners_integration
        self._instances['cla_v2_integration'] = cla_v2_integration
        self._instances['monaco_pnl_integration'] = monaco_pnl_integration
    
    async def _initialize_analyzers(self):
        """Initialize analyzer components."""
        # Error Handler (enhanced error handling with retry logic)
        error_handler = ErrorHandler()
        self._instances['error_handler'] = error_handler
        logger.info("✅ Enhanced error handler initialized")

        # Enhanced Stats Tracker (replacing global singleton)
        enhanced_stats_tracker = EnhancedStatsTracker()
        self._instances['enhanced_stats_tracker'] = enhanced_stats_tracker

        # High Volume CA Analyzer (replacing global singleton)
        high_volume_ca_analyzer = HighVolumeCAAnalyzer()
        self._instances['high_volume_ca_analyzer'] = high_volume_ca_analyzer

        # CA Rescue Tracker (replacing global singleton)
        ca_rescue_tracker = CARescueTracker()
        self._instances['ca_rescue_tracker'] = ca_rescue_tracker

        # CA Analyzer
        ca_detector = self.get('ca_detector')
        trending_analyzer = self.get('trending_analyzer')
        ca_analyzer = CAAnalyzer(
            ca_detector=ca_detector,
            trending_analyzer=trending_analyzer,
            high_volume_analyzer=high_volume_ca_analyzer,
            enhanced_stats_tracker=enhanced_stats_tracker,
            ca_rescue_tracker=ca_rescue_tracker
        )
        self._instances['ca_analyzer'] = ca_analyzer

        # Trending Pattern Detector
        trending_pattern_detector = TrendingPatternDetector()
        self._instances['trending_pattern_detector'] = trending_pattern_detector

        logger.info("✅ Analyzer components initialized")
    
    async def _initialize_processors(self):
        """Initialize processor components."""
        # Message Validator
        message_validator = MessageValidator()
        self._instances['message_validator'] = message_validator
        
        # Message Router
        message_router = MessageRouter()
        self._instances['message_router'] = message_router
        
        logger.info("✅ Processor components initialized")
    
    async def _initialize_managers(self):
        """Initialize manager components."""
        # Get shared dependencies
        enhanced_stats_tracker = self.get('enhanced_stats_tracker')
        ca_analyzer = self.get('ca_analyzer')

        # Forwarding Manager
        bonkbot_integration = self.get('bonkbot_integration')
        cla_v2_integration = self.get('cla_v2_integration')
        monaco_pnl_integration = self.get('monaco_pnl_integration')

        forwarding_manager = ForwardingManager(
            bonkbot_integration=bonkbot_integration,
            cla_v2_integration=cla_v2_integration,
            monaco_pnl_integration=monaco_pnl_integration,
            enhanced_stats_tracker=enhanced_stats_tracker
        )
        self._instances['forwarding_manager'] = forwarding_manager

        # Forwarding Queue
        forwarding_queue = ForwardingQueue(max_queue_size=1000)
        self._instances['forwarding_queue'] = forwarding_queue

        # Message Processor (depends on analyzers and managers)
        message_processor = MessageProcessor(ca_analyzer, forwarding_manager, enhanced_stats_tracker)
        self._instances['message_processor'] = message_processor
        
        logger.info("✅ Manager components initialized")
    
    def get(self, component_name: str) -> Any:
        """Get component instance by name."""
        if not self._initialized and component_name not in self._instances:
            raise RuntimeError(f"Dependency container not initialized or component '{component_name}' not found")
        
        return self._instances.get(component_name)
    
    def get_all_components(self) -> Dict[str, Any]:
        """Get all component instances."""
        return self._instances.copy()
    
    def is_initialized(self) -> bool:
        """Check if container is initialized."""
        return self._initialized
    
    async def cleanup(self):
        """Cleanup all components."""
        try:
            logger.info("🧹 Cleaning up dependencies...")
            
            # Cleanup in reverse order
            components_to_cleanup = [
                'message_processor',
                'forwarding_queue',
                'forwarding_manager',
                'ca_analyzer',
                'trending_analyzer',
                'ca_detector',
                'telegram_client',
                'db_manager'
            ]
            
            for component_name in components_to_cleanup:
                component = self._instances.get(component_name)
                if component and hasattr(component, 'cleanup'):
                    try:
                        await component.cleanup()
                        logger.info(f"✅ {component_name} cleaned up")
                    except Exception as e:
                        logger.error(f"❌ Error cleaning up {component_name}: {e}")
            
            # Close database connection
            db_manager = self._instances.get('db_manager')
            if db_manager and hasattr(db_manager, 'close'):
                await db_manager.close()
                logger.info("✅ Database connection closed")
            
            self._instances.clear()
            self._initialized = False
            logger.info("✅ All dependencies cleaned up")
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
    
    def get_component_stats(self) -> Dict[str, Any]:
        """Get statistics from all components that support it."""
        stats = {}
        
        for name, component in self._instances.items():
            if hasattr(component, 'get_stats'):
                try:
                    stats[name] = component.get_stats()
                except Exception as e:
                    stats[name] = {'error': str(e)}
        
        return stats
    
    async def health_check(self) -> Dict[str, bool]:
        """Perform health check on all components."""
        health_status = {}
        
        for name, component in self._instances.items():
            try:
                if hasattr(component, 'health_check'):
                    health_status[name] = await component.health_check()
                else:
                    # Basic health check - component exists and is not None
                    health_status[name] = component is not None
            except Exception as e:
                logger.error(f"Health check failed for {name}: {e}")
                health_status[name] = False
        
        return health_status

# Global dependency container instance
dependency_container = DependencyContainer()
