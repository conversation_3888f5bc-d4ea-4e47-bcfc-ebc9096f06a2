# CLA v2 Telegram Bot - Git Ignore File

# Environment and Configuration
.env
.env.local
.env.production
config.local.py
secrets.py

# Telegram Session Files
*.session
*.session-journal

# Database Files
*.db
*.db-shm
*.db-wal
data/*.db*
data/cla_bot.db*

# Log Files
logs/*.log
logs/*.log.*
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary Files
*.tmp
*.temp
temp/
tmp/

# Test Files (keep test scripts but exclude test data)
test_data/
*.test.json

# Data Files (exclude actual data but keep structure)
data/*.json
data/slow_cook_patterns.json
data/analytics_data.json

# Backup Files
*.bak
*.backup
backup/

# Runtime Files
*.pid
*.lock

# Documentation Build
docs/_build/

# Coverage Reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/
