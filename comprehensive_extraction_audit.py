#!/usr/bin/env python3
"""
Comprehensive CA Extraction Audit for CLA v2 Telegram Bot
Tests all extraction methods, validation consistency, and edge cases
"""

import sys
import time
import asyncio
sys.path.append('.')

def test_all_extraction_methods():
    """Test all CA extraction methods across different message formats."""
    print("🔍 COMPREHENSIVE CA EXTRACTION AUDIT")
    print("=" * 80)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Comprehensive test cases covering all supported formats
        test_cases = {
            "BUGSIE Activity Tracker (Complete)": {
                "message": """🔥 **ACTIVITY DETECTED** 🔥

├ **$FARM**
├ `7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk`
└| ⏳ 1h | 👁️ 490

📊 Token details
├ PRICE:    $0
├ MC:       $56.2K /2.4X from VIP/

📈 Charts + Exchanges
├ Axiom (https://axiom.trade/t/7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk/@bugsie)""",
                "expected": ["7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk"],
                "format": "Activity Tracker"
            },
            
            "BUGSIE Activity Tracker (Truncated)": {
                "message": """🔥 **ACTIVITY DETECTED** 🔥

├ **$FARM**
├ `7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TN...`

📈 Charts + Exchanges
├ Axiom (https://axiom.trade/t/7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk/@bugsie)""",
                "expected": ["7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk"],
                "format": "Activity Tracker (Truncated)"
            },
            
            "GMGN Featured Signals": {
                "message": """🔥 **GMGN FEATURED** 🔥

**$BOOP** | MC: $2.1M
CA: `GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk`

🔗 https://gmgn.ai/sol/token/GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk""",
                "expected": ["GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"],
                "format": "GMGN"
            },
            
            "FREE WHALE SIGNALS (URL)": {
                "message": "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)",
                "expected": ["5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"],
                "format": "URL Format"
            },
            
            "Standalone CA (44 chars)": {
                "message": "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
                "expected": ["GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"],
                "format": "Standalone 44-char"
            },
            
            "Standalone CA (43 chars)": {
                "message": "So11111111111111111111111111111111111111112",
                "expected": ["So11111111111111111111111111111111111111112"],
                "format": "Standalone 43-char"
            },
            
            "Mixed Format": {
                "message": """Token $BOOP is pumping!
CA: GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
Check: https://dexscreener.com/solana/GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk""",
                "expected": ["GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"],
                "format": "Mixed"
            },
            
            "Multiple CAs": {
                "message": """Multiple tokens:
1. GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
2. 7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk""",
                "expected": ["GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk", "7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk"],
                "format": "Multiple CAs"
            },
            
            "Edge Case - Pump.fun URL": {
                "message": "Check this: https://pump.fun/6jiL8tdTT28hkkGt8CMJT1tzdt6RJE9rWZmwdtjXbonk",
                "expected": ["6jiL8tdTT28hkkGt8CMJT1tzdt6RJE9rWZmwdtjXbonk"],
                "format": "Pump.fun URL"
            },
            
            "Edge Case - Solscan URL": {
                "message": "View on Solscan: https://solscan.io/token/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
                "expected": ["EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"],
                "format": "Solscan URL"
            }
        }
        
        print("Testing all CA extraction methods:")
        print()
        
        results = {}
        total_time = 0
        
        for test_name, test_data in test_cases.items():
            message = test_data["message"]
            expected_cas = test_data["expected"]
            format_type = test_data["format"]
            
            print(f"🧪 TEST: {test_name}")
            print(f"Format: {format_type}")
            print(f"Expected: {expected_cas}")
            print("-" * 60)
            
            # Test full extraction pipeline
            start_time = time.perf_counter()
            extracted_cas = parser.extract_contract_addresses(message)
            extraction_time = (time.perf_counter() - start_time) * 1000
            total_time += extraction_time
            
            print(f"Extracted: {extracted_cas} ({extraction_time:.2f}ms)")
            
            # Check success
            success = all(ca in extracted_cas for ca in expected_cas)
            
            # Test individual methods for debugging
            print("Individual method results:")
            
            # URL extraction
            url_cas = parser._extract_cas_from_urls(message)
            print(f"  URL extraction: {url_cas}")
            
            # Standalone extraction
            cleaned_text = parser._clean_message_text_no_url_extraction(message)
            standalone_cas = parser._extract_standalone_cas(cleaned_text)
            print(f"  Standalone extraction: {standalone_cas}")
            
            # Activity Tracker detection
            is_activity_format = parser._is_activity_tracker_format(message)
            if is_activity_format:
                activity_cas = parser._extract_activity_tracker_cas(message)
                print(f"  Activity Tracker: {activity_cas}")
            
            # Validation check
            for ca in extracted_cas:
                is_valid = parser._validate_solana_ca(ca)
                print(f"  Validation {ca}: {'✅' if is_valid else '❌'}")
            
            results[test_name] = {
                "success": success,
                "extracted": extracted_cas,
                "expected": expected_cas,
                "time": extraction_time,
                "format": format_type
            }
            
            print(f"Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            if not success:
                missing = [ca for ca in expected_cas if ca not in extracted_cas]
                extra = [ca for ca in extracted_cas if ca not in expected_cas]
                if missing:
                    print(f"  Missing: {missing}")
                if extra:
                    print(f"  Extra: {extra}")
            print()
        
        # Summary
        print("🎯 EXTRACTION AUDIT SUMMARY")
        print("=" * 80)
        
        total_tests = len(results)
        passed_tests = sum(1 for r in results.values() if r["success"])
        
        print(f"Total tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success rate: {(passed_tests/total_tests)*100:.1f}%")
        print()
        
        # Performance analysis
        avg_time = total_time / len(results)
        max_time = max(r["time"] for r in results.values())
        min_time = min(r["time"] for r in results.values())
        
        print(f"Performance metrics:")
        print(f"  Total extraction time: {total_time:.2f}ms")
        print(f"  Average extraction time: {avg_time:.2f}ms")
        print(f"  Maximum extraction time: {max_time:.2f}ms")
        print(f"  Minimum extraction time: {min_time:.2f}ms")
        print()
        
        # Format-specific analysis
        format_results = {}
        for test_name, result in results.items():
            format_type = result["format"]
            if format_type not in format_results:
                format_results[format_type] = {"passed": 0, "total": 0, "times": []}
            format_results[format_type]["total"] += 1
            format_results[format_type]["times"].append(result["time"])
            if result["success"]:
                format_results[format_type]["passed"] += 1
        
        print("Format-specific results:")
        for format_type, stats in format_results.items():
            success_rate = (stats["passed"] / stats["total"]) * 100
            avg_time = sum(stats["times"]) / len(stats["times"])
            print(f"  {format_type}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%) - {avg_time:.2f}ms avg")
        
        # Identify issues
        failed_tests = [name for name, result in results.items() if not result["success"]]
        if failed_tests:
            print()
            print("⚠️ FAILED TESTS REQUIRING ATTENTION:")
            for test_name in failed_tests:
                result = results[test_name]
                print(f"  - {test_name}")
                print(f"    Expected: {result['expected']}")
                print(f"    Got: {result['extracted']}")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ Audit failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_consistency():
    """Test CA validation consistency and edge cases."""
    print("\n🔍 CA VALIDATION CONSISTENCY AUDIT")
    print("=" * 80)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test CAs with different characteristics
        test_cas = [
            # Valid standard CAs
            ("7727oP6FK5Rsq1vNRKju73fScMLSYBLd1TB2TNXbonk", True, "Standard 44-char CA"),
            ("GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk", True, "Standard 44-char CA"),
            ("So11111111111111111111111111111111111111112", True, "SOL token (43 chars)"),
            ("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", True, "USDC token (44 chars)"),
            
            # Edge cases
            ("5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups", True, "DeFi protocol CA"),
            
            # Invalid CAs
            ("invalid_ca_too_short", False, "Too short"),
            ("invalid_ca_too_long_definitely_not_valid_solana_address", False, "Too long"),
            ("1111111111111111111111111111111111111111111", False, "All 1s (43 chars)"),
            ("0000000000000000000000000000000000000000000", False, "Contains 0s (invalid base58)"),
            ("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", False, "All As (43 chars)"),
            ("", False, "Empty string"),
            ("GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonkX", False, "45 chars (too long)"),
            ("GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bon", False, "42 chars (too short)"),
        ]
        
        print("Testing validation consistency:")
        print()
        
        validation_issues = []
        
        for ca, expected_valid, description in test_cas:
            print(f"Testing: {description}")
            print(f"  CA: {ca} ({len(ca)} chars)")
            
            # Test parser validation
            parser_valid = parser._validate_solana_ca(ca)
            print(f"  Parser validation: {'✅ VALID' if parser_valid else '❌ INVALID'}")
            
            # Check if result matches expectation
            if parser_valid != expected_valid:
                validation_issues.append({
                    "ca": ca,
                    "description": description,
                    "expected": expected_valid,
                    "actual": parser_valid
                })
                print(f"  ⚠️ VALIDATION MISMATCH! Expected: {expected_valid}, Got: {parser_valid}")
            else:
                print(f"  ✅ Validation correct")
            
            print()
        
        if validation_issues:
            print("❌ VALIDATION ISSUES FOUND:")
            for issue in validation_issues:
                print(f"  {issue['description']}: {issue['ca']}")
                print(f"    Expected: {issue['expected']}, Got: {issue['actual']}")
            return False
        else:
            print("✅ All validation tests passed")
            return True
            
    except Exception as e:
        print(f"❌ Validation audit failed: {e}")
        return False

def main():
    """Run comprehensive CA extraction audit."""
    print("🚀 COMPREHENSIVE CA EXTRACTION AUDIT - CLA V2 TELEGRAM BOT")
    print("=" * 80)
    print("Testing all extraction methods, validation consistency, and edge cases")
    print("=" * 80)
    
    # Run audits
    extraction_success = test_all_extraction_methods()
    validation_success = test_validation_consistency()
    
    print("\n🎯 COMPREHENSIVE AUDIT RESULTS")
    print("=" * 80)
    print(f"CA Extraction Methods: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    print(f"Validation Consistency: {'✅ PASS' if validation_success else '❌ FAIL'}")
    
    if extraction_success and validation_success:
        print("\n🎉 COMPREHENSIVE AUDIT PASSED!")
        print("✅ All CA extraction methods working correctly")
        print("✅ Validation is consistent and robust")
        print("✅ All message formats supported")
        print("✅ Edge cases handled properly")
        print("✅ System ready for production use")
    else:
        print("\n⚠️ AUDIT ISSUES IDENTIFIED")
        print("Some components require attention before production use")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
