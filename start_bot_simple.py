#!/usr/bin/env python3
"""
Simple Bot Starter - Bypasses main.py startup issues
"""

import asyncio
import signal
import sys
from pathlib import Path

from loguru import logger
from config import config
from src.bot import CLABot
from src.database import DatabaseManager
from src.logger_setup import setup_logging

class SimpleBotRunner:
    """Simple bot runner that works around startup issues."""
    
    def __init__(self):
        self.bot = None
        self.db_manager = None
        self.running = False
    
    async def initialize(self):
        """Initialize bot components."""
        try:
            # Setup logging
            setup_logging()
            logger.info("🚀 Starting CLA v2.0 Bot (Simple Startup)...")
            
            # Initialize database
            self.db_manager = DatabaseManager()
            await self.db_manager.initialize()
            logger.info("Database initialized successfully")
            
            # Initialize bot
            self.bot = CLABot(self.db_manager)
            await self.bot.initialize()
            logger.info("Bot initialized successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize bot: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def run_forever(self):
        """Run the bot forever."""
        if not await self.initialize():
            logger.error("Bot initialization failed. Exiting.")
            return
        
        # Setup signal handlers
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}. Shutting down...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            self.running = True
            logger.info("🎉 Bot started successfully! Monitoring for signals...")
            logger.info("📊 All integrations active and ready")
            logger.info("🔄 Press Ctrl+C to stop the bot")
            
            # Keep running until signal received
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Bot encountered an error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up resources...")
        
        if self.bot:
            try:
                await self.bot.stop()
            except:
                pass
        
        if self.db_manager:
            try:
                await self.db_manager.close()
            except:
                pass
        
        logger.info("Cleanup completed")

async def main():
    """Main entry point."""
    runner = SimpleBotRunner()
    await runner.run_forever()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
