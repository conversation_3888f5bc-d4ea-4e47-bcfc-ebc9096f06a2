@echo off
echo ========================================
echo    CLA Bot Enhanced Startup Script
echo ========================================
echo.

echo [INFO] Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo [ERROR] Python not found! Please install Python 3.8+
    pause
    exit /b 1
)

echo [INFO] Checking current directory...
echo Current directory: %cd%
echo.

echo [INFO] Checking for required files...
if not exist "main.py" (
    echo [ERROR] main.py not found! Please run from the correct directory.
    pause
    exit /b 1
)

if not exist "src\bot.py" (
    echo [ERROR] src\bot.py not found! Please run from the correct directory.
    pause
    exit /b 1
)

echo [INFO] Starting CLA Bot with enhanced fixes...
echo [INFO] Press Ctrl+C to stop the bot
echo.

python start_bot.py

echo.
echo [INFO] <PERSON><PERSON> has stopped.
pause
