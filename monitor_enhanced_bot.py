"""Real-time monitoring dashboard for enhanced CLA v2.0 bot."""

import time
import json
import os
import sys
from datetime import datetime, timedelta
from collections import defaultdict

# Add src to path
sys.path.insert(0, 'src')

def display_dashboard_header():
    """Display dashboard header."""
    print("\033[2J\033[H")  # Clear screen and move cursor to top
    print("🚀 CLA v2.0 Bot - Real-Time Monitoring Dashboard")
    print("=" * 70)
    print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | Press Ctrl+C to exit")
    print("=" * 70)

def load_current_stats():
    """Load current statistics from file."""
    try:
        if os.path.exists('data/hourly_stats.json'):
            with open('data/hourly_stats.json', 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading stats: {e}")
    return None

def display_system_overview(stats):
    """Display system overview."""
    if not stats or 'system' not in stats:
        print("📊 System Overview: No data available")
        return
    
    system = stats['system']
    uptime_hours = system.get('uptime_hours', 0)
    
    print("📊 System Overview:")
    print(f"   ⏱️ Uptime: {uptime_hours:.1f} hours")
    print(f"   📨 Messages: {system.get('total_messages', 0)}")
    print(f"   🔍 CAs Detected: {system.get('total_cas_detected', 0)}")
    print(f"   📤 CAs Forwarded: {system.get('total_cas_forwarded', 0)}")
    print(f"   🔄 Duplicates Filtered: {system.get('total_duplicates_filtered', 0)}")
    print(f"   📈 Trending Qualified: {system.get('trending_qualified', 0)}")
    print(f"   🛡️ Pump Schemes Blocked: {system.get('pump_schemes_blocked', 0)}")
    print(f"   ❌ Errors: {system.get('errors_count', 0)} | ⚠️ Warnings: {system.get('warnings_count', 0)}")

def display_group_statistics(stats):
    """Display per-group statistics."""
    if not stats or 'groups' not in stats:
        print("\n📈 Group Statistics: No data available")
        return
    
    print("\n📈 Group Statistics:")
    
    # Sort groups by activity (high-volume first, then by message count)
    groups = stats['groups']
    sorted_groups = sorted(
        groups.items(),
        key=lambda x: (not x[1].get('is_high_volume', False), -x[1].get('messages_received', 0))
    )
    
    for group_id, group_stats in sorted_groups:
        group_type = "🔥 HIGH" if group_stats.get('is_high_volume', False) else "⚡ LOW"
        group_name = group_stats.get('group_name', 'Unknown')
        
        # Calculate activity status
        last_activity = group_stats.get('last_activity')
        if last_activity:
            try:
                last_time = datetime.fromisoformat(last_activity.replace('Z', '+00:00'))
                time_since = (datetime.now() - last_time.replace(tzinfo=None)).total_seconds() / 60
                activity_status = f"{time_since:.0f}min ago" if time_since < 60 else f"{time_since/60:.1f}h ago"
            except:
                activity_status = "Unknown"
        else:
            activity_status = "Never"
        
        print(f"   {group_type} {group_name}:")
        print(f"      📨 Messages: {group_stats.get('messages_received', 0)} | 🔍 CAs: {group_stats.get('cas_detected', 0)}")
        print(f"      📤 Forwarded: {group_stats.get('cas_forwarded', 0)} | 🔄 Filtered: {group_stats.get('cas_filtered_duplicate', 0) + group_stats.get('cas_filtered_trending', 0)}")
        
        if group_stats.get('is_high_volume', False):
            print(f"      📈 Trending: {group_stats.get('trending_qualified', 0)} | 🔇 Noise: {group_stats.get('noise_filtered', 0)}")
        
        print(f"      🕐 Last Activity: {activity_status}")

def display_destination_statistics(stats):
    """Display forwarding destination statistics."""
    if not stats or 'destinations' not in stats:
        print("\n📤 Destination Statistics: No data available")
        return
    
    print("\n📤 Destination Statistics:")
    
    destinations = stats['destinations']
    for dest_name, dest_stats in destinations.items():
        total_sent = dest_stats.get('total_cas_sent', 0)
        success_rate = dest_stats.get('success_rate', 100.0)

        last_sent = dest_stats.get('last_sent')
        if last_sent:
            try:
                last_time = datetime.fromisoformat(last_sent.replace('Z', '+00:00'))
                time_since = (datetime.now() - last_time.replace(tzinfo=None)).total_seconds() / 60
                last_sent_str = f"{time_since:.0f}min ago" if time_since < 60 else f"{time_since/60:.1f}h ago"
            except:
                last_sent_str = "Unknown"
        else:
            last_sent_str = "Never"

        print(f"   {dest_name}: 📤 {total_sent} CAs | ✅ {success_rate:.1f}% | 🕐 {last_sent_str}")

def display_performance_statistics(stats):
    """Display performance statistics."""
    if not stats or 'performance' not in stats:
        print("\n⚡ Performance Statistics: No data available")
        return
    
    performance = stats['performance']
    
    print("\n⚡ Performance Statistics:")
    print(f"   📨 Message Handling: {performance.get('message_handling_avg', 0):.1f}ms avg")
    print(f"   🔍 CA Detection: {performance.get('ca_detection_avg', 0):.1f}ms avg")
    print(f"   📊 Trending Analysis: {performance.get('trending_analysis_avg', 0):.1f}ms avg")
    print(f"   📤 Forwarding: {performance.get('forwarding_avg', 0):.1f}ms avg")
    print(f"   🔄 Total Pipeline: {performance.get('pipeline_total_avg', 0):.1f}ms avg")
    print(f"   ⚠️ Slow Operations: {performance.get('slow_operations_count', 0)}")

def display_system_health(stats):
    """Display system health indicators."""
    if not stats:
        print("\n🏥 System Health: No data available")
        return
    
    print("\n🏥 System Health:")
    
    # Calculate health score
    system = stats.get('system', {})
    performance = stats.get('performance', {})
    
    errors = system.get('errors_count', 0)
    warnings = system.get('warnings_count', 0)
    slow_ops = performance.get('slow_operations_count', 0)
    
    if errors == 0 and slow_ops < 10:
        health_status = "🟢 EXCELLENT"
        health_desc = "System operating optimally"
    elif errors < 5 and slow_ops < 25:
        health_status = "🟡 GOOD"
        health_desc = "Minor performance issues detected"
    else:
        health_status = "🔴 NEEDS ATTENTION"
        health_desc = "Significant issues detected"
    
    print(f"   Status: {health_status}")
    print(f"   Description: {health_desc}")
    
    # Performance health
    avg_pipeline = performance.get('pipeline_total_avg', 0)
    if avg_pipeline < 500:
        perf_status = "🟢 FAST"
    elif avg_pipeline < 1000:
        perf_status = "🟡 MODERATE"
    else:
        perf_status = "🔴 SLOW"
    
    print(f"   Performance: {perf_status} ({avg_pipeline:.0f}ms avg pipeline)")

def display_anti_pump_status():
    """Display anti-pump protection status."""
    print("\n🛡️ Anti-Pump Protection Status:")
    print("   🔥 High-Volume Groups:")
    print("      • Threshold: 6 mentions in 8 minutes")
    print("      • Min spread: 120 seconds")
    print("      • Max velocity: 3.0 mentions/minute")
    print("      • Min organic growth: 3 minutes")
    print("   ⚡ Low-Volume Groups:")
    print("      • Direct forwarding enabled")
    print("      • Global duplicate prevention active")
    print("   🔄 Global Protection:")
    print("      • 7-day duplicate cache")
    print("      • Race condition protection")

def monitor_real_time():
    """Monitor bot in real-time."""
    print("🚀 Starting Real-Time Monitoring...")
    print("Press Ctrl+C to exit")
    
    try:
        while True:
            # Clear screen and display dashboard
            display_dashboard_header()
            
            # Load current statistics
            stats = load_current_stats()
            
            # Display all sections
            display_system_overview(stats)
            display_group_statistics(stats)
            display_destination_statistics(stats)
            display_performance_statistics(stats)
            display_system_health(stats)
            display_anti_pump_status()
            
            print("\n" + "=" * 70)
            print("🔄 Refreshing in 30 seconds... (Ctrl+C to exit)")
            
            # Wait 30 seconds before refresh
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped by user")
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")

def show_current_status():
    """Show current status once."""
    display_dashboard_header()
    
    stats = load_current_stats()
    
    if not stats:
        print("❌ No statistics data available")
        print("Make sure the enhanced bot is running and has generated stats")
        return
    
    display_system_overview(stats)
    display_group_statistics(stats)
    display_destination_statistics(stats)
    display_performance_statistics(stats)
    display_system_health(stats)
    display_anti_pump_status()

def main():
    """Main monitoring function."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'status':
        show_current_status()
    else:
        print("🚀 Enhanced CLA v2.0 Bot Monitor")
        print("\nOptions:")
        print("  python monitor_enhanced_bot.py        - Real-time monitoring")
        print("  python monitor_enhanced_bot.py status - Show current status")
        print()
        
        choice = input("Start real-time monitoring? (y/n): ").lower()
        if choice in ['y', 'yes']:
            monitor_real_time()
        else:
            show_current_status()

if __name__ == "__main__":
    main()
