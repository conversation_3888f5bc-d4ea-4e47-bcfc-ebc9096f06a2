"""Test race condition fixes for CLA v2.0 Bot."""

import asyncio
import sys
import tempfile
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_ca_processing_locks():
    """Test async locks for CA processing."""
    print("🧪 Testing CA Processing Locks...")
    
    try:
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        db_manager = DatabaseManager()
        db_manager.db_path = temp_db.name
        await db_manager.initialize()
        
        ca_detector = CADetector(db_manager)
        
        # Test CA lock creation
        test_ca = "8aWzZb5kZfF4CxnESSyVvs4k9XwH3SbbJovYnDLZL12e"
        lock1 = await ca_detector._get_ca_lock(test_ca)
        lock2 = await ca_detector._get_ca_lock(test_ca)
        
        # Should be the same lock object
        if lock1 is lock2:
            print("✅ CA locks working correctly - same lock for same CA")
        else:
            print("❌ CA locks not working - different locks for same CA")
            return False
        
        # Test lock cleanup
        ca_detector.lock_cleanup_threshold = 2
        await ca_detector._get_ca_lock("test_ca_1")
        await ca_detector._get_ca_lock("test_ca_2")
        await ca_detector._get_ca_lock("test_ca_3")  # Should trigger cleanup
        
        print(f"✅ Lock cleanup test completed - {len(ca_detector.ca_processing_locks)} locks remaining")
        
        await db_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ CA processing locks test failed: {e}")
        return False

async def test_message_deduplication():
    """Test message-level deduplication."""
    print("\n🧪 Testing Message Deduplication...")
    
    try:
        from src.bot import CLABot
        from src.database import DatabaseManager
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        db_manager = DatabaseManager()
        db_manager.db_path = temp_db.name
        
        bot = CLABot(db_manager)
        
        # Test duplicate message detection
        message_id = 12345
        
        # First check - should not be duplicate
        is_dup1 = await bot._is_duplicate_message(message_id)
        if not is_dup1:
            print("✅ First message check - not duplicate")
        else:
            print("❌ First message incorrectly marked as duplicate")
            return False
        
        # Second check - should be duplicate
        is_dup2 = await bot._is_duplicate_message(message_id)
        if is_dup2:
            print("✅ Second message check - correctly identified as duplicate")
        else:
            print("❌ Second message not identified as duplicate")
            return False
        
        # Check statistics
        if bot.stats['duplicate_messages_filtered'] == 0:
            print("✅ Duplicate message statistics initialized correctly")
        else:
            print("❌ Duplicate message statistics not initialized correctly")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Message deduplication test failed: {e}")
        return False

async def test_trending_logic_fixes():
    """Test trending logic fixes."""
    print("\n🧪 Testing Trending Logic Fixes...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        test_ca = "8aWzZb5kZfF4CxnESSyVvs4k9XwH3SbbJovYnDLZL12e"
        gmgn_group_id = -1002202241417
        
        # First mention
        result1 = await analyzer.analyze_ca_mention(test_ca, gmgn_group_id, "GMGN", 1001)
        print(f"   First mention - Trending: {result1.is_trending}, Count: {result1.mention_count}")
        
        # Second mention
        result2 = await analyzer.analyze_ca_mention(test_ca, gmgn_group_id, "GMGN", 1002)
        print(f"   Second mention - Trending: {result2.is_trending}, Count: {result2.mention_count}")
        
        # Third mention (should trigger trending)
        result3 = await analyzer.analyze_ca_mention(test_ca, gmgn_group_id, "GMGN", 1003)
        print(f"   Third mention - Trending: {result3.is_trending}, Count: {result3.mention_count}")
        
        if not result3.is_trending:
            print("❌ Trending not triggered after 3 mentions")
            return False
        
        # Fourth mention (should return existing trending result without re-processing)
        result4 = await analyzer.analyze_ca_mention(test_ca, gmgn_group_id, "GMGN", 1004)
        print(f"   Fourth mention - Trending: {result4.is_trending}, Count: {result4.mention_count}")
        
        if not result4.is_trending:
            print("❌ Already trending CA not returning trending result")
            return False
        
        # Check trending timestamps
        if test_ca in analyzer.trending_timestamps:
            print("✅ Trending timestamp tracking working")
        else:
            print("❌ Trending timestamp not tracked")
            return False
        
        print("✅ Trending logic fixes working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Trending logic test failed: {e}")
        return False

async def test_queue_duplicate_prevention():
    """Test queue-level duplicate prevention."""
    print("\n🧪 Testing Queue Duplicate Prevention...")
    
    try:
        from src.bonkbot_integration import BonkBotIntegration
        from src.telegram_client import TelegramClientManager
        
        # Mock telegram client
        class MockTelegramClient:
            async def get_entity(self, username):
                return None
        
        client = MockTelegramClient()
        bonkbot = BonkBotIntegration(client)
        
        test_ca = "8aWzZb5kZfF4CxnESSyVvs4k9XwH3SbbJovYnDLZL12e"
        
        # Check initial state
        if len(bonkbot.queued_cas) == 0:
            print("✅ Queue initially empty")
        else:
            print("❌ Queue not initially empty")
            return False
        
        # Test recent send check
        is_recent = await bonkbot._is_ca_recently_sent(test_ca)
        if not is_recent:
            print("✅ CA not recently sent initially")
        else:
            print("❌ CA incorrectly marked as recently sent")
            return False
        
        # Add to recent sent
        bonkbot.recent_sent_cas[test_ca] = datetime.now()
        
        # Check again
        is_recent2 = await bonkbot._is_ca_recently_sent(test_ca)
        if is_recent2:
            print("✅ CA correctly identified as recently sent")
        else:
            print("❌ CA not identified as recently sent")
            return False
        
        print("✅ Queue duplicate prevention working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Queue duplicate prevention test failed: {e}")
        return False

async def test_database_atomic_operations():
    """Test database atomic operations."""
    print("\n🧪 Testing Database Atomic Operations...")
    
    try:
        from src.database import DatabaseManager
        
        # Create temporary database
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        db_manager = DatabaseManager()
        db_manager.db_path = temp_db.name
        await db_manager.initialize()
        
        test_ca = "8aWzZb5kZfF4CxnESSyVvs4k9XwH3SbbJovYnDLZL12e"
        
        # First insertion - should succeed
        result1 = await db_manager.add_contract_address(test_ca, 1001, -1002202241417)
        if result1:
            print("✅ First CA insertion successful")
        else:
            print("❌ First CA insertion failed")
            return False
        
        # Second insertion - should fail (duplicate)
        result2 = await db_manager.add_contract_address(test_ca, 1002, -1002202241417)
        if not result2:
            print("✅ Duplicate CA insertion correctly prevented")
        else:
            print("❌ Duplicate CA insertion not prevented")
            return False
        
        await db_manager.close()
        print("✅ Database atomic operations working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Database atomic operations test failed: {e}")
        return False

async def main():
    """Run all race condition fix tests."""
    print("🚀 CLA v2.0 Bot - Race Condition Fixes Testing\n")
    
    try:
        # Test 1: CA Processing Locks
        if not await test_ca_processing_locks():
            return False
        
        # Test 2: Message Deduplication
        if not await test_message_deduplication():
            return False
        
        # Test 3: Trending Logic Fixes
        if not await test_trending_logic_fixes():
            return False
        
        # Test 4: Queue Duplicate Prevention
        if not await test_queue_duplicate_prevention():
            return False
        
        # Test 5: Database Atomic Operations
        if not await test_database_atomic_operations():
            return False
        
        print("\n🎉 All race condition fix tests passed!")
        print("\n📋 Fixes Implemented:")
        print("✅ Async locks for CA processing")
        print("✅ Message-level deduplication")
        print("✅ Trending logic re-processing prevention")
        print("✅ Queue-level duplicate prevention")
        print("✅ Database atomic check-and-insert operations")
        
        print("\n🎯 Race Condition Issues Resolved:")
        print("✅ Simultaneous CA processing from multiple GMGN messages")
        print("✅ Global duplicate prevention race conditions")
        print("✅ Trending analysis inconsistencies")
        print("✅ Multiple forwarding to same destinations")
        print("✅ Database duplicate insertion race conditions")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Race condition fix test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
