#!/usr/bin/env python3
"""
Check WINNERS configuration status
"""
import os
import sys

def check_winners_config():
    print("=" * 60)
    print("WINNERS Configuration Status Check")
    print("=" * 60)
    
    # Check environment variable directly
    env_value = os.getenv('TRENDING_EXCLUDE_DESTINATIONS', '')
    print(f"Environment Variable: TRENDING_EXCLUDE_DESTINATIONS = '{env_value}'")
    
    # Check .env file
    if os.path.exists('.env'):
        print("\n.env file contents (TRENDING_EXCLUDE_DESTINATIONS lines):")
        with open('.env', 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if 'TRENDING_EXCLUDE_DESTINATIONS' in line:
                    status = "COMMENTED" if line.strip().startswith('#') else "ACTIVE"
                    print(f"  Line {i}: {line.strip()} [{status}]")
    else:
        print("\n.env file not found")
    
    # Check config object
    try:
        from config import config
        excluded = getattr(config.trending, 'exclude_destinations', [])
        print(f"\nConfig object exclude_destinations: {excluded}")
        
        # Check if WINNERS is excluded
        winners_excluded = 'WINNERS' in [dest.upper() for dest in excluded]
        print(f"WINNERS excluded in config: {winners_excluded}")
        
    except Exception as e:
        print(f"\nError loading config: {e}")
    
    # Check trending analyzer
    try:
        from src.trending_analyzer import TrendingAnalyzer
        analyzer = TrendingAnalyzer()
        
        # Test WINNERS forwarding decision
        should_forward = analyzer.should_forward_to_destination("TestCA", "WINNERS", -1002380594298)
        print(f"\nTrending Analyzer - Should forward to WINNERS: {should_forward}")
        
    except Exception as e:
        print(f"\nError testing trending analyzer: {e}")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    check_winners_config()
