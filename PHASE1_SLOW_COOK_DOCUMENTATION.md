# Phase 1 Slow Cook Tracking Implementation Documentation

## 🎯 Overview

Phase 1 implements comprehensive slow cook pattern analysis for the CLA v2 Telegram bot. This system tracks multi-mention patterns across groups to identify tokens that gain momentum gradually rather than through pump-and-dump schemes.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                    PHASE 1 SLOW COOK SYSTEM                    │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ SlowCookAnalytics│  │ TrendingAnalyzer│  │ CARescueTracker │ │
│  │                 │  │                 │  │                 │ │
│  │ • Pattern Store │  │ • Enhanced      │  │ • Multi-mention │ │
│  │ • Cross-group   │  │   Trending      │  │   Rescue        │ │
│  │ • Time Windows  │  │ • Anti-pump     │  │ • Slow Cook     │ │
│  │ • Analytics     │  │ • Velocity      │  │   Eligible      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           └─────────────────────┼─────────────────────┘        │
│                                 │                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                MESSAGE PROCESSING PIPELINE              │   │
│  │                                                         │   │
│  │  Message → CA Detection → Trending Analysis →          │   │
│  │  Slow Cook Pattern → Rescue Tracking → Forwarding     │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 📁 File Structure and Dependencies

### Core Files
```
src/
├── slow_cook_analytics.py      # Main slow cook pattern analysis
├── trending_analyzer.py        # Enhanced trending with slow cook integration
├── ca_rescue_tracker.py        # Multi-mention rescue tracking
├── message_parser.py           # Enhanced CA extraction (URLs + standalone)
├── group_manager.py            # Centralized group configuration
└── bounded_cache.py            # Memory-efficient caching

data/
├── slow_cook_patterns.json     # Pattern storage (auto-created)
├── analytics_data.json         # Analytics data (auto-created)
└── cla_bot.db                  # Main database

config/
├── main.py                     # Phase 1 initialization
└── config.py                   # Configuration with slow cook settings
```

### Import Dependencies
```python
# Core Phase 1 imports
from src.slow_cook_analytics import SlowCookAnalytics
from src.trending_analyzer import TrendingAnalyzer  # Enhanced version
from src.ca_rescue_tracker import CARescueTracker   # Enhanced version

# Supporting imports
from src.group_manager import GroupManager
from src.bounded_cache import BoundedCache, BoundedSet
from src.message_parser import MessageParser        # Enhanced version
```

## 🔧 Component Details

### 1. SlowCookAnalytics (`src/slow_cook_analytics.py`)

**Purpose**: Core pattern analysis and data collection for slow cook detection.

**Key Features**:
- Cross-group mention tracking
- Time-based pattern analysis
- Velocity calculations
- Pattern persistence

**Key Methods**:
```python
async def record_mention(ca: str, group_id: int, timestamp: float)
async def analyze_pattern(ca: str) -> SlowCookPattern
async def is_slow_cook_candidate(ca: str) -> bool
async def get_pattern_summary() -> Dict[str, Any]
```

**Configuration**:
```python
SLOW_COOK_CONFIG = {
    'min_mentions': 3,           # Minimum mentions to consider
    'time_window': 3600,         # 1 hour analysis window
    'cross_group_bonus': 1.5,    # Bonus for cross-group mentions
    'velocity_threshold': 0.5,   # Max mentions per minute
    'pattern_memory': 86400      # 24 hour pattern retention
}
```

### 2. Enhanced TrendingAnalyzer (`src/trending_analyzer.py`)

**Purpose**: Enhanced trending analysis with slow cook pattern integration.

**Key Enhancements**:
- Slow cook pattern detection
- Anti-pump protection with velocity analysis
- Selective protection for high-volume groups
- Cross-group trending analysis

**Integration Points**:
```python
# In analyze_ca method
slow_cook_pattern = await self.slow_cook_analytics.analyze_pattern(ca)
if slow_cook_pattern.is_candidate:
    # Apply slow cook bonus to trending score
    trending_score *= slow_cook_pattern.confidence
```

### 3. Enhanced CARescueTracker (`src/ca_rescue_tracker.py`)

**Purpose**: Multi-mention rescue tracking with slow cook eligibility.

**Key Enhancements**:
- Multi-mention rescue eligibility
- Slow cook candidate tracking
- Cross-group rescue coordination
- Enhanced logging for Phase 1 analysis

**New Rescue Categories**:
```python
RESCUE_CATEGORIES = {
    'MULTI_MENTION': 'Multiple mentions across time windows',
    'SLOW_COOK': 'Slow cook pattern detected',
    'CROSS_GROUP': 'Mentions across multiple groups',
    'VELOCITY_FILTERED': 'Filtered due to high velocity'
}
```

## 🗄️ Database Schema

### New Tables (Auto-created)

```sql
-- Slow cook pattern tracking
CREATE TABLE slow_cook_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    group_id INTEGER NOT NULL,
    mention_timestamp REAL NOT NULL,
    pattern_data TEXT,  -- JSON data
    created_at REAL DEFAULT (julianday('now'))
);

-- Enhanced rescue tracking
CREATE TABLE rescue_tracking_enhanced (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    rescue_type TEXT NOT NULL,  -- MULTI_MENTION, SLOW_COOK, etc.
    group_id INTEGER NOT NULL,
    mention_count INTEGER DEFAULT 1,
    first_mention REAL NOT NULL,
    last_mention REAL NOT NULL,
    pattern_confidence REAL DEFAULT 0.0,
    created_at REAL DEFAULT (julianday('now'))
);

-- Analytics data storage
CREATE TABLE analytics_data (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_type TEXT NOT NULL,    -- 'slow_cook_summary', 'pattern_analysis'
    data_content TEXT NOT NULL, -- JSON data
    timestamp REAL DEFAULT (julianday('now'))
);
```

### Indexes for Performance
```sql
CREATE INDEX idx_slow_cook_ca ON slow_cook_patterns(ca);
CREATE INDEX idx_slow_cook_timestamp ON slow_cook_patterns(mention_timestamp);
CREATE INDEX idx_rescue_enhanced_ca ON rescue_tracking_enhanced(ca);
CREATE INDEX idx_analytics_type ON analytics_data(data_type);
```

## ⚙️ Configuration Parameters

### Phase 1 Specific Settings

```python
# In config.py
SLOW_COOK_TRACKING = {
    'enabled': True,
    'phase': 1,                    # Current phase
    'data_collection_only': True,  # Phase 1: collect data only
    'min_mentions_threshold': 3,
    'time_window_minutes': 60,
    'cross_group_weight': 1.5,
    'velocity_limit': 0.5,         # mentions per minute
    'pattern_memory_hours': 24
}

# Enhanced trending thresholds
TRENDING_CONFIG = {
    'high_volume_groups': [-1002333406905, -1002202241417],  # MEME 1000X, GMGN
    'enhanced_threshold': 6,        # mentions for high-volume
    'time_window_minutes': 8,
    'anti_pump_enabled': True,
    'velocity_threshold': 3.0,      # mentions per minute
    'organic_growth_minutes': 3
}
```

## 🔄 Integration Points

### 1. Main Bot Integration (`main.py`)

```python
# Phase 1 initialization
async def initialize(self):
    # ... existing initialization ...
    
    # Initialize Phase 1 Slow Cook Tracking
    logger.info("🐌 Initializing Phase 1 Slow Cook Pattern Analysis...")
    try:
        from src.slow_cook_analytics import SlowCookAnalytics
        slow_cook_analytics = SlowCookAnalytics()
        logger.info("   ✅ Slow cook analytics initialized")
        logger.info("   📊 Phase 1 data collection: ACTIVE")
    except Exception as e:
        logger.warning(f"   ⚠️ Slow cook analytics initialization failed: {e}")
```

### 2. Message Processing Integration

```python
# In bot.py message handler
async def _process_for_cas(self, message, group_id, group_name):
    # ... existing CA detection ...
    
    # Phase 1: Record all mentions for pattern analysis
    for ca in detected_cas:
        await self.slow_cook_analytics.record_mention(
            ca, group_id, time.time()
        )
        
        # Analyze slow cook pattern
        pattern = await self.slow_cook_analytics.analyze_pattern(ca)
        if pattern.is_candidate:
            logger.info(f"🐌 SLOW COOK CANDIDATE: {ca} | "
                       f"Confidence: {pattern.confidence:.2f}")
```

### 3. Enhanced CA Detection

```python
# In message_parser.py
def extract_contract_addresses(self, message_text: str) -> List[str]:
    cas = []
    
    # Extract from URLs (GMGN, pump.fun, etc.)
    url_cas = self._extract_cas_from_urls(message_text)
    cas.extend(url_cas)
    
    # Extract standalone CAs
    standalone_cas = self._extract_standalone_cas(cleaned_text)
    cas.extend(standalone_cas)
    
    # Validate (43-44 character Solana addresses)
    return [ca for ca in cas if self._validate_solana_ca(ca)]
```

## 🧪 Testing and Verification

### 1. Phase 1 Verification Script

```bash
python verify_phase1_implementation.py
```

**Checks**:
- File presence verification
- Import testing
- Configuration validation
- Component integration
- Log monitoring for Phase 1 indicators

### 2. CA Detection Testing

```bash
python test_ca_detection.py
```

**Tests**:
- GMGN URL extraction
- Standalone CA detection
- Validation logic
- Real message processing

### 3. Manual Verification

```bash
# Check Phase 1 logs
tail -f logs/cla_bot.log | grep "🐌\|SLOW COOK"

# Verify data collection
ls -la data/slow_cook_patterns.json
ls -la data/analytics_data.json
```

## 📊 Monitoring and Analytics

### Phase 1 Log Indicators

```
🐌 Initializing Phase 1 Slow Cook Pattern Analysis...
🐌 SLOW COOK TRACKING: Phase 1 pattern analysis enabled
🐌 SLOW COOK CANDIDATE: [CA] | Confidence: 0.85
🛡️ MULTI-MENTION RESCUE ELIGIBLE: [CA] from [GROUP]
🔄 CROSS-GROUP SLOW COOK: [CA] mentioned in 3 groups
🎯 SLOW COOK PATTERN: [CA] | Velocity: 0.3/min | Groups: 2
```

### Data Collection Files

```json
// data/slow_cook_patterns.json
{
  "patterns": {
    "CA_ADDRESS": {
      "mentions": [
        {
          "group_id": -1002202241417,
          "timestamp": 1722334567.123,
          "group_name": "GMGN Featured Signals"
        }
      ],
      "analysis": {
        "total_mentions": 5,
        "unique_groups": 2,
        "velocity": 0.4,
        "confidence": 0.75,
        "is_candidate": true
      }
    }
  },
  "summary": {
    "total_patterns": 150,
    "candidates": 23,
    "last_updated": 1722334567.123
  }
}
```

## 🚀 Deployment Steps

### 1. Pre-deployment Checklist

- [ ] All Phase 1 files present
- [ ] Import tests pass
- [ ] Configuration validated
- [ ] Database schema ready
- [ ] Telegram authentication working
- [ ] CA detection fixes applied

### 2. Deployment Process

```bash
# 1. Stop existing bot
pkill -f python

# 2. Apply Phase 1 changes (already done)
# Files: slow_cook_analytics.py, enhanced trending_analyzer.py, etc.

# 3. Start bot with Phase 1
python main.py

# 4. Verify Phase 1 activation
python verify_phase1_implementation.py

# 5. Monitor logs
tail -f logs/cla_bot.log | grep "🐌"
```

### 3. Rollback Plan

```bash
# If issues occur, revert to previous version
git checkout HEAD~1 -- src/trending_analyzer.py src/ca_rescue_tracker.py
git checkout HEAD~1 -- src/message_parser.py
# Remove Phase 1 files
rm src/slow_cook_analytics.py
# Restart bot
python main.py
```

## 🔮 Future Phases

### Phase 2: Active Slow Cook Detection
- Real-time slow cook scoring
- Automatic forwarding of slow cook candidates
- Advanced pattern recognition

### Phase 3: Machine Learning Integration
- Pattern learning from historical data
- Predictive slow cook identification
- Adaptive thresholds

## 📝 Maintenance

### Regular Tasks
- Monitor data collection files growth
- Analyze pattern effectiveness
- Adjust thresholds based on data
- Clean up old pattern data (>7 days)

### Performance Monitoring
- Database query performance
- Memory usage of pattern storage
- CA detection accuracy
- False positive rates

---

**Phase 1 Status**: ✅ IMPLEMENTED AND READY
**Next Phase**: Data collection and analysis (1-2 weeks)
**Documentation Version**: 1.0
**Last Updated**: 2025-07-30
