#!/usr/bin/env python3
"""
Phase 1 Implementation Verification Script
Verifies that slow cook pattern tracking is working correctly after restart.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path

def print_status(message):
    """Print timestamped status message."""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_phase1_files():
    """Check that all Phase 1 files are present."""
    print_status("🔍 Checking Phase 1 file presence...")
    
    required_files = [
        'src/slow_cook_analytics.py',
        'src/trending_analyzer.py',
        'src/ca_rescue_tracker.py',
        'start_enhanced_bot.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print_status(f"   ✅ {file_path}")
    
    if missing_files:
        print_status(f"   ❌ Missing files: {missing_files}")
        return False
    
    print_status("✅ All Phase 1 files present")
    return True

def check_phase1_imports():
    """Test Phase 1 module imports."""
    print_status("🧪 Testing Phase 1 imports...")
    
    try:
        # Test slow cook analytics
        sys.path.append('.')
        from src.slow_cook_analytics import SlowCookAnalytics, PatternAnalysis
        print_status("   ✅ SlowCookAnalytics import successful")
        
        # Test trending analyzer slow cook integration
        from src.trending_analyzer import TrendingAnalyzer
        analyzer = TrendingAnalyzer()
        if hasattr(analyzer, 'slow_cook_patterns'):
            print_status("   ✅ TrendingAnalyzer slow cook integration present")
        else:
            print_status("   ❌ TrendingAnalyzer missing slow cook integration")
            return False
        
        # Test rescue tracker multi-mention integration
        from src.ca_rescue_tracker import CARescueTracker
        print_status("   ✅ CARescueTracker import successful")
        
        return True
        
    except Exception as e:
        print_status(f"   ❌ Import test failed: {e}")
        return False

def check_data_directory():
    """Check data directory setup."""
    print_status("📁 Checking data directory...")
    
    if not os.path.exists('data'):
        os.makedirs('data')
        print_status("   📁 Created data directory")
    else:
        print_status("   ✅ Data directory exists")
    
    return True

def monitor_bot_logs(duration_seconds=60):
    """Monitor bot logs for Phase 1 indicators."""
    print_status(f"📊 Monitoring bot logs for {duration_seconds} seconds...")
    
    log_file = 'logs/cla_bot.log'
    if not os.path.exists(log_file):
        print_status(f"   ❌ Log file not found: {log_file}")
        return False
    
    # Get initial file size
    initial_size = os.path.getsize(log_file)
    start_time = time.time()
    
    phase1_indicators = {
        'slow_cook_init': False,
        'slow_cook_filtered': False,
        'multi_mention_rescue': False,
        'cross_group_pattern': False,
        'slow_cook_candidate': False
    }
    
    print_status("   🔍 Looking for Phase 1 indicators:")
    print_status("      🐌 Slow cook initialization")
    print_status("      🐌 SLOW COOK FILTERED logs")
    print_status("      🛡️ MULTI-MENTION RESCUE ELIGIBLE logs")
    print_status("      🔄 CROSS-GROUP SLOW COOK logs")
    print_status("      🎯 SLOW COOK CANDIDATE logs")
    
    while time.time() - start_time < duration_seconds:
        try:
            current_size = os.path.getsize(log_file)
            if current_size > initial_size:
                # Read new content
                with open(log_file, 'r', encoding='utf-8') as f:
                    f.seek(initial_size)
                    new_content = f.read()
                    initial_size = current_size
                
                # Check for Phase 1 indicators
                if 'Slow cook analytics initialized' in new_content:
                    phase1_indicators['slow_cook_init'] = True
                    print_status("      ✅ Slow cook analytics initialization detected")
                
                if 'SLOW COOK FILTERED' in new_content:
                    phase1_indicators['slow_cook_filtered'] = True
                    print_status("      ✅ Slow cook filtering detected")
                
                if 'MULTI-MENTION RESCUE ELIGIBLE' in new_content:
                    phase1_indicators['multi_mention_rescue'] = True
                    print_status("      ✅ Multi-mention rescue tracking detected")
                
                if 'CROSS-GROUP SLOW COOK' in new_content:
                    phase1_indicators['cross_group_pattern'] = True
                    print_status("      ✅ Cross-group pattern detection detected")
                
                if 'SLOW COOK CANDIDATE' in new_content:
                    phase1_indicators['slow_cook_candidate'] = True
                    print_status("      ✅ Slow cook candidate detection detected")
        
        except Exception as e:
            print_status(f"   ⚠️ Error reading log: {e}")
        
        time.sleep(2)
    
    # Summary
    detected_count = sum(phase1_indicators.values())
    total_indicators = len(phase1_indicators)
    
    print_status(f"📊 Phase 1 Detection Summary: {detected_count}/{total_indicators} indicators found")
    
    if phase1_indicators['slow_cook_init']:
        print_status("✅ Phase 1 initialization confirmed")
    else:
        print_status("❌ Phase 1 initialization not detected")
    
    return detected_count >= 1  # At least initialization should be detected

def check_analytics_data():
    """Check if analytics data files are being created."""
    print_status("📊 Checking analytics data creation...")
    
    analytics_file = 'data/slow_cook_analytics.json'
    
    # Wait a bit for data to be created
    for i in range(30):  # Wait up to 30 seconds
        if os.path.exists(analytics_file):
            print_status(f"   ✅ Analytics data file created: {analytics_file}")
            
            try:
                with open(analytics_file, 'r') as f:
                    data = json.load(f)
                    print_status(f"   📊 Data entries: {len(data.get('patterns', []))}")
                    return True
            except Exception as e:
                print_status(f"   ⚠️ Error reading analytics data: {e}")
        
        time.sleep(1)
    
    print_status("   ⚠️ Analytics data file not created yet (may take time)")
    return True  # Not critical for immediate verification

def main():
    """Main verification function."""
    print_status("=" * 60)
    print_status("🐌 PHASE 1 SLOW COOK IMPLEMENTATION VERIFICATION")
    print_status("=" * 60)
    
    # Step 1: Check files
    if not check_phase1_files():
        print_status("❌ Phase 1 file check failed")
        return False
    
    # Step 2: Check imports
    if not check_phase1_imports():
        print_status("❌ Phase 1 import check failed")
        return False
    
    # Step 3: Check data directory
    check_data_directory()
    
    # Step 4: Monitor logs for Phase 1 activity
    print_status("⏱️ Starting 60-second monitoring period...")
    log_success = monitor_bot_logs(60)
    
    # Step 5: Check analytics data
    check_analytics_data()
    
    # Final assessment
    print_status("=" * 60)
    if log_success:
        print_status("🎉 PHASE 1 VERIFICATION SUCCESSFUL!")
        print_status("✅ Slow cook pattern tracking is operational")
        print_status("📊 Data collection has begun")
        print_status("🔍 Monitor logs for ongoing pattern detection")
    else:
        print_status("⚠️ PHASE 1 VERIFICATION INCOMPLETE")
        print_status("🔧 Bot may need restart to apply Phase 1 changes")
        print_status("📋 Check startup logs for initialization messages")
    
    print_status("=" * 60)
    return log_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print_status("Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print_status(f"Verification failed with error: {e}")
        sys.exit(1)
