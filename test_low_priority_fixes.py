"""Test script to verify LOW PRIORITY fixes are working correctly."""

import asyncio
import sys
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_dependency_injection():
    """Test that global singletons have been replaced with dependency injection."""
    print("🔗 Testing Dependency Injection...")
    
    try:
        from src.dependency_container import DependencyContainer
        
        # Test dependency container
        container = DependencyContainer()
        print("  ✅ Dependency container created")
        
        # Test that components can be retrieved (even if not initialized)
        component = container.get('non_existent')
        print(f"  ✅ Component retrieval working: {component is None}")
        
        # Test initialization status
        is_initialized = container.is_initialized()
        print(f"  ✅ Initialization status: {is_initialized}")
        
        print("  ✅ Dependency injection system working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing dependency injection: {e}")

async def test_database_optimization():
    """Test database query optimizations."""
    print("\n💾 Testing Database Optimizations...")
    
    try:
        from src.database import DatabaseManager
        
        # Test database manager with optimization features
        db_manager = DatabaseManager()
        print("  ✅ Database manager with optimizations created")
        
        # Test batch lookup method exists
        if hasattr(db_manager, 'batch_ca_lookup'):
            print("  ✅ Batch CA lookup method available")
        else:
            print("  ⚠️ Batch CA lookup method not found")
        
        # Test query caching method exists
        if hasattr(db_manager, 'get_cached_query_result'):
            print("  ✅ Query caching method available")
        else:
            print("  ⚠️ Query caching method not found")
        
        # Test performance tracking
        if hasattr(db_manager, 'query_stats'):
            print(f"  ✅ Query performance tracking: {len(db_manager.query_stats)} metrics")
        else:
            print("  ⚠️ Query performance tracking not found")
        
        print("  ✅ Database optimizations working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing database optimizations: {e}")

async def test_structured_logging():
    """Test structured logging implementation."""
    print("\n📝 Testing Structured Logging...")
    
    try:
        from src.structured_logger import StructuredLogger, CorrelationContext, get_logger
        
        # Test structured logger creation
        logger = get_logger("test_component")
        print("  ✅ Structured logger created")
        
        # Test logging methods
        logger.info("Test info message", test_param="test_value")
        logger.debug("Test debug message")
        logger.warning("Test warning message")
        print("  ✅ Logging methods working")
        
        # Test correlation context
        with CorrelationContext("test-123") as correlation_id:
            logger.info("Test message with correlation", correlation_id=correlation_id)
            print(f"  ✅ Correlation context working: {correlation_id}")
        
        # Test performance logging
        logger.performance("test_operation", 150.5, extra_data="test")
        print("  ✅ Performance logging working")
        
        print("  ✅ Structured logging working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing structured logging: {e}")

async def test_unit_test_framework():
    """Test unit testing framework."""
    print("\n🧪 Testing Unit Test Framework...")
    
    try:
        import os
        
        # Check if test files exist
        test_files = [
            'tests/test_message_processor.py',
            'tests/test_ca_analyzer.py',
            'tests/test_dependency_container.py'
        ]
        
        existing_tests = []
        for test_file in test_files:
            if os.path.exists(test_file):
                existing_tests.append(test_file)
                print(f"  ✅ Test file exists: {test_file}")
            else:
                print(f"  ⚠️ Test file missing: {test_file}")
        
        print(f"  📊 Test coverage: {len(existing_tests)}/{len(test_files)} test files")
        
        # Test that pytest can be imported
        try:
            import pytest
            print("  ✅ Pytest framework available")
        except ImportError:
            print("  ⚠️ Pytest not installed")
        
        print("  ✅ Unit test framework ready!")
        
    except Exception as e:
        print(f"  ❌ Error testing unit test framework: {e}")

async def test_refactored_components():
    """Test that refactored components work with dependency injection."""
    print("\n🏗️ Testing Refactored Components...")
    
    try:
        # Test MessageProcessor with dependency injection
        from src.message_processor import MessageProcessor
        from unittest.mock import Mock
        
        # Create mock dependencies
        mock_ca_analyzer = Mock()
        mock_forwarding_manager = Mock()
        mock_enhanced_stats = Mock()
        
        # Test MessageProcessor creation with dependencies
        processor = MessageProcessor(mock_ca_analyzer, mock_forwarding_manager, mock_enhanced_stats)
        print("  ✅ MessageProcessor with dependency injection")
        
        # Test CAAnalyzer with dependency injection
        from src.ca_analyzer import CAAnalyzer
        
        analyzer = CAAnalyzer(
            ca_detector=Mock(),
            trending_analyzer=Mock(),
            high_volume_analyzer=Mock(),
            enhanced_stats_tracker=Mock(),
            ca_rescue_tracker=Mock()
        )
        print("  ✅ CAAnalyzer with dependency injection")
        
        # Test ForwardingManager with dependency injection
        from src.forwarding_manager import ForwardingManager
        
        manager = ForwardingManager(
            bonkbot_integration=Mock(),
            cla_v2_integration=Mock(),
            monaco_pnl_integration=Mock(),
            enhanced_stats_tracker=Mock()
        )
        print("  ✅ ForwardingManager with dependency injection")
        
        print("  ✅ Refactored components working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing refactored components: {e}")

async def test_configuration_enhancements():
    """Test configuration system enhancements."""
    print("\n⚙️ Testing Configuration Enhancements...")
    
    try:
        from config import config
        
        # Test new configuration sections
        if hasattr(config, 'rescue'):
            print(f"  ✅ Rescue config: enabled={config.rescue.enabled}")
        else:
            print("  ⚠️ Rescue config not found")
        
        if hasattr(config, 'performance'):
            print(f"  ✅ Performance config: dedup_window={config.performance.message_dedup_window_seconds}s")
        else:
            print("  ⚠️ Performance config not found")
        
        if hasattr(config, 'integration'):
            print(f"  ✅ Integration config: timeout={config.integration.forwarding_timeout_seconds}s")
        else:
            print("  ⚠️ Integration config not found")
        
        # Test configuration validation
        if hasattr(config, 'get_configuration_summary'):
            summary = config.get_configuration_summary()
            print(f"  ✅ Configuration summary: {len(summary)} sections")
        else:
            print("  ⚠️ Configuration summary not available")
        
        print("  ✅ Configuration enhancements working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing configuration enhancements: {e}")

async def test_backward_compatibility():
    """Test that existing functionality still works."""
    print("\n🔄 Testing Backward Compatibility...")
    
    try:
        # Test that original bot still works
        from src.bot import CLABot
        print("  ✅ Original CLABot class still available")
        
        # Test that global instances still work for backward compatibility
        from src.group_manager import group_manager
        from src.enhanced_stats_tracker import enhanced_stats
        
        # Test group manager
        test_group_id = -1002380594298
        is_low_volume = group_manager.is_low_volume_group(test_group_id)
        print(f"  ✅ Group manager working: {is_low_volume}")
        
        # Test enhanced stats
        current_stats = enhanced_stats.get_current_stats()
        print(f"  ✅ Enhanced stats working: {len(current_stats)} categories")
        
        print("  ✅ Backward compatibility maintained!")
        
    except Exception as e:
        print(f"  ❌ Error testing backward compatibility: {e}")

async def run_comprehensive_test():
    """Run all low priority fix tests."""
    print("🔧 LOW PRIORITY FIXES VERIFICATION")
    print("=" * 60)
    print(f"📅 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # Run all tests
    await test_dependency_injection()
    await test_database_optimization()
    await test_structured_logging()
    await test_unit_test_framework()
    await test_refactored_components()
    await test_configuration_enhancements()
    await test_backward_compatibility()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print()
    print("=" * 60)
    print(f"✅ All tests completed in {duration:.2f} seconds")
    print("🎯 LOW PRIORITY FIXES VERIFICATION COMPLETE")

def main():
    """Main test function."""
    try:
        asyncio.run(run_comprehensive_test())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")

if __name__ == "__main__":
    main()
