"""Error handling and retry mechanisms for CLA v2.0 Bot."""

import asyncio
import functools
from typing import Callable, Any, Optional, Type, Tuple
from datetime import datetime, timedelta
from loguru import logger
from telethon.errors import (
    FloodWaitError, 
    NetworkMigrateError, 
    PhoneMigrateError,
    UserMigrateError,
    SessionPasswordNeededError,
    AuthKeyUnregisteredError
)

class RetryConfig:
    """Configuration for retry mechanisms."""
    
    def __init__(self, 
                 max_attempts: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 exponential_base: float = 2.0,
                 exceptions: Tuple[Type[Exception], ...] = (Exception,)):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.exceptions = exceptions

class ErrorHandler:
    """Centralized error handling and recovery with enhanced features."""

    def __init__(self):
        self.error_counts = {}
        self.last_errors = {}
        self.circuit_breakers = {}

        # Enhanced error tracking
        self.error_history = []
        self.fallback_handlers = {}
        self.stats = {
            'total_errors': 0,
            'resolved_errors': 0,
            'failed_retries': 0,
            'successful_retries': 0,
            'fallbacks_triggered': 0
        }
    
    async def handle_error_with_retry(self, operation: Callable, operation_name: str,
                                     max_retries: int = 3, base_delay: float = 1.0,
                                     context: Optional[dict] = None) -> Any:
        """Execute operation with retry logic and error handling."""
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                if attempt > 0:
                    delay = base_delay * (2 ** (attempt - 1))
                    delay = min(delay, 60.0)  # Max 60 seconds
                    logger.info(f"🔄 Retrying {operation_name} (attempt {attempt + 1}/{max_retries + 1}) after {delay}s")
                    await asyncio.sleep(delay)

                result = await operation() if asyncio.iscoroutinefunction(operation) else operation()

                if attempt > 0:
                    self.stats['successful_retries'] += 1
                    logger.info(f"✅ {operation_name} succeeded on retry attempt {attempt + 1}")

                return result

            except Exception as e:
                last_error = e
                self.stats['total_errors'] += 1

                error_msg = f"❌ Error in {operation_name} (attempt {attempt + 1}): {str(e)}"
                if attempt == max_retries:
                    logger.error(error_msg)
                    self.stats['failed_retries'] += 1
                else:
                    logger.warning(error_msg)

        # All retries failed
        raise last_error

    async def handle_telegram_error(self, error: Exception, context: str = "") -> bool:
        """Handle Telegram-specific errors."""
        try:
            if isinstance(error, FloodWaitError):
                wait_time = error.seconds
                logger.warning(f"Rate limited for {wait_time} seconds in {context}")
                await asyncio.sleep(wait_time)
                return True
                
            elif isinstance(error, (NetworkMigrateError, PhoneMigrateError, UserMigrateError)):
                logger.error(f"Migration error in {context}: {error}")
                # These require reconnection
                return False
                
            elif isinstance(error, AuthKeyUnregisteredError):
                logger.error(f"Auth key unregistered in {context}: {error}")
                # Requires re-authentication
                return False
                
            elif isinstance(error, SessionPasswordNeededError):
                logger.error(f"2FA required in {context}: {error}")
                # Requires user intervention
                return False
                
            else:
                logger.error(f"Unhandled Telegram error in {context}: {error}")
                return False
                
        except Exception as e:
            logger.error(f"Error in error handler: {e}")
            return False
    
    def track_error(self, error: Exception, context: str):
        """Track error occurrences for monitoring."""
        error_key = f"{context}:{type(error).__name__}"
        
        # Update error count
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Update last error time
        self.last_errors[error_key] = datetime.now()
        
        # Log error details
        logger.error(f"Error tracked - {error_key}: {error}")
    
    def should_circuit_break(self, context: str, threshold: int = 5, window_minutes: int = 10) -> bool:
        """Check if circuit breaker should activate."""
        now = datetime.now()
        window_start = now - timedelta(minutes=window_minutes)
        
        # Count recent errors for this context
        recent_errors = 0
        for error_key, last_time in self.last_errors.items():
            if error_key.startswith(f"{context}:") and last_time > window_start:
                recent_errors += 1
        
        if recent_errors >= threshold:
            if context not in self.circuit_breakers:
                self.circuit_breakers[context] = now
                logger.warning(f"Circuit breaker activated for {context}")
            return True
        
        return False
    
    def reset_circuit_breaker(self, context: str):
        """Reset circuit breaker for a context."""
        if context in self.circuit_breakers:
            del self.circuit_breakers[context]
            logger.info(f"Circuit breaker reset for {context}")
    
    def get_error_stats(self) -> dict:
        """Get error statistics."""
        return {
            'error_counts': self.error_counts.copy(),
            'active_circuit_breakers': list(self.circuit_breakers.keys()),
            'total_errors': sum(self.error_counts.values())
        }

def with_retry(config: Optional[RetryConfig] = None):
    """Decorator for adding retry logic to functions."""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                        
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        # Last attempt failed
                        break
                    
                    # Calculate delay
                    delay = min(
                        config.base_delay * (config.exponential_base ** attempt),
                        config.max_delay
                    )
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {delay}s")
                    await asyncio.sleep(delay)
            
            # All attempts failed
            logger.error(f"All {config.max_attempts} attempts failed for {func.__name__}")
            raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return func(*args, **kwargs)
                        
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        # Last attempt failed
                        break
                    
                    # Calculate delay
                    delay = min(
                        config.base_delay * (config.exponential_base ** attempt),
                        config.max_delay
                    )
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. Retrying in {delay}s")
                    import time
                    time.sleep(delay)
            
            # All attempts failed
            logger.error(f"All {config.max_attempts} attempts failed for {func.__name__}")
            raise last_exception
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def with_error_handling(context: str, error_handler: Optional[ErrorHandler] = None):
    """Decorator for adding error handling to functions."""
    if error_handler is None:
        error_handler = ErrorHandler()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs) -> Any:
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
                    
            except Exception as e:
                error_handler.track_error(e, context)
                
                # Try to handle Telegram errors
                if "telegram" in context.lower():
                    if await error_handler.handle_telegram_error(e, context):
                        # Error was handled, try again
                        try:
                            if asyncio.iscoroutinefunction(func):
                                return await func(*args, **kwargs)
                            else:
                                return func(*args, **kwargs)
                        except Exception as retry_error:
                            logger.error(f"Retry failed in {context}: {retry_error}")
                            raise retry_error
                
                # Re-raise if not handled
                raise e
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
                    
            except Exception as e:
                error_handler.track_error(e, context)
                raise e
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Global error handler instance
global_error_handler = ErrorHandler()

# Common retry configurations
TELEGRAM_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=2.0,
    max_delay=30.0,
    exceptions=(FloodWaitError, ConnectionError, TimeoutError)
)

DATABASE_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    base_delay=1.0,
    max_delay=10.0,
    exceptions=(Exception,)  # Catch all database errors
)

NETWORK_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    base_delay=1.0,
    max_delay=15.0,
    exceptions=(ConnectionError, TimeoutError, OSError)
)
