#!/usr/bin/env python3
"""
Test Contract Address Detection
"""

import sys
sys.path.append('.')

from src.message_parser import MessageParser

def test_ca_detection():
    """Test CA detection functionality."""
    
    parser = MessageParser()
    test_ca = '7xKXtg2CW3El9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn'
    
    print("=" * 60)
    print("CONTRACT ADDRESS DETECTION TEST")
    print("=" * 60)
    
    print(f"Testing CA: {test_ca}")
    print(f"Length: {len(test_ca)}")
    print(f"Valid: {parser._validate_solana_ca(test_ca)}")
    
    # Test extraction with deduplication priority testing
    test_messages = [
        # Message with SAME CA in both URL and backticks (should return only 1 CA, prioritize standalone)
        '** 3 KOL Buy **[**GOOD**](https://gmgn.ai/sol/token/mi4bpzjFBoXPanh3KwtYLHYiELieZGQiC3qauG9bonk)**!**\n🟢🟢🟢\n\n**💵 KOL Inflow净流入:$-260.4709(-1.4119 Sol) **\n**💳 KOL Buy/Sell:3/2**\n\n**$GOOD**(Do Good)\n`mi4bpzjFBoXPanh3KwtYLHYiELieZGQiC3qauG9bonk`',

        # Message with DIFFERENT CAs in URL vs backticks (should return both)
        '**Check this** [**TOKEN**](https://gmgn.ai/sol/token/So11111111111111111111111111111111111111112) and also `mi4bpzjFBoXPanh3KwtYLHYiELieZGQiC3qauG9bonk`',

        # Message with only URL CA
        '**Check** [**PUMP**](https://pump.fun/6jiL8tdTT28hkkGt8CMJT1tzdt6RJE9rWZmwdtjXbonk) **this token!**',

        # Message with only standalone CA
        '**$CMINI**(ClaudeMini)\n`6jiL8tdTT28hkkGt8CMJT1tzdt6RJE9rWZmwdtjXbonk`',

        # Simple standalone CA
        'mi4bpzjFBoXPanh3KwtYLHYiELieZGQiC3qauG9bonk',
    ]
    
    print("\nTesting message extraction:")
    for i, msg in enumerate(test_messages):
        print(f"\nMessage {i+1}: {msg}")
        cas = parser.extract_contract_addresses(msg)
        print(f"  Extracted: {len(cas)} CAs - {cas}")
        
        # Test individual steps
        cleaned = parser._clean_message_text_no_url_extraction(msg)
        print(f"  Cleaned: {cleaned}")
        
        standalone = parser._extract_standalone_cas(cleaned)
        print(f"  Standalone: {standalone}")

if __name__ == "__main__":
    test_ca_detection()
