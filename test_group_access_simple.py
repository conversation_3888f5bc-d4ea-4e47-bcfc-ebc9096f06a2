#!/usr/bin/env python3
"""
Simple Group Access Test
Tests if the bot can access and read from Telegram groups
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add project root to path
sys.path.append('.')

from config import *
from telethon import TelegramClient, events
from src.logger_setup import setup_logging

# Setup logging
logger = setup_logging()

async def test_group_access():
    """Test group access and message reception"""
    print("=" * 80)
    print("🔍 SIMPLE GROUP ACCESS TEST")
    print("=" * 80)
    
    try:
        # Create Telegram client using config objects
        telegram_config = TelegramConfig()
        client = TelegramClient(
            telegram_config.session_name,
            telegram_config.api_id,
            telegram_config.api_hash
        )
        
        print("📱 Connecting to Telegram...")
        await client.start()
        
        me = await client.get_me()
        print(f"✅ Connected as: {me.first_name} (@{me.username})")
        
        # Test group access
        target_group_config = TargetGroupConfig()
        groups_to_test = [target_group_config.group_id] + target_group_config.additional_group_ids
        
        print(f"\n🔧 Testing access to {len(groups_to_test)} groups:")
        
        accessible_groups = []
        for group_id in groups_to_test:
            try:
                entity = await client.get_entity(group_id)
                group_name = getattr(entity, 'title', f'Group {group_id}')
                print(f"   ✅ {group_name} ({group_id}) - ACCESSIBLE")
                accessible_groups.append(group_id)
            except Exception as e:
                print(f"   ❌ Group {group_id} - ERROR: {e}")
        
        if not accessible_groups:
            print("❌ No groups accessible! Bot cannot receive messages.")
            return
        
        print(f"\n📊 {len(accessible_groups)} groups accessible")
        print("🎯 Setting up message listener for 30 seconds...")
        
        message_count = 0
        start_time = time.time()
        
        @client.on(events.NewMessage(chats=accessible_groups))
        async def test_handler(event):
            nonlocal message_count
            message_count += 1
            
            group_name = "Unknown"
            try:
                chat = await event.get_chat()
                group_name = getattr(chat, 'title', f'Group {event.chat_id}')
            except:
                pass
            
            elapsed = time.time() - start_time
            message_text = event.message.text or "[Media/Sticker]"
            preview = message_text[:50] + "..." if len(message_text) > 50 else message_text
            
            print(f"📨 [{elapsed:.1f}s] Message #{message_count} from {group_name}: {preview}")
        
        print("⏱️ Listening for messages...")
        print("💡 Send a test message in any monitored group to verify")
        print("-" * 80)
        
        # Listen for 30 seconds
        await asyncio.sleep(30)
        
        print("-" * 80)
        print("📊 TEST RESULTS:")
        print(f"   Messages received: {message_count}")
        print(f"   Test duration: 30 seconds")
        
        if message_count > 0:
            print("✅ MESSAGE RECEPTION: WORKING")
            print("   The bot can receive messages from Telegram groups")
        else:
            print("❌ MESSAGE RECEPTION: NO MESSAGES")
            print("   Possible issues:")
            print("   1. Groups are inactive during test period")
            print("   2. Message handler not properly registered")
            print("   3. Network connectivity issues")
            print("   4. Telegram API rate limiting")
        
        await client.disconnect()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 80)
    print("🎉 TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_group_access())
