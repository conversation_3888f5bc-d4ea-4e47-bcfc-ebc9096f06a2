#!/usr/bin/env python3
"""
Test Telegram Connection
"""

import asyncio
import sys
from loguru import logger
from config import config

async def test_telegram():
    """Test basic Telegram connection."""
    try:
        print("🔍 Testing Telegram connection...")
        
        # Import Telegram client
        from src.telegram_client import TelegramClient
        
        print("✅ TelegramClient imported successfully")
        
        # Create client
        client = TelegramClient()
        print("✅ TelegramClient created successfully")
        
        # Test connection
        print("🔗 Attempting to connect to Telegram...")
        await client.connect()
        print("✅ Connected to Telegram successfully!")
        
        # Test basic functionality
        print("📱 Testing basic client functionality...")
        me = await client.client.get_me()
        print(f"✅ Authenticated as: {me.first_name} (@{me.username})")
        
        # Disconnect
        await client.disconnect()
        print("✅ Disconnected successfully")
        
        print("🎉 Telegram connection test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Telegram connection test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    print("=" * 60)
    print("🧪 TELEGRAM CONNECTION TEST")
    print("=" * 60)
    
    success = await test_telegram()
    
    print("=" * 60)
    if success:
        print("✅ TEST RESULT: SUCCESS")
        print("🚀 Bot should be able to start normally")
    else:
        print("❌ TEST RESULT: FAILED")
        print("🔧 Fix Telegram connection issues before starting bot")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Test failed with error: {e}")
        sys.exit(1)
