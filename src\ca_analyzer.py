"""Contract Address analysis component for CLA v2.0 Bot."""

import asyncio
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger

from src.group_manager import group_manager
from src.slow_cook_analytics import SlowCookAnalytics

class CAAnalyzer:
    """Handles contract address detection, trending analysis, and rescue operations."""

    def __init__(self, ca_detector, trending_analyzer, high_volume_analyzer,
                 enhanced_stats_tracker=None, ca_rescue_tracker=None):
        """Initialize CA analyzer with dependencies."""
        self.ca_detector = ca_detector
        self.trending_analyzer = trending_analyzer
        self.high_volume_analyzer = high_volume_analyzer
        self.enhanced_stats_tracker = enhanced_stats_tracker
        self.ca_rescue_tracker = ca_rescue_tracker

        # PHASE 1: Initialize slow cook analytics
        self.slow_cook_analytics = SlowCookAnalytics()
        
        # Statistics
        self.stats = {
            'messages_analyzed': 0,
            'cas_detected': 0,
            'trending_cas_found': 0,
            'rescue_attempts': 0,
            'analysis_errors': 0
        }
        
        logger.info("CA Analyzer initialized")
    
    async def analyze_message(self, message_text: str, message_id: int, chat_id: int, 
                            group_name: str, group_type: str) -> Dict[str, Any]:
        """Analyze message for contract addresses and trending patterns."""
        analysis_start = time.perf_counter()
        
        try:
            self.stats['messages_analyzed'] += 1
            
            # Log analysis start
            if group_type == "HIGH-VOLUME":
                logger.info(f"🔥 HIGH-VOLUME CA ANALYSIS: Starting for {group_name}")
            elif group_type == "LOW-VOLUME":
                logger.info(f"⚡ LOW-VOLUME CA ANALYSIS: Starting for {group_name}")
            
            # Detect contract addresses
            ca_detection_start = time.perf_counter()
            new_cas = await self.ca_detector.process_message(message_text, message_id, chat_id)
            ca_detection_time = (time.perf_counter() - ca_detection_start) * 1000
            
            self.stats['cas_detected'] += len(new_cas)
            
            # Initialize result
            result = {
                'new_cas': new_cas,
                'trending_cas': [],
                'rescue_cas': [],
                'analysis_time_ms': 0,
                'ca_detection_time_ms': ca_detection_time,
                'trending_analysis_time_ms': 0,
                'rescue_check_time_ms': 0
            }
            
            # Check for rescue opportunities from low-volume groups
            if group_type == "LOW-VOLUME":
                rescue_start = time.perf_counter()
                rescue_cas = await self._check_rescue_opportunities(new_cas, chat_id, group_name, message_text)
                result['rescue_cas'] = rescue_cas
                result['rescue_check_time_ms'] = (time.perf_counter() - rescue_start) * 1000
                self.stats['rescue_attempts'] += len(rescue_cas)
            
            # Perform trending analysis for new CAs
            if new_cas:
                trending_start = time.perf_counter()
                trending_cas = await self._analyze_trending_patterns(
                    new_cas, chat_id, group_name, group_type, message_text
                )
                result['trending_cas'] = trending_cas
                result['trending_analysis_time_ms'] = (time.perf_counter() - trending_start) * 1000
                self.stats['trending_cas_found'] += len(trending_cas)
            
            # Log analysis results
            total_analysis_time = (time.perf_counter() - analysis_start) * 1000
            result['analysis_time_ms'] = total_analysis_time
            
            logger.info(f"📊 CA ANALYSIS COMPLETE: {group_name} | "
                       f"CAs: {len(new_cas)} | Trending: {len(result['trending_cas'])} | "
                       f"Rescue: {len(result['rescue_cas'])} | Time: {total_analysis_time:.1f}ms")
            
            return result
            
        except Exception as e:
            self.stats['analysis_errors'] += 1
            logger.error(f"Error in CA analysis: {e}")
            return {
                'new_cas': [],
                'trending_cas': [],
                'rescue_cas': [],
                'analysis_time_ms': 0,
                'error': str(e)
            }
    
    async def _check_rescue_opportunities(self, new_cas: List[str], chat_id: int, 
                                        group_name: str, message_text: str) -> List[str]:
        """Check for rescue opportunities from low-volume groups."""
        rescue_cas = []
        
        try:
            if self.ca_rescue_tracker:
                for ca in new_cas:
                    if await self.ca_rescue_tracker.check_for_rescue(ca, chat_id, group_name, message_text, self.ca_detector):
                        rescue_cas.append(ca)
                        logger.info(f"🚀 RESCUE TRIGGERED: {ca} from {group_name}")

        except Exception as e:
            logger.error(f"Error checking rescue opportunities: {e}")
        
        return rescue_cas
    
    async def _analyze_trending_patterns(self, new_cas: List[str], chat_id: int, 
                                       group_name: str, group_type: str, 
                                       message_text: str) -> List[str]:
        """Analyze trending patterns for contract addresses."""
        trending_cas = []
        
        try:
            for ca in new_cas:
                # Perform trending analysis
                trending_result = await self.trending_analyzer.analyze_ca_trending(
                    ca, chat_id, group_name, message_text
                )
                
                # Record high-volume detection
                if group_type == "HIGH-VOLUME":
                    await self.high_volume_analyzer.record_ca_detection(
                        ca, chat_id, group_name, trending_result.is_trending, 
                        trending_result.mention_count
                    )
                
                # Log trending result
                if trending_result.is_trending:
                    trending_cas.append(ca)
                    if group_type == "HIGH-VOLUME":
                        logger.info(f"🔥 HIGH-VOLUME QUALIFIED: {ca} with {trending_result.mention_count} mentions!")
                    elif group_type == "LOW-VOLUME":
                        logger.info(f"⚡ LOW-VOLUME QUALIFIED: {ca} (direct forwarding)")
                else:
                    # Handle noise filtering for high-volume groups
                    if group_type == "HIGH-VOLUME":
                        logger.info(f"🔥 HIGH-VOLUME NOISE FILTERED: {ca} (count: {trending_result.mention_count})")
                        
                        # Add to rescue-eligible cache for potential low-volume rescue
                        if self.ca_rescue_tracker:
                            await self.ca_rescue_tracker.add_filtered_ca(
                                ca=ca,
                                group_id=chat_id,
                                group_name=group_name,
                                mention_count=trending_result.mention_count,
                                message_text=message_text
                            )

                        # PHASE 1: Record filtered CA for analytics
                        if trending_result.mentions:
                            time_span_hours = (trending_result.latest_mention - trending_result.first_mention).total_seconds() / 3600
                            groups = [m.group_id for m in trending_result.mentions]
                            group_names = [m.group_name for m in trending_result.mentions]

                            await self.slow_cook_analytics.record_filtered_ca(
                                ca=ca,
                                mention_count=trending_result.mention_count,
                                time_span_hours=time_span_hours,
                                groups=groups,
                                group_names=group_names
                            )
                
                # Log trending result to database
                await self.ca_detector.db_manager.add_trending_result(
                    ca, chat_id, group_name,
                    trending_result.first_mention,
                    trending_result.latest_mention,
                    trending_result.mention_count,
                    trending_result.time_to_trend.total_seconds() if trending_result.time_to_trend else 0
                )
            
        except Exception as e:
            logger.error(f"Error analyzing trending patterns: {e}")
        
        return trending_cas
    
    async def cleanup_expired_data(self):
        """Cleanup expired data from analyzers."""
        try:
            # Cleanup trending analyzer data
            await self.trending_analyzer.cleanup_expired_data()
            
            # Cleanup rescue tracker expired entries
            if self.ca_rescue_tracker:
                await self.ca_rescue_tracker.cleanup_expired_entries()
            
            logger.debug("CA analyzer cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during CA analyzer cleanup: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get CA analyzer statistics."""
        return self.stats.copy()

class TrendingPatternDetector:
    """Detects trending patterns in contract address mentions."""
    
    def __init__(self):
        """Initialize trending pattern detector."""
        self.stats = {
            'patterns_analyzed': 0,
            'trending_detected': 0,
            'noise_filtered': 0,
            'detection_errors': 0
        }
        
        logger.info("Trending Pattern Detector initialized")
    
    async def detect_pattern(self, ca: str, mentions: List[Any]) -> Dict[str, Any]:
        """Detect trending pattern for a contract address."""
        try:
            self.stats['patterns_analyzed'] += 1
            
            # Analyze mention frequency
            mention_count = len(mentions)
            
            # Analyze time distribution
            if mentions:
                time_span = (mentions[-1].timestamp - mentions[0].timestamp).total_seconds()
                velocity = mention_count / max(1, time_span / 60)  # mentions per minute
            else:
                time_span = 0
                velocity = 0
            
            # Determine if trending
            is_trending = mention_count >= 6 and time_span >= 120  # 6 mentions in at least 2 minutes
            
            if is_trending:
                self.stats['trending_detected'] += 1
            else:
                self.stats['noise_filtered'] += 1
            
            return {
                'is_trending': is_trending,
                'mention_count': mention_count,
                'time_span_seconds': time_span,
                'velocity_per_minute': velocity,
                'pattern_type': 'trending' if is_trending else 'noise'
            }
            
        except Exception as e:
            self.stats['detection_errors'] += 1
            logger.error(f"Error detecting trending pattern: {e}")
            return {
                'is_trending': False,
                'error': str(e)
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get trending pattern detector statistics."""
        return self.stats.copy()
