"""Main bot class for CLA v2.0 - Telegram Memecoin Signal Monitoring Bot."""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Optional, List
from telethon import events
from loguru import logger

from src.telegram_client import TelegramClientManager
from src.database import DatabaseManager
from src.ca_detector import CADetector

from src.bonkbot_integration import BonkBotIntegration
# from src.winners_integration import WinnersIntegration  # Temporarily disabled
from src.cla_v2_integration import CLAv2Integration
from src.monaco_pnl_integration import MonacoPNLIntegration
from src.trending_analyzer import TrendingAnalyzer
from src.message_parser import MessageParser
from src.enhanced_stats_tracker import enhanced_stats
from src.high_volume_ca_analyzer import high_volume_analyzer
from src.ca_rescue_tracker import ca_rescue_tracker
from src.group_manager import group_manager
from config import config

class CLABot:
    """Main CLA v2.0 Bot class that coordinates all components."""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.telegram_client = TelegramClientManager()
        self.ca_detector = CADetector(db_manager)

        self.trending_analyzer = TrendingAnalyzer()
        self.bonkbot_integration = None
        self.winners_integration = None
        self.cla_v2_integration = None
        self.monaco_pnl_integration = None
        self.message_parser = MessageParser()

        # Message deduplication to prevent race conditions
        self.recent_messages = {}  # message_id -> timestamp
        self.message_dedup_window = 30  # seconds

        # Statistics
        self.stats = {
            'messages_processed': 0,
            'cas_detected': 0,
            'cas_sent_to_bonkbot': 0,
            'cas_sent_to_winners': 0,
            'cas_sent_to_cla_v2': 0,
            'cas_sent_to_monaco_pnl': 0,

            'gmgn_mentions': 0,
            'trending_qualified': 0,
            'noise_filtered': 0,
            'duplicate_messages_filtered': 0,
            'start_time': None
        }
        
        self.running = False
    
    async def initialize(self):
        """Initialize all bot components."""
        try:
            logger.info("Initializing CLA v2.0 Bot components...")
            
            # Initialize Telegram client
            if not await self.telegram_client.initialize():
                raise Exception("Failed to initialize Telegram client")
            
            # Connect to Telegram
            if not await self.telegram_client.connect():
                raise Exception("Failed to connect to Telegram")
            
            # Verify access to all target groups (including paused ones for future use)
            for group_id in config.target_group.all_group_ids:
                if not await self.telegram_client.join_group(group_id):
                    logger.warning(f"Could not verify access to group {group_id}")
                else:
                    status = config.target_group.group_status.get(group_id, 'ACTIVE')
                    logger.info(f"Verified access to group {group_id} [{status}]")
            
            # Initialize BonkBot integration
            self.bonkbot_integration = BonkBotIntegration(self.telegram_client)
            if not await self.bonkbot_integration.initialize():
                logger.warning("BonkBot integration failed - continuing without BonkBot forwarding")
                self.bonkbot_integration = None

            # Initialize WINNERS group integration - TEMPORARILY DISABLED
            # TODO: Re-enable WINNERS integration when ready
            # self.winners_integration = WinnersIntegration(self.telegram_client)
            # if not await self.winners_integration.initialize():
            #     logger.warning("WINNERS group integration failed - continuing without WINNERS forwarding")
            #     self.winners_integration = None
            self.winners_integration = None  # Temporarily disabled
            logger.info("⏸️ WINNERS integration temporarily disabled")

            # Initialize CLA v2.0 channel integration
            self.cla_v2_integration = CLAv2Integration(self.telegram_client)
            if not await self.cla_v2_integration.initialize():
                logger.warning("CLA v2.0 channel integration failed - continuing without CLA v2.0 forwarding")
                self.cla_v2_integration = None

            # Initialize Monaco PNL channel integration
            self.monaco_pnl_integration = MonacoPNLIntegration(self.telegram_client)
            if not await self.monaco_pnl_integration.initialize():
                logger.warning("Monaco PNL channel integration failed - continuing without Monaco PNL forwarding")
                self.monaco_pnl_integration = None
            
            # Setup message handler
            await self.telegram_client.setup_message_handler(self._handle_message)
            
            logger.info("Bot initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Bot initialization failed: {e}")
            return False
    
    async def start(self):
        """Start the bot."""
        try:
            self.running = True
            self.stats['start_time'] = datetime.now()
            
            logger.info("CLA v2.0 Bot started - monitoring for signals...")
            logger.info(f"Total groups configured: {len(config.target_group.all_group_ids)} | Active: {len(config.target_group.active_group_ids)}")
            logger.info(f"  - {config.target_group.group_name} ({config.target_group.group_id}) [ACTIVE]")
            for i, group_id in enumerate(config.target_group.additional_group_ids):
                group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {group_id}"
                status = config.target_group.group_status.get(group_id, 'UNKNOWN')
                logger.info(f"  - {group_name} ({group_id}) [{status}]")

            # Log forwarding destinations (WINNERS temporarily disabled)
            destinations = []
            if self.bonkbot_integration: destinations.append("BonkBot")
            if self.cla_v2_integration: destinations.append("CLA v2.0")
            if self.monaco_pnl_integration: destinations.append("Monaco PNL")
            # WINNERS temporarily disabled
            logger.info(f"Forwarding to {len(destinations)} destinations: {', '.join(destinations)} (WINNERS temporarily disabled)")
            logger.info(f"Cache period: {config.cache.ca_expiry_hours} hours (7 days)")
            
            # Start listening for messages
            await self.telegram_client.start_listening()

            # Add connection diagnostics
            logger.info(f"🔍 DIAGNOSTICS: Telegram client connected: {self.telegram_client.is_connected()}")

            # Test message handler with a simple ping
            logger.info("🔍 DIAGNOSTICS: Message handler should be active for these groups:")
            for group_id in config.target_group.active_group_ids:
                group_name = self._get_group_name(group_id)
                logger.info(f"   - {group_name} ({group_id})")

            logger.info("🔍 DIAGNOSTICS: Waiting for any message from monitored groups...")
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            self.running = False
    
    async def stop(self):
        """Stop the bot."""
        try:
            logger.info("Stopping CLA v2.0 Bot...")
            self.running = False
            
            # Stop all integrations
            if self.bonkbot_integration:
                await self.bonkbot_integration.shutdown()

            if self.winners_integration:
                await self.winners_integration.shutdown()

            if self.cla_v2_integration:
                await self.cla_v2_integration.shutdown()

            if self.monaco_pnl_integration:
                await self.monaco_pnl_integration.shutdown()
            
            # Disconnect Telegram client
            await self.telegram_client.disconnect()
            
            # Log final statistics
            await self._log_final_stats()
            
            logger.info("Bot stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping bot: {e}")
    
    async def _is_duplicate_message(self, message_id: int) -> bool:
        """Check if message was recently processed to prevent duplicates."""
        now = datetime.now()

        # Clean up old messages
        cutoff_time = now - timedelta(seconds=self.message_dedup_window)
        expired_messages = [mid for mid, timestamp in self.recent_messages.items() if timestamp < cutoff_time]
        for mid in expired_messages:
            del self.recent_messages[mid]

        # Check if message is duplicate
        if message_id in self.recent_messages:
            return True

        # Add message to recent list
        self.recent_messages[message_id] = now
        return False

    async def _handle_message(self, event):
        """Handle incoming Telegram messages with enhanced real-time diagnostics."""
        reception_time = time.perf_counter()
        from datetime import datetime
        reception_datetime = datetime.now()

        try:
            message = event.message
            message_text = message.text or ""
            chat_id = event.chat_id

            # Enhanced real-time diagnostic logging
            group_name = self._get_group_name(chat_id)
            logger.info(f"📨 REALTIME: {group_name} | ID={message.id} | Time={reception_datetime.strftime('%H:%M:%S.%f')[:-3]}")

            # High-volume group timing analysis
            if self.trending_analyzer.is_high_volume_group(chat_id):
                # Calculate time since last message from this group
                last_time_attr = f'_last_message_time_{chat_id}'
                last_time = getattr(self, last_time_attr, 0)
                time_diff = reception_time - last_time if last_time > 0 else 0
                setattr(self, last_time_attr, reception_time)

                if time_diff > 0:
                    rate = 1 / time_diff if time_diff > 0 else 0
                    logger.info(f"🔥 HIGH-VOLUME TIMING: {group_name} | Gap={time_diff*1000:.1f}ms | Rate={rate:.1f}/sec")
                else:
                    logger.info(f"🔥 HIGH-VOLUME TIMING: {group_name} | First message")

            # CRITICAL DEBUG: Log ALL message reception attempts
            logger.info(f"🔍 MESSAGE HANDLER CALLED: Chat={chat_id} | Group={group_name} | ID={message.id}")

            # Record message received in enhanced stats
            enhanced_stats.record_message_received(chat_id)

            # Check for duplicate messages (race condition protection)
            if await self._is_duplicate_message(message.id):
                self.stats['duplicate_messages_filtered'] += 1
                logger.info(f"🔍 DUPLICATE MESSAGE FILTERED: ID={message.id} | Group={group_name}")
                return

            # Skip empty messages
            if not message_text.strip():
                logger.info(f"🔍 EMPTY MESSAGE SKIPPED: Chat={chat_id} | Group={group_name}")
                return

            self.stats['messages_processed'] += 1

            # Concise logging for message reception
            if chat_id == -1002202241417:  # GMGN Featured Signals
                logger.info(f"🧠 GMGN: ID={message.id} | {message_text[:100]}...")
            elif chat_id == -1002333406905:  # MEME 1000X
                logger.info(f"🚀 MEME: ID={message.id} | {message_text[:100]}...")
            else:
                logger.info(f"📨 {group_name}: ID={message.id} | {message_text[:80]}...")

            # Process for contract addresses
            await self._process_for_cas(message_text, message.id, chat_id)



            # Log periodic statistics
            if self.stats['messages_processed'] % 100 == 0:
                await self._log_periodic_stats()

            # Cleanup trending analyzer data periodically
            if self.stats['messages_processed'] % 500 == 0:
                await self.trending_analyzer.cleanup_expired_data()

                # Cleanup rescue tracker expired entries
                await ca_rescue_tracker.cleanup_expired_entries()

            # Send hourly status reports
            if enhanced_stats.should_send_hourly_report():
                await self._send_hourly_status_report()

        except Exception as e:
            logger.error(f"Error handling message: {e}")
        finally:
            # Enhanced performance monitoring with real-time diagnostics
            processing_end = time.perf_counter()
            total_time = (processing_end - reception_time) * 1000

            # Log processing completion
            logger.info(f"✅ PROCESSED: {group_name} | ID={message.id} | Total={total_time:.1f}ms")

            # Check for processing delays
            if total_time > 100:
                logger.warning(f"⚠️ SLOW PROCESSING: {group_name} | ID={message.id} | {total_time:.1f}ms")
            elif total_time < 10:
                logger.debug(f"⚡ FAST PROCESSING: {group_name} | ID={message.id} | {total_time:.1f}ms")

            # Legacy performance monitoring (keep for compatibility)
            duration_ms = total_time
            enhanced_stats.record_performance_timing('message_handling', duration_ms)

            if duration_ms > 150:  # Increased threshold to reduce noise
                logger.warning(f"⚠️ SLOW MESSAGE HANDLING: {duration_ms:.1f}ms for {group_name} | ID={message.id}")
            elif duration_ms > 75:  # Medium performance warning
                logger.info(f"⚡ MEDIUM PROCESSING: {duration_ms:.1f}ms for {group_name} | ID={message.id}")
            elif duration_ms > 50:
                logger.debug(f"📊 MESSAGE TIMING: {duration_ms:.1f}ms for {group_name}")
    
    async def _process_for_cas(self, message_text: str, message_id: int, chat_id: int):
        """Process message for contract addresses with trending analysis and multi-destination forwarding."""
        pipeline_start = time.perf_counter()

        try:
            # Enhanced debug logging based on group type
            source_group = self._get_group_name(chat_id)
            if self.trending_analyzer.is_high_volume_group(chat_id):
                logger.info(f"🔥 HIGH-VOLUME CA PROCESSING: Starting trending analysis for message {message_id} from {source_group}")
            elif self.trending_analyzer.is_low_volume_group(chat_id):
                logger.info(f"⚡ LOW-VOLUME CA PROCESSING: Direct forwarding for message {message_id} from {source_group}")
            else:
                logger.info(f"CA PROCESSING: Starting detection for message {message_id} from {source_group}")

            # Detect new contract addresses (global duplicate prevention)
            ca_detection_start = time.perf_counter()
            new_cas = await self.ca_detector.process_message(message_text, message_id, chat_id)
            ca_detection_time = (time.perf_counter() - ca_detection_start) * 1000

            # Check for rescue opportunities from low-volume groups
            rescue_cas = []
            if group_manager.is_low_volume_group(chat_id):
                for ca in new_cas:
                    if await ca_rescue_tracker.check_for_rescue(ca, chat_id, source_group, message_text, self.ca_detector):
                        rescue_cas.append(ca)
                        logger.info(f"🚀 RESCUE TRIGGERED: {ca} from {source_group}")

            # Debug logging for CA detection results based on group type with timing
            if self.trending_analyzer.is_high_volume_group(chat_id):
                logger.info(f"🔥 HIGH-VOLUME CA DETECTION: Found {len(new_cas)} CAs in {ca_detection_time:.1f}ms: {new_cas}")
            elif self.trending_analyzer.is_low_volume_group(chat_id):
                logger.info(f"⚡ LOW-VOLUME CA DETECTION: Found {len(new_cas)} CAs in {ca_detection_time:.1f}ms: {new_cas}")
            else:
                logger.info(f"CA DETECTION: Found {len(new_cas)} CAs in {ca_detection_time:.1f}ms: {new_cas}")

            # Record CA detection stats
            enhanced_stats.record_ca_detected(chat_id, len(new_cas))
            enhanced_stats.record_performance_timing('ca_detection', ca_detection_time)

            # Log slow CA detection
            if ca_detection_time > 200:
                logger.warning(f"⚠️ SLOW CA DETECTION: {ca_detection_time:.1f}ms for {source_group}")

            if new_cas:
                self.stats['cas_detected'] += len(new_cas)

                # Track high-volume group activity
                if self.trending_analyzer.is_high_volume_group(chat_id):
                    self.stats['gmgn_mentions'] += len(new_cas)
                    logger.info(f"🔥 HIGH-VOLUME STATS UPDATE: Total mentions now: {self.stats['gmgn_mentions']}")

                # Process each CA through selective trending analysis
                trending_analysis_start = time.perf_counter()
                trending_cas = []
                for ca in new_cas:
                    # Enhanced debug logging based on group type
                    if self.trending_analyzer.is_high_volume_group(chat_id):
                        logger.info(f"🔥 HIGH-VOLUME TRENDING: Analyzing CA {ca}")
                    elif self.trending_analyzer.is_low_volume_group(chat_id):
                        logger.info(f"⚡ LOW-VOLUME DIRECT: Processing CA {ca}")

                    # Add to database for tracking
                    await self.db_manager.add_ca_mention(ca, chat_id, source_group, message_id)

                    # Analyze trending status (selective protection)
                    trending_result = await self.trending_analyzer.analyze_ca_mention(ca, chat_id, source_group, message_id)

                    # Enhanced debug logging for trending results
                    if self.trending_analyzer.is_high_volume_group(chat_id):
                        logger.info(f"🔥 HIGH-VOLUME RESULT: CA={ca} | Trending={trending_result.is_trending} | Count={trending_result.mention_count}")

                        # Record high-volume CA detection for pattern analysis
                        high_volume_analyzer.record_ca_detection(
                            ca=ca,
                            group_id=chat_id,
                            group_name=source_group,
                            message_id=message_id,
                            message_text=message_text,
                            trending_qualified=trending_result.is_trending,
                            mention_count=trending_result.mention_count,
                            time_to_trend=trending_result.time_to_trend.total_seconds() if trending_result.time_to_trend else None
                        )

                    elif self.trending_analyzer.is_low_volume_group(chat_id):
                        logger.info(f"⚡ LOW-VOLUME RESULT: CA={ca} | Direct forwarding={trending_result.is_trending}")

                    if trending_result.is_trending:
                        trending_cas.append(ca)
                        self.stats['trending_qualified'] += 1

                        if self.trending_analyzer.is_high_volume_group(chat_id):
                            logger.info(f"🔥 HIGH-VOLUME QUALIFIED: {ca} with {trending_result.mention_count} mentions!")
                        elif self.trending_analyzer.is_low_volume_group(chat_id):
                            logger.info(f"⚡ LOW-VOLUME QUALIFIED: {ca} (direct forwarding)")

                        # Log trending result to database
                        await self.db_manager.add_trending_result(
                            ca, chat_id, source_group,
                            trending_result.first_mention,
                            trending_result.latest_mention,
                            trending_result.mention_count,
                            trending_result.time_to_trend.total_seconds() if trending_result.time_to_trend else 0
                        )
                    else:
                        # Only filter noise for high-volume groups (low-volume groups forward everything)
                        if self.trending_analyzer.is_high_volume_group(chat_id):
                            self.stats['noise_filtered'] += 1
                            logger.info(f"🔥 HIGH-VOLUME NOISE FILTERED: {ca} (count: {trending_result.mention_count})")

                            # TODO: Record noise filtering in enhanced stats for status reports
                            # enhanced_stats.record_noise_filtered(chat_id, 1)

                            # Add to rescue-eligible cache for potential low-volume rescue
                            await ca_rescue_tracker.add_filtered_ca(
                                ca=ca,
                                group_id=chat_id,
                                group_name=source_group,
                                mention_count=trending_result.mention_count,
                                message_text=message_text
                            )
                        # Low-volume groups don't have noise filtering - they forward everything

                trending_analysis_time = (time.perf_counter() - trending_analysis_start) * 1000
                logger.info(f"Detected {len(new_cas)} CAs from {source_group} | Trending: {len(trending_cas)} | Analysis: {trending_analysis_time:.1f}ms")

                # Record trending analysis stats
                enhanced_stats.record_trending_qualified(chat_id, len(trending_cas))
                enhanced_stats.record_performance_timing('trending_analysis', trending_analysis_time)

                # Log slow trending analysis
                if trending_analysis_time > 300:
                    logger.warning(f"⚠️ SLOW TRENDING ANALYSIS: {trending_analysis_time:.1f}ms for {len(new_cas)} CAs")

                # Forward to destinations based on trending analysis
                forwarding_start = time.perf_counter()
                await self._forward_cas_to_destinations(new_cas, trending_cas, message_text, source_group, chat_id)
                forwarding_time = (time.perf_counter() - forwarding_start) * 1000

                # Handle rescue forwarding for low-volume groups
                if rescue_cas:
                    logger.info(f"🚀 RESCUE FORWARDING: {len(rescue_cas)} CAs from {source_group}")
                    rescue_forwarding_start = time.perf_counter()
                    await self._forward_rescued_cas(rescue_cas, message_text, source_group, chat_id)
                    rescue_forwarding_time = (time.perf_counter() - rescue_forwarding_start) * 1000
                    logger.info(f"🚀 RESCUE FORWARDING COMPLETED: {rescue_forwarding_time:.1f}ms")

                # Log slow forwarding
                if forwarding_time > 500:
                    logger.warning(f"⚠️ SLOW FORWARDING: {forwarding_time:.1f}ms for {len(trending_cas)} CAs")

        except Exception as e:
            logger.error(f"Error processing message for CAs: {e}")
        finally:
            # Total pipeline performance monitoring
            total_pipeline_time = (time.perf_counter() - pipeline_start) * 1000
            enhanced_stats.record_performance_timing('pipeline_total', total_pipeline_time)

            if total_pipeline_time > 1000:  # Log slow pipeline
                logger.warning(f"⚠️ SLOW PIPELINE: {total_pipeline_time:.1f}ms for {source_group} | MSG={message_id}")
            elif total_pipeline_time > 500:
                logger.info(f"📊 PIPELINE TIMING: {total_pipeline_time:.1f}ms for {source_group}")

    async def _forward_cas_to_destinations(self, all_cas: list, trending_cas: list, message_text: str, source_group: str, chat_id: int):
        """Forward CAs to destinations in parallel with optimized performance and timeout protection."""
        forwarding_start_time = time.perf_counter()

        try:
            # Prepare forwarding tasks for parallel execution
            forwarding_tasks = []

            # BonkBot forwarding task
            if self.bonkbot_integration:
                cas_to_send = [ca for ca in all_cas if self.trending_analyzer.should_forward_to_destination(ca, 'BONKBOT', chat_id)]
                if cas_to_send:
                    forwarding_tasks.append(
                        self._forward_to_bonkbot_parallel(cas_to_send, message_text, chat_id)
                    )

            # CLA v2.0 forwarding task
            if self.cla_v2_integration:
                cas_to_send = [ca for ca in all_cas if self.trending_analyzer.should_forward_to_destination(ca, 'CLA_V2', chat_id)]
                if cas_to_send:
                    forwarding_tasks.append(
                        self._forward_to_cla_v2_parallel(cas_to_send, message_text, source_group, chat_id)
                    )

            # Monaco PNL forwarding task
            if self.monaco_pnl_integration:
                cas_to_send = [ca for ca in all_cas if self.trending_analyzer.should_forward_to_destination(ca, 'MONACO_PNL', chat_id)]
                if cas_to_send:
                    forwarding_tasks.append(
                        self._forward_to_monaco_pnl_parallel(cas_to_send, message_text, source_group, chat_id)
                    )

            # WINNERS forwarding task - TEMPORARILY DISABLED
            # TODO: Re-enable WINNERS forwarding when ready
            # if self.winners_integration:
            #     cas_to_send = [ca for ca in all_cas if self.trending_analyzer.should_forward_to_destination(ca, 'WINNERS', chat_id)]
            #     if cas_to_send:
            #         forwarding_tasks.append(
            #             self._forward_to_winners_parallel(cas_to_send, message_text, source_group, chat_id)
            #         )

            # Execute all forwarding tasks in parallel with timeout protection
            if forwarding_tasks:
                try:
                    # PERFORMANCE OPTIMIZATION: Add timeout to prevent hanging
                    results = await asyncio.wait_for(
                        asyncio.gather(*forwarding_tasks, return_exceptions=True),
                        timeout=10.0  # 10 second timeout for all parallel tasks
                    )
                except asyncio.TimeoutError:
                    logger.warning("⚠️ FORWARDING TIMEOUT: Parallel forwarding exceeded 10 seconds")
                    results = [Exception("Timeout") for _ in forwarding_tasks]

                # Process results and calculate totals
                bonkbot_sent = 0
                cla_v2_sent = 0
                monaco_pnl_sent = 0
                winners_sent = 0

                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Forwarding task {i} failed: {result}")
                    elif isinstance(result, dict):
                        bonkbot_sent += result.get('bonkbot', 0)
                        cla_v2_sent += result.get('cla_v2', 0)
                        monaco_pnl_sent += result.get('monaco_pnl', 0)
                        winners_sent += result.get('winners', 0)

                # Summary log with performance metrics (WINNERS temporarily disabled)
                total_sent = bonkbot_sent + cla_v2_sent + monaco_pnl_sent  # winners_sent excluded
                forwarding_time = (time.perf_counter() - forwarding_start_time) * 1000

                if total_sent > 0:
                    logger.info(f"📤 PARALLEL FORWARDING: BonkBot({bonkbot_sent}) | CLA v2.0({cla_v2_sent}) | Monaco PNL({monaco_pnl_sent}) | WINNERS(disabled)")

                    # PERFORMANCE MONITORING: Log slow forwarding
                    if forwarding_time > 100:  # Warn if over 100ms
                        logger.warning(f"⚠️ SLOW FORWARDING: {forwarding_time:.1f}ms for {total_sent} CAs")
                    else:
                        logger.debug(f"⚡ FAST FORWARDING: {forwarding_time:.1f}ms for {total_sent} CAs")
            else:
                logger.debug("No CAs to forward to any destination")

        except Exception as e:
            forwarding_time = (time.perf_counter() - forwarding_start_time) * 1000
            logger.error(f"Error in parallel forwarding ({forwarding_time:.1f}ms): {e}")

    async def _forward_to_bonkbot_parallel(self, cas_to_send: list, message_text: str, chat_id: int) -> dict:
        """Forward CAs to BonkBot in parallel task with timeout protection."""
        try:
            # PERFORMANCE OPTIMIZATION: Add timeout for individual integration
            bonkbot_sent = await asyncio.wait_for(
                self.bonkbot_integration.send_multiple_cas(cas_to_send, message_text),
                timeout=5.0  # 5 second timeout per integration
            )
            self.stats['cas_sent_to_bonkbot'] += bonkbot_sent

            if bonkbot_sent > 0:
                logger.info(f"✅ BonkBot: {bonkbot_sent} CAs sent")
                enhanced_stats.record_ca_forwarded(chat_id, "BonkBot", bonkbot_sent)

            return {'bonkbot': bonkbot_sent}

        except asyncio.TimeoutError:
            logger.warning(f"⚠️ BonkBot forwarding timeout for {len(cas_to_send)} CAs")
            return {'bonkbot': 0}
        except Exception as e:
            logger.error(f"Error forwarding to BonkBot: {e}")
            return {'bonkbot': 0}

    async def _forward_to_cla_v2_parallel(self, cas_to_send: list, message_text: str, source_group: str, chat_id: int) -> dict:
        """Forward CAs to CLA v2.0 in parallel task with timeout protection."""
        try:
            # PERFORMANCE OPTIMIZATION: Add timeout for individual integration
            cla_v2_sent = await asyncio.wait_for(
                self.cla_v2_integration.send_multiple_cas(cas_to_send, message_text, source_group),
                timeout=5.0  # 5 second timeout per integration
            )
            self.stats['cas_sent_to_cla_v2'] += cla_v2_sent

            if cla_v2_sent > 0:
                logger.info(f"✅ CLA v2.0: {cla_v2_sent} CAs sent")
                enhanced_stats.record_ca_forwarded(chat_id, "CLA v2.0", cla_v2_sent)

            return {'cla_v2': cla_v2_sent}

        except asyncio.TimeoutError:
            logger.warning(f"⚠️ CLA v2.0 forwarding timeout for {len(cas_to_send)} CAs")
            return {'cla_v2': 0}
        except Exception as e:
            logger.error(f"Error forwarding to CLA v2.0: {e}")
            return {'cla_v2': 0}

    async def _forward_to_monaco_pnl_parallel(self, cas_to_send: list, message_text: str, source_group: str, chat_id: int) -> dict:
        """Forward CAs to Monaco PNL in parallel task with timeout protection."""
        try:
            # PERFORMANCE OPTIMIZATION: Add timeout for individual integration
            monaco_pnl_sent = await asyncio.wait_for(
                self.monaco_pnl_integration.send_multiple_cas(cas_to_send, message_text, source_group),
                timeout=5.0  # 5 second timeout per integration
            )
            self.stats['cas_sent_to_monaco_pnl'] += monaco_pnl_sent

            if monaco_pnl_sent > 0:
                logger.info(f"✅ Monaco PNL: {monaco_pnl_sent} CAs sent (Admin Identity)")
                enhanced_stats.record_ca_forwarded(chat_id, "Monaco PNL", monaco_pnl_sent)

            return {'monaco_pnl': monaco_pnl_sent}

        except asyncio.TimeoutError:
            logger.warning(f"⚠️ Monaco PNL forwarding timeout for {len(cas_to_send)} CAs")
            return {'monaco_pnl': 0}
        except Exception as e:
            logger.error(f"Error forwarding to Monaco PNL: {e}")
            return {'monaco_pnl': 0}

    async def _forward_to_winners_parallel(self, cas_to_send: list, message_text: str, source_group: str, chat_id: int) -> dict:
        """Forward CAs to WINNERS in parallel task."""
        try:
            winners_sent = await self.winners_integration.send_multiple_cas(cas_to_send, message_text, source_group)
            self.stats['cas_sent_to_winners'] = self.stats.get('cas_sent_to_winners', 0) + winners_sent

            if winners_sent > 0:
                logger.info(f"✅ WINNERS: {winners_sent} CAs sent")
                enhanced_stats.record_ca_forwarded(chat_id, "WINNERS", winners_sent)

            return {'winners': winners_sent}

        except Exception as e:
            logger.error(f"Error forwarding to WINNERS: {e}")
            return {'winners': 0}

    async def _rescue_to_bonkbot_parallel(self, validated_cas: list, message_text: str, chat_id: int) -> int:
        """Rescue forward CAs to BonkBot in parallel task."""
        try:
            success = await self.bonkbot_integration.send_multiple_cas(validated_cas, message_text)
            if success:
                logger.info(f"🚀 RESCUE → BonkBot: {len(validated_cas)} CAs sent")
                enhanced_stats.record_ca_forwarded(chat_id, "BonkBot", len(validated_cas))
                return len(validated_cas)
            return 0
        except Exception as e:
            logger.error(f"Error in rescue forwarding to BonkBot: {e}")
            return 0

    async def _rescue_to_cla_v2_parallel(self, validated_cas: list, message_text: str, source_group: str, chat_id: int) -> int:
        """Rescue forward CAs to CLA v2.0 in parallel task."""
        try:
            success = await self.cla_v2_integration.send_multiple_cas(validated_cas, message_text, source_group)
            if success:
                logger.info(f"🚀 RESCUE → CLA v2.0: {len(validated_cas)} CAs sent")
                enhanced_stats.record_ca_forwarded(chat_id, "CLA v2.0", len(validated_cas))
                return len(validated_cas)
            return 0
        except Exception as e:
            logger.error(f"Error in rescue forwarding to CLA v2.0: {e}")
            return 0

    async def _rescue_to_monaco_pnl_parallel(self, validated_cas: list, message_text: str, source_group: str, chat_id: int) -> int:
        """Rescue forward CAs to Monaco PNL in parallel task."""
        try:
            success = await self.monaco_pnl_integration.send_multiple_cas(validated_cas, message_text, source_group)
            if success:
                logger.info(f"🚀 RESCUE → Monaco PNL: {len(validated_cas)} CAs sent (Admin Identity)")
                enhanced_stats.record_ca_forwarded(chat_id, "Monaco PNL", len(validated_cas))
                return len(validated_cas)
            return 0
        except Exception as e:
            logger.error(f"Error in rescue forwarding to Monaco PNL: {e}")
            return 0

    def _get_group_name(self, chat_id: int) -> str:
        """Get group name from chat ID."""
        if chat_id == config.target_group.group_id:
            return config.target_group.group_name

        for i, group_id in enumerate(config.target_group.additional_group_ids):
            if chat_id == group_id:
                if i < len(config.target_group.additional_group_names):
                    return config.target_group.additional_group_names[i]
                return f"Group {group_id}"

        return f"Unknown Group {chat_id}"
    

    
    async def _log_periodic_stats(self):
        """Log periodic statistics."""
        try:
            uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
            uptime_str = str(uptime).split('.')[0] if uptime else "Unknown"
            
            logger.info(f"📊 Bot Statistics - Uptime: {uptime_str}")
            logger.info(f"   Messages processed: {self.stats['messages_processed']}")
            logger.info(f"   CAs detected: {self.stats['cas_detected']}")
            logger.info(f"   CAs sent to BonkBot: {self.stats['cas_sent_to_bonkbot']}")
            logger.info(f"   CAs sent to WINNERS: {self.stats['cas_sent_to_winners']}")
            logger.info(f"   CAs sent to CLA v2.0: {self.stats['cas_sent_to_cla_v2']}")
            logger.info(f"   CAs sent to Monaco PNL: {self.stats['cas_sent_to_monaco_pnl']}")


            # GMGN and trending statistics
            if self.stats['gmgn_mentions'] > 0:
                logger.info(f"🧠 GMGN Activity:")
                logger.info(f"   GMGN mentions: {self.stats['gmgn_mentions']}")
                logger.info(f"   Trending qualified: {self.stats['trending_qualified']}")
                logger.info(f"   Noise filtered: {self.stats['noise_filtered']}")

                # Get trending analyzer stats
                trending_stats = self.trending_analyzer.get_trending_stats()
                logger.info(f"   Mentions/min: {trending_stats['mentions_per_minute']}")
                logger.info(f"   Trending rate: {trending_stats['trending_rate_percentage']}%")
            
            # Get database stats
            db_stats = await self.db_manager.get_stats()
            logger.info(f"   Total CAs in DB: {db_stats.get('total_cas', 0)}")
            logger.info(f"   CAs today: {db_stats.get('cas_today', 0)}")
            
        except Exception as e:
            logger.error(f"Error logging periodic stats: {e}")
    
    async def _log_final_stats(self):
        """Log final statistics when bot stops."""
        try:
            uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
            uptime_str = str(uptime).split('.')[0] if uptime else "Unknown"
            
            logger.info("🏁 Final Bot Statistics:")
            logger.info(f"   Total uptime: {uptime_str}")
            logger.info(f"   Messages processed: {self.stats['messages_processed']}")
            logger.info(f"   CAs detected: {self.stats['cas_detected']}")
            logger.info(f"   CAs sent to BonkBot: {self.stats['cas_sent_to_bonkbot']}")
            logger.info(f"   CAs sent to WINNERS: {self.stats['cas_sent_to_winners']}")
            logger.info(f"   CAs sent to CLA v2.0: {self.stats['cas_sent_to_cla_v2']}")
            logger.info(f"   CAs sent to Monaco PNL: {self.stats['cas_sent_to_monaco_pnl']}")

            logger.info(f"   GMGN mentions: {self.stats['gmgn_mentions']}")
            logger.info(f"   Trending qualified: {self.stats['trending_qualified']}")
            logger.info(f"   Noise filtered: {self.stats['noise_filtered']}")
            
        except Exception as e:
            logger.error(f"Error logging final stats: {e}")
    
    async def get_status(self) -> dict:
        """Get current bot status."""
        try:
            uptime = datetime.now() - self.stats['start_time'] if self.stats['start_time'] else None
            
            status = {
                'running': self.running,
                'uptime_seconds': uptime.total_seconds() if uptime else 0,
                'telegram_connected': self.telegram_client.is_connected(),
                'bonkbot_available': self.bonkbot_integration is not None,
                'winners_available': self.winners_integration is not None,
                'winners_disabled': False,  # Reactivated
                'cla_v2_available': self.cla_v2_integration is not None,
                'monaco_pnl_available': self.monaco_pnl_integration is not None,
                'active_groups': len(config.target_group.active_group_ids),
                'total_groups': len(config.target_group.all_group_ids),
                'cache_period_hours': config.cache.ca_expiry_hours,
                'stats': self.stats.copy()
            }

            if self.bonkbot_integration:
                status['bonkbot_queue'] = await self.bonkbot_integration.get_queue_status()

            if self.winners_integration:
                status['winners_queue'] = await self.winners_integration.get_queue_status()

            if self.cla_v2_integration:
                status['cla_v2_queue'] = await self.cla_v2_integration.get_queue_status()

            if self.monaco_pnl_integration:
                status['monaco_pnl_queue'] = await self.monaco_pnl_integration.get_queue_status()
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting bot status: {e}")
            return {'error': str(e)}
    
    async def force_cleanup(self):
        """Force cleanup of caches and old data."""
        try:
            logger.info("Performing force cleanup...")

            # Cleanup CA detector cache
            await self.ca_detector.force_cleanup()



            logger.info("Force cleanup completed")

        except Exception as e:
            logger.error(f"Error during force cleanup: {e}")

    async def _forward_rescued_cas(self, rescue_cas: List[str], message_text: str,
                                 source_group: str, chat_id: int):
        """Forward rescued CAs to all active destinations with final validation."""
        try:
            if not rescue_cas:
                return

            logger.info(f"🚀 RESCUE FORWARDING: Processing {len(rescue_cas)} rescued CAs from {source_group}")

            # FINAL SAFETY CHECK: Validate each CA hasn't been processed since rescue decision
            validated_cas = []
            for ca in rescue_cas:
                # Check one more time against the CA detector cache
                if not await self.ca_detector.is_ca_in_cache(ca):
                    validated_cas.append(ca)
                    logger.info(f"✅ RESCUE VALIDATED: {ca} cleared for forwarding")
                else:
                    logger.warning(f"🚫 RESCUE BLOCKED: {ca} found in cache during final validation")

            if not validated_cas:
                logger.warning(f"🚫 RESCUE FORWARDING ABORTED: No CAs passed final validation")
                return

            logger.info(f"🚀 RESCUE FORWARDING: {len(validated_cas)}/{len(rescue_cas)} CAs validated for forwarding")

            # Forward to all active destinations in parallel
            rescue_tasks = []

            # BonkBot rescue task
            if self.bonkbot_integration:
                rescue_tasks.append(
                    self._rescue_to_bonkbot_parallel(validated_cas, message_text, chat_id)
                )

            # CLA v2.0 rescue task
            if self.cla_v2_integration:
                rescue_tasks.append(
                    self._rescue_to_cla_v2_parallel(validated_cas, message_text, source_group, chat_id)
                )

            # Monaco PNL rescue task
            if self.monaco_pnl_integration:
                rescue_tasks.append(
                    self._rescue_to_monaco_pnl_parallel(validated_cas, message_text, source_group, chat_id)
                )

            # Execute all rescue tasks in parallel
            forwarded_count = 0
            if rescue_tasks:
                results = await asyncio.gather(*rescue_tasks, return_exceptions=True)

                for i, result in enumerate(results):
                    if isinstance(result, Exception):
                        logger.error(f"Rescue task {i} failed: {result}")
                    elif isinstance(result, int):
                        forwarded_count += result

            # Mark rescued CAs as processed to prevent future duplicates
            for ca in validated_cas:
                await self.ca_detector.db_manager.mark_ca_processed(ca)
                self.ca_detector.processed_cache.add(ca)
                logger.debug(f"🔒 RESCUE PROCESSED: {ca} marked as processed")

            # Update statistics
            self.stats['cas_forwarded'] += len(validated_cas)

            logger.info(f"🚀 RESCUE FORWARDING SUMMARY: {len(validated_cas)} CAs → {forwarded_count} total sends")

        except Exception as e:
            logger.error(f"Error in rescue forwarding: {e}")

    async def _send_hourly_status_report(self):
        """Send comprehensive hourly status report to CLA v2.0 channel."""
        try:
            report = enhanced_stats.generate_hourly_report()

            # Log concise summary instead of full report to reduce verbosity
            summary = enhanced_stats.generate_concise_summary()
            logger.info(f"📊 HOURLY STATUS: {summary}")

            # Only log full report if there are issues
            if enhanced_stats.should_log_detailed_report():
                logger.info("📊 DETAILED STATUS REPORT (issues detected):")
                for line in report.split('\n'):
                    if line.strip():
                        logger.info(f"   {line}")

            # Save stats to file for persistence
            enhanced_stats.save_stats_to_file('data/hourly_stats.json')

            # Send status report to CLA v2.0 channel ONLY
            if self.cla_v2_integration:
                try:
                    # Format report for Telegram (split if too long)
                    report_chunks = self._split_report_for_telegram(report)

                    for chunk in report_chunks:
                        success = await self.cla_v2_integration.send_status_report(chunk)
                        if success:
                            logger.info("✅ Hourly status report sent to CLA v2.0 channel")
                        else:
                            logger.warning("⚠️ Failed to send status report to CLA v2.0 channel")

                        # Small delay between chunks
                        await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"Error sending status report to CLA v2.0 channel: {e}")
            else:
                logger.warning("CLA v2.0 integration not available for status reports")

        except Exception as e:
            logger.error(f"Error generating hourly status report: {e}")
            enhanced_stats.record_error()

    def _split_report_for_telegram(self, report: str) -> list:
        """Split long reports into Telegram-compatible chunks."""
        max_length = 4000  # Telegram message limit with some buffer
        chunks = []

        if len(report) <= max_length:
            return [report]

        lines = report.split('\n')
        current_chunk = ""

        for line in lines:
            if len(current_chunk + line + '\n') > max_length:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = line + '\n'
                else:
                    # Single line too long, truncate
                    chunks.append(line[:max_length-3] + "...")
            else:
                current_chunk += line + '\n'

        if current_chunk:
            chunks.append(current_chunk.strip())

        return chunks
    
    def is_running(self) -> bool:
        """Check if bot is running."""
        return self.running
