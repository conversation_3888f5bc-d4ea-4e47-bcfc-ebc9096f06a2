"""WINNERS group integration for CLA v2.0 Bot."""

import asyncio
from typing import Optional, List
from datetime import datetime
from loguru import logger

from src.telegram_client import TelegramClientManager
from config import config

class WinnersIntegration:
    """Handles integration with WINNERS group for CA forwarding."""
    
    def __init__(self, telegram_client: TelegramClientManager):
        self.telegram_client = telegram_client
        self.winners_entity = None
        self.message_queue = asyncio.Queue()
        self.processing_queue = False
    
    async def initialize(self):
        """Initialize WINNERS group integration."""
        try:
            # Get WINNERS group entity
            self.winners_entity = await self.telegram_client.get_entity(config.winners_group.group_id)
            
            if not self.winners_entity:
                logger.error(f"Failed to find WINNERS group: {config.winners_group.group_id}")
                return False
            
            logger.info(f"WINNERS integration initialized: {config.winners_group.group_name} ({config.winners_group.group_id})")
            
            # Start message queue processor
            asyncio.create_task(self._process_message_queue())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize WINNERS integration: {e}")
            return False
    
    async def send_ca_to_winners(self, ca: str, source_message: str = "", source_group: str = "") -> bool:
        """Send contract address to WINNERS group."""
        try:
            # Format message for WINNERS group
            formatted_message = self._format_ca_message(ca, source_message, source_group)
            
            # Add to queue for processing
            await self.message_queue.put({
                'ca': ca,
                'message': formatted_message,
                'timestamp': datetime.now(),
                'source': source_message[:100] if source_message else "",
                'source_group': source_group
            })
            
            logger.info(f"CA queued for WINNERS group: {ca}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue CA for WINNERS group: {e}")
            return False
    
    def _format_ca_message(self, ca: str, source_message: str = "", source_group: str = "") -> str:
        """Format contract address message for WINNERS group - clean format."""
        # Clean format: just the CA
        return ca
    
    async def _process_message_queue(self):
        """Process queued messages to WINNERS group."""
        self.processing_queue = True
        logger.info("Started WINNERS group message queue processor")
        
        while self.processing_queue:
            try:
                # Get message from queue (wait up to 1 second)
                try:
                    message_data = await asyncio.wait_for(
                        self.message_queue.get(), 
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Send message to WINNERS group
                success = await self._send_message_to_winners(message_data)
                
                if success:
                    logger.info(f"Successfully sent CA to WINNERS group: {message_data['ca']}")
                else:
                    logger.error(f"Failed to send CA to WINNERS group: {message_data['ca']}")
                
                # Rate limiting - wait between messages (same as BonkBot)
                await asyncio.sleep(2)  # 2 second delay between messages
                
            except Exception as e:
                logger.error(f"Error in WINNERS message queue processor: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _send_message_to_winners(self, message_data: dict) -> bool:
        """Send individual message to WINNERS group."""
        try:
            if not self.winners_entity:
                logger.error("WINNERS group entity not initialized")
                return False
            
            # Send the message
            success = await self.telegram_client.send_message(
                self.winners_entity, 
                message_data['message']
            )
            
            if success:
                logger.debug(f"Message sent to WINNERS group: {message_data['message']}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to WINNERS group: {e}")
            return False
    
    async def send_multiple_cas(self, cas: List[str], source_message: str = "", source_group: str = "") -> int:
        """Send multiple contract addresses to WINNERS group."""
        success_count = 0
        
        for ca in cas:
            if await self.send_ca_to_winners(ca, source_message, source_group):
                success_count += 1
            
            # Small delay between queueing multiple CAs
            await asyncio.sleep(0.5)
        
        logger.info(f"Queued {success_count}/{len(cas)} CAs for WINNERS group")
        return success_count
    
    async def test_winners_connection(self) -> bool:
        """Test connection to WINNERS group."""
        try:
            if not self.winners_entity:
                logger.error("WINNERS group entity not initialized")
                return False
            
            # We can't send a test message to a group without permission
            # Just verify we can access the entity
            logger.info("WINNERS group connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"WINNERS group connection test error: {e}")
            return False
    
    async def get_queue_status(self) -> dict:
        """Get status of the message queue."""
        return {
            'queue_size': self.message_queue.qsize(),
            'processing': self.processing_queue,
            'winners_connected': self.winners_entity is not None
        }
    
    async def clear_queue(self):
        """Clear the message queue."""
        try:
            while not self.message_queue.empty():
                await self.message_queue.get()
            logger.info("WINNERS group message queue cleared")
        except Exception as e:
            logger.error(f"Error clearing WINNERS queue: {e}")
    
    async def pause_processing(self):
        """Pause message queue processing."""
        self.processing_queue = False
        logger.info("WINNERS group message processing paused")
    
    async def resume_processing(self):
        """Resume message queue processing."""
        if not self.processing_queue:
            self.processing_queue = True
            asyncio.create_task(self._process_message_queue())
            logger.info("WINNERS group message processing resumed")
    
    async def send_custom_message(self, message: str) -> bool:
        """Send a custom message to WINNERS group."""
        try:
            if not self.winners_entity:
                logger.error("WINNERS group entity not initialized")
                return False
            
            success = await self.telegram_client.send_message(
                self.winners_entity, 
                message
            )
            
            if success:
                logger.info(f"Custom message sent to WINNERS group: {message}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending custom message to WINNERS group: {e}")
            return False
    
    def get_winners_info(self) -> dict:
        """Get WINNERS group configuration info."""
        return {
            'group_id': config.winners_group.group_id,
            'group_name': config.winners_group.group_name,
            'entity_found': self.winners_entity is not None,
            'queue_processing': self.processing_queue
        }
    
    async def shutdown(self):
        """Shutdown WINNERS group integration."""
        logger.info("Shutting down WINNERS group integration")
        self.processing_queue = False
        await self.clear_queue()
        logger.info("WINNERS group integration shutdown complete")
