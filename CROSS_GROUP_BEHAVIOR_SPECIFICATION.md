# Cross-Group Duplicate Prevention - Official Specification

## 🎯 **DESIGN PHILOSOPHY: Quality Over Coverage**

The CLA v2.0 bot implements **strict global duplicate prevention** as a core design principle to ensure **signal quality over signal coverage**. This specification documents the intended behavior for cross-group scenarios.

---

## **📊 OFFICIAL BEHAVIOR SPECIFICATION**

### **🔒 Global Duplicate Prevention (7-Day Cache)**

**Core Principle:** Each contract address is processed **exactly once** across all monitored groups within a 7-day period.

**Implementation Level:** CA Detector (`ca_detector.py`)
**Scope:** Global across all 7 monitored groups
**Cache Duration:** 168 hours (7 days)
**Race Protection:** Async locks prevent concurrent processing

---

## **🎯 CROSS-GROUP SCENARIOS (OFFICIAL BEHAVIOR)**

### **Scenario 1: High-Volume Group First, Low-Volume Group Second**

```
Time 10:00 - MEME 1000X (High-Volume): "ContractAddress123"
    ↓
✅ CA Detector: Returns [CA] (new)
    ↓
📊 Trending Analysis: 1 mention → Not trending
    ↓
❌ Forwarding Decision: False (requires 6 mentions)
    ↓
🔒 Result: NOT FORWARDED + Added to global cache
    ↓
Time 10:05 - FREE WHALE SIGNALS (Low-Volume): "ContractAddress123"
    ↓
❌ CA Detector: Returns [] (DUPLICATE BLOCKED)
    ↓
🚫 Result: NOT PROCESSED (duplicate prevention)
```

**Outcome:** CA is permanently filtered (no rescue mechanism)

### **Scenario 2: Low-Volume Group First, High-Volume Group Second**

```
Time 10:00 - FREE WHALE SIGNALS (Low-Volume): "ContractAddress123"
    ↓
✅ CA Detector: Returns [CA] (new)
    ↓
⚡ Trending Analysis: Always trending (direct forwarding)
    ↓
✅ Forwarding Decision: True (low-volume direct forwarding)
    ↓
📤 Result: FORWARDED + Added to global cache
    ↓
Time 10:05 - MEME 1000X (High-Volume): "ContractAddress123"
    ↓
❌ CA Detector: Returns [] (DUPLICATE BLOCKED)
    ↓
🚫 Result: NOT PROCESSED (already forwarded)
```

**Outcome:** CA forwarded by first group only

---

## **🎯 DESIGN RATIONALE**

### **✅ Why Strict Duplicate Prevention is Preferred**

**1. Signal Quality Assurance:**
- Prevents noise and manipulation schemes
- Ensures high-confidence signals for auto-trading systems
- Maintains institutional-grade signal quality

**2. Anti-Pump Protection:**
- Blocks coordinated pump-and-dump schemes across groups
- Prevents artificial signal amplification
- Maintains organic signal detection

**3. System Reliability:**
- Simple, predictable behavior
- No complex edge cases or race conditions
- Proven operational stability

**4. Business Alignment:**
- Quality-first trading strategy
- Premium destination focus (BonkBot auto-trading)
- WINNERS globally disabled (quality over quantity)

### **❌ Why Rescue Mechanism is NOT Implemented**

**1. Business Case:**
- No evidence of missed opportunities
- Current system achieving desired signal quality
- Auto-trading systems require high-confidence signals

**2. Technical Risk:**
- High implementation complexity
- Potential for new race conditions
- Risk of undermining anti-pump protection

**3. Operational Impact:**
- Additional maintenance overhead
- Complex testing requirements
- Potential for regression issues

---

## **🔍 MONITORING & VERIFICATION**

### **Log Patterns for Cross-Group Behavior:**

**Duplicate Detection (Expected):**
```
🧠 GMGN DUPLICATE: {CA} already in cache
⚡ LOW-VOLUME DUPLICATE: {CA} already processed
```

**Successful Processing (First Occurrence):**
```
🔥 HIGH-VOLUME CA DETECTION: Found 1 CAs: [CA]
⚡ LOW-VOLUME CA DETECTION: Found 1 CAs: [CA]
```

**Forwarding Decisions:**
```
🔥 HIGH-VOLUME NOISE FILTERED: {CA} (count: 1)
⚡ LOW-VOLUME RESULT: CA={CA} | Direct forwarding=True
```

### **Key Metrics to Monitor:**

- **Duplicate Prevention Rate:** Should be >99%
- **Signal Quality:** High-confidence signals only
- **Anti-Pump Effectiveness:** 95%+ pump scheme blocking
- **System Reliability:** No race conditions or edge cases

---

## **🎯 OPERATIONAL GUIDELINES**

### **Expected Behavior:**

**✅ Normal Operations:**
- Each CA processed exactly once
- High-volume groups filter noise effectively
- Low-volume groups provide immediate forwarding
- No duplicate signals across destinations

**✅ Cross-Group Scenarios:**
- First group to process CA determines outcome
- Subsequent groups see duplicate and skip processing
- No rescue mechanism or confidence boost behavior
- Consistent 7-day cache across all groups

### **Troubleshooting:**

**If CAs appear to be "lost":**
1. Verify first group processed CA correctly
2. Check if CA failed trending threshold (expected)
3. Confirm duplicate prevention working (expected)
4. Review if signal quality requirements are appropriate

**If duplicates appear:**
1. Check for race conditions (should be prevented)
2. Verify cache functionality
3. Review async lock implementation

---

## **🚀 FUTURE CONSIDERATIONS**

### **Potential Enhancements (Low Priority):**

**Signal Coverage Analysis:**
- Monitor if legitimate signals are being filtered
- Analyze missed opportunities vs noise reduction
- Consider adjusting trending thresholds if needed

**Group Classification Review:**
- Evaluate if current high/low-volume classifications are optimal
- Consider adding medium-volume category if needed
- Review group performance and signal quality

**Alternative Approaches (If Needed):**
- Time-based reprocessing (e.g., allow after 24 hours)
- Confidence scoring system instead of binary trending
- Manual override mechanism for exceptional cases

---

## **📋 CONCLUSION**

**The current cross-group duplicate prevention behavior is the INTENDED DESIGN for the CLA v2.0 bot system.**

**Key Principles:**
- ✅ Quality over coverage
- ✅ Strict duplicate prevention
- ✅ Anti-pump protection priority
- ✅ System reliability and simplicity

**This specification serves as the official documentation for cross-group behavior and should be referenced for any future modifications or troubleshooting.**

---

**Document Version:** 1.0  
**Last Updated:** 2025-07-26  
**Status:** OFFICIAL SPECIFICATION  
**Review Date:** 2025-10-26 (Quarterly Review)
