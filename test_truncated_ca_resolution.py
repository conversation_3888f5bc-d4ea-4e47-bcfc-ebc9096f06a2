#!/usr/bin/env python3
"""
Test Truncated CA Resolution
Tests the fix for BUGSIE's truncated CA messages based on live log analysis
"""

import sys
sys.path.append('.')

def test_truncated_ca_resolution():
    """Test the truncated CA resolution based on actual BUGSIE behavior."""
    print("🧪 TESTING TRUNCATED CA RESOLUTION")
    print("=" * 60)
    print("Based on live log analysis showing BUGSIE sends both complete and truncated CAs")
    print()
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test scenario 1: First message with complete CA (should be cached)
        complete_message = """🔥 **ACTIVITY DETECTED** 🔥

├ **$SCHIZOCOIN**
├ `GrpfnabLYhwTN2teEBaBw1PwKS813oEKDJkb12cnbonk`"""
        
        print("🧪 TEST 1: COMPLETE CA MESSAGE (First occurrence)")
        print("-" * 50)
        print(f"Message: {complete_message[:60]}...")
        
        # Extract CAs from complete message
        complete_cas = parser.extract_contract_addresses(complete_message)
        print(f"Extracted CAs: {complete_cas}")
        
        expected_complete_ca = "GrpfnabLYhwTN2teEBaBw1PwKS813oEKDJkb12cnbonk"
        if expected_complete_ca in complete_cas:
            print("✅ Complete CA extracted and cached successfully")
            complete_success = True
        else:
            print("❌ Complete CA extraction failed")
            complete_success = False
        
        print()
        
        # Test scenario 2: Later message with truncated CA (should be resolved from cache)
        truncated_message = """🔥 **ACTIVITY DETECTED** 🔥

├ **$SCHIZOCOIN**
├ `GrpfnabLYhwTN2teEBaBw1PwKS813oEK...`"""
        
        print("🧪 TEST 2: TRUNCATED CA MESSAGE (Later occurrence)")
        print("-" * 50)
        print(f"Message: {truncated_message[:60]}...")
        
        # Extract CAs from truncated message
        truncated_cas = parser.extract_contract_addresses(truncated_message)
        print(f"Extracted CAs: {truncated_cas}")
        
        if expected_complete_ca in truncated_cas:
            print("✅ Truncated CA resolved to complete CA successfully")
            truncated_success = True
        else:
            print("❌ Truncated CA resolution failed")
            truncated_success = False
        
        print()
        
        # Test scenario 3: Different truncated CA patterns
        test_cases = [
            {
                "name": "BUGSIE NIMBUS (from logs)",
                "message": """🔥 **ACTIVITY DETECTED** 🔥

├ **$NIMBUS**
├ `Di6neRG1oYd4fks39tfbAA8ffYgRpta5K4vr...`""",
                "expected_behavior": "Should log truncation warning and attempt resolution"
            },
            {
                "name": "Known CA pattern",
                "message": """🔥 **ACTIVITY DETECTED** 🔥

├ **$TEST**
├ `DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjem...`""",
                "expected_ca": "DUd7AMKTQLXe6WwPHpMUBooz6AP5eQrnjAgbfjembonk",
                "expected_behavior": "Should resolve to known CA"
            }
        ]
        
        print("🧪 TEST 3: VARIOUS TRUNCATED CA PATTERNS")
        print("-" * 50)
        
        pattern_success = 0
        total_patterns = len(test_cases)
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            message = test_case["message"]
            expected_behavior = test_case["expected_behavior"]
            
            print(f"Pattern {i}: {name}")
            print(f"Expected: {expected_behavior}")
            
            # Extract CAs
            pattern_cas = parser.extract_contract_addresses(message)
            print(f"Result: {pattern_cas}")
            
            # Check if we got any CAs (success depends on whether we have the complete CA cached)
            if "expected_ca" in test_case:
                expected_ca = test_case["expected_ca"]
                if expected_ca in pattern_cas:
                    print("✅ Pattern resolved successfully")
                    pattern_success += 1
                else:
                    print("⚠️ Pattern not resolved (may need complete CA in cache first)")
            else:
                print("ℹ️ Pattern processed (check logs for truncation warnings)")
                pattern_success += 1  # Count as success if no exception
            
            print()
        
        print("🎯 TEST SUMMARY")
        print("=" * 60)
        
        tests = [
            ("Complete CA Extraction", complete_success),
            ("Truncated CA Resolution", truncated_success),
            ("Pattern Processing", pattern_success == total_patterns)
        ]
        
        passed = sum(1 for _, result in tests if result)
        total = len(tests)
        
        for test_name, result in tests:
            print(f"{test_name}: {'✅ PASS' if result else '❌ FAIL'}")
        
        print(f"\\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("\\n🎉 ALL TESTS PASSED!")
            print("✅ Truncated CA resolution implemented successfully")
            print("✅ BUGSIE CA extraction should now work consistently")
        else:
            print("\\n⚠️ SOME TESTS FAILED")
            print("Further investigation needed")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_activity_tracker_format_detection():
    """Test that BUGSIE messages are correctly identified as Activity Tracker format."""
    print("\\n🧪 TESTING ACTIVITY TRACKER FORMAT DETECTION")
    print("=" * 60)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test BUGSIE message format
        bugsie_message = """🔥 **ACTIVITY DETECTED** 🔥

├ **$SCHIZOCOIN**
├ `GrpfnabLYhwTN2teEBaBw1PwKS813oEKDJkb12cnbonk`
└| ⏳ 1h | 👁️ 490

📊 Token details
├ PRICE:    $0
├ MC:       $56.2K /2.4X from VIP/"""
        
        print("Testing BUGSIE Activity Tracker format detection:")
        print(f"Message: {bugsie_message[:100]}...")
        print()
        
        is_activity_format = parser._is_activity_tracker_format(bugsie_message)
        print(f"Activity Tracker format detected: {'✅ YES' if is_activity_format else '❌ NO'}")
        
        if is_activity_format:
            print("✅ BUGSIE messages correctly identified as Activity Tracker format")
            return True
        else:
            print("❌ BUGSIE messages not identified as Activity Tracker format")
            
            # Debug format detection
            indicators = [
                "🔥 ACTIVITY DETECTED 🔥",
                "📊 Token details",
                "🔒 Security",
                "├ PRICE:",
                "├ MC:",
                "from VIP"
            ]
            
            print("\\nChecking individual indicators:")
            for indicator in indicators:
                found = indicator.upper() in bugsie_message.upper()
                print(f"  '{indicator}': {'✅' if found else '❌'}")
            
            return False
        
    except Exception as e:
        print(f"❌ Format detection test failed: {e}")
        return False

def main():
    """Run all truncated CA resolution tests."""
    print("🚀 TRUNCATED CA RESOLUTION TEST SUITE")
    print("=" * 80)
    print("Testing the fix for BUGSIE's inconsistent CA truncation behavior")
    print("Based on live log analysis showing both complete and truncated CAs")
    print("=" * 80)
    
    # Run tests
    resolution_success = test_truncated_ca_resolution()
    format_success = test_activity_tracker_format_detection()
    
    print("\\n🎯 FINAL RESULTS")
    print("=" * 80)
    print(f"Truncated CA Resolution: {'✅ PASS' if resolution_success else '❌ FAIL'}")
    print(f"Activity Tracker Detection: {'✅ PASS' if format_success else '❌ FAIL'}")
    
    if resolution_success and format_success:
        print("\\n🎉 ALL TESTS PASSED!")
        print("✅ BUGSIE truncated CA issue should be resolved")
        print("✅ System can handle both complete and truncated CAs")
        print("✅ CA caching and resolution working correctly")
    else:
        print("\\n⚠️ ISSUES IDENTIFIED")
        print("Some components need further attention")
    
    print("\\n📋 DEPLOYMENT NOTES")
    print("-" * 30)
    print("1. The fix handles BUGSIE's inconsistent CA truncation")
    print("2. Complete CAs are cached for future truncated CA resolution")
    print("3. Truncated CAs are detected and resolved from cache")
    print("4. Enhanced logging shows truncation warnings and resolutions")
    print("5. System maintains high performance with caching")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
