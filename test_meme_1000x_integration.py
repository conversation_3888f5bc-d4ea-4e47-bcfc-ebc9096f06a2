"""Test MEME 1000X integration and verification."""

import asyncio
import sys
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

async def test_meme_1000x_configuration():
    """Test MEME 1000X configuration and classification."""
    print("🧪 Testing MEME 1000X Configuration...")
    
    try:
        from config import config
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        meme_1000x_id = -1002333406905
        
        print(f"\n📋 Group Configuration:")
        print(f"   MEME 1000X ID: {meme_1000x_id}")
        print(f"   High-Volume Groups: {list(analyzer.high_volume_groups)}")
        print(f"   Low-Volume Groups: {list(analyzer.low_volume_groups)}")
        
        # Test group classification
        is_high_volume = analyzer.is_high_volume_group(meme_1000x_id)
        is_low_volume = analyzer.is_low_volume_group(meme_1000x_id)
        requires_trending = analyzer.requires_trending_analysis(meme_1000x_id)
        
        print(f"\n🔍 MEME 1000X Classification:")
        print(f"   Is High-Volume: {is_high_volume}")
        print(f"   Is Low-Volume: {is_low_volume}")
        print(f"   Requires Trending: {requires_trending}")
        
        if not is_high_volume:
            print(f"   ❌ MEME 1000X should be classified as high-volume")
            return False
        
        if not requires_trending:
            print(f"   ❌ MEME 1000X should require trending analysis")
            return False
        
        print(f"   ✅ MEME 1000X correctly classified as high-volume with trending analysis")
        
        # Test forwarding logic
        test_ca = "MEME1000XTestCA123456789"
        
        # Non-trending CA should not be forwarded
        should_forward_non_trending = analyzer.should_forward_to_destination(test_ca, "BONKBOT", meme_1000x_id)
        print(f"   Non-trending CA forwarding: {should_forward_non_trending} (should be False)")
        
        if should_forward_non_trending:
            print(f"   ❌ Non-trending CA should not be forwarded from high-volume group")
            return False
        
        # Trending CA should be forwarded
        analyzer.trending_cas.add(test_ca)
        should_forward_trending = analyzer.should_forward_to_destination(test_ca, "BONKBOT", meme_1000x_id)
        print(f"   Trending CA forwarding: {should_forward_trending} (should be True)")
        
        if not should_forward_trending:
            print(f"   ❌ Trending CA should be forwarded from high-volume group")
            return False
        
        # WINNERS should be excluded
        should_forward_winners = analyzer.should_forward_to_destination(test_ca, "WINNERS", meme_1000x_id)
        print(f"   WINNERS exclusion: {should_forward_winners} (should be False)")
        
        if should_forward_winners:
            print(f"   ❌ WINNERS should be excluded for all groups")
            return False
        
        print(f"   ✅ MEME 1000X forwarding logic working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_meme_1000x_trending_analysis():
    """Test MEME 1000X trending analysis with anti-pump protection."""
    print(f"\n🧪 Testing MEME 1000X Trending Analysis...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        meme_1000x_id = -1002333406905
        test_ca = "MEME1000XTrendingTestCA123456789"
        
        print(f"   Testing trending analysis for MEME 1000X ({meme_1000x_id})")
        
        # Test 1: Single mention (should not trend)
        result1 = await analyzer.analyze_ca_mention(test_ca, meme_1000x_id, "MEME 1000X", 1001)
        print(f"   Single mention: Trending={result1.is_trending}, Count={result1.mention_count}")
        
        if result1.is_trending:
            print(f"   ❌ Single mention should not trend for high-volume groups")
            return False
        
        # Test 2: Multiple mentions (simulate organic growth over time)
        base_time = datetime.now()
        mention_times = [
            base_time,
            base_time + timedelta(minutes=1),
            base_time + timedelta(minutes=2.5),
            base_time + timedelta(minutes=4),
            base_time + timedelta(minutes=5.5),
            base_time + timedelta(minutes=7)
        ]
        
        # Add mentions with proper time distribution
        for i, timestamp in enumerate(mention_times):
            from src.trending_analyzer import CAMention
            mention = CAMention(
                ca=test_ca,
                timestamp=timestamp,
                group_id=meme_1000x_id,
                group_name="MEME 1000X",
                message_id=2000 + i
            )
            
            # Add to analyzer's tracking
            analyzer.ca_mentions[test_ca].append(mention)
            analyzer.stats['total_mentions'] += 1
            
            # Analyze trending status
            result = await analyzer._analyze_trending_status(test_ca)
            print(f"   Mention {i+1}: Trending={result.is_trending}, Count={result.mention_count}")
            
            if result.is_trending:
                print(f"   ✅ MEME 1000X CA achieved trending status with {result.mention_count} mentions")
                break
        
        if not result.is_trending:
            print(f"   ❌ MEME 1000X CA should achieve trending status with 6 organic mentions")
            return False
        
        print(f"   ✅ MEME 1000X trending analysis working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Trending analysis test failed: {e}")
        return False

async def test_global_duplicate_prevention():
    """Test global duplicate prevention across GMGN, MEME 1000X, and low-volume groups."""
    print(f"\n🧪 Testing Global Duplicate Prevention...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        # Test groups
        gmgn_id = -1002202241417
        meme_1000x_id = -1002333406905
        whale_signals_id = -1002380594298
        
        test_ca = "GlobalDuplicateTestCA123456789"
        
        print(f"   Testing duplicate prevention across multiple groups")
        
        # Test 1: GMGN processes CA first (high-volume, needs trending)
        result1 = await analyzer.analyze_ca_mention(test_ca, gmgn_id, "GMGN", 3001)
        print(f"   GMGN first mention: Trending={result1.is_trending}, Count={result1.mention_count}")
        
        # Test 2: MEME 1000X processes same CA (high-volume, should add to same tracking)
        result2 = await analyzer.analyze_ca_mention(test_ca, meme_1000x_id, "MEME 1000X", 3002)
        print(f"   MEME 1000X mention: Trending={result2.is_trending}, Count={result2.mention_count}")
        
        # Test 3: Low-volume group processes same CA (should be direct forwarding)
        result3 = await analyzer.analyze_ca_mention(test_ca, whale_signals_id, "FREE WHALE SIGNALS", 3003)
        print(f"   Low-volume mention: Trending={result3.is_trending}, Count={result3.mention_count}")
        
        # Verify mention tracking
        total_mentions = len(analyzer.ca_mentions.get(test_ca, []))
        print(f"   Total mentions tracked: {total_mentions}")
        
        # Test forwarding decisions
        gmgn_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", gmgn_id)
        meme_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", meme_1000x_id)
        whale_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", whale_signals_id)
        
        print(f"   GMGN forwarding: {gmgn_forward}")
        print(f"   MEME 1000X forwarding: {meme_forward}")
        print(f"   Whale Signals forwarding: {whale_forward}")
        
        # Low-volume should always forward, high-volume depends on trending
        if not whale_forward:
            print(f"   ❌ Low-volume group should always forward")
            return False
        
        print(f"   ✅ Global duplicate prevention and cross-group tracking working")
        return True
        
    except Exception as e:
        print(f"❌ Global duplicate prevention test failed: {e}")
        return False

async def test_anti_pump_protection_meme_1000x():
    """Test anti-pump protection specifically for MEME 1000X."""
    print(f"\n🧪 Testing Anti-Pump Protection for MEME 1000X...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer, CAMention
        
        analyzer = TrendingAnalyzer()
        meme_1000x_id = -1002333406905
        
        # Test pump scheme (rapid mentions)
        pump_ca = "MEME1000XPumpSchemeCA123456789"
        base_time = datetime.now()
        
        # Create 6 mentions within 30 seconds (pump pattern)
        pump_mentions = []
        for i in range(6):
            mention = CAMention(
                ca=pump_ca,
                timestamp=base_time + timedelta(seconds=i*5),  # 5 seconds apart
                group_id=meme_1000x_id,
                group_name="MEME 1000X",
                message_id=4000 + i
            )
            pump_mentions.append(mention)
        
        # Test pump detection
        is_pump, reason = await analyzer._detect_pump_and_dump_pattern(pump_mentions)
        print(f"   Pump scheme detection: {is_pump} (reason: {reason})")
        
        if not is_pump:
            print(f"   ❌ Should have detected pump scheme")
            return False
        
        # Test organic growth (proper time distribution)
        organic_ca = "MEME1000XOrganicCA123456789"
        organic_mentions = []
        
        # Create 6 mentions spread over 5 minutes
        for i in range(6):
            mention = CAMention(
                ca=organic_ca,
                timestamp=base_time + timedelta(minutes=i*0.8),  # 48 seconds apart
                group_id=meme_1000x_id,
                group_name="MEME 1000X",
                message_id=5000 + i
            )
            organic_mentions.append(mention)
        
        # Test organic pattern detection
        is_pump2, reason2 = await analyzer._detect_pump_and_dump_pattern(organic_mentions)
        print(f"   Organic pattern detection: {is_pump2} (reason: {reason2})")
        
        if is_pump2:
            print(f"   ❌ Should not have detected pump scheme for organic growth")
            return False
        
        print(f"   ✅ Anti-pump protection working correctly for MEME 1000X")
        return True
        
    except Exception as e:
        print(f"❌ Anti-pump protection test failed: {e}")
        return False

async def main():
    """Run all MEME 1000X integration tests."""
    print("🚀 CLA v2.0 Bot - MEME 1000X Integration Testing\n")
    
    try:
        # Test 1: Configuration
        if not await test_meme_1000x_configuration():
            return False
        
        # Test 2: Trending analysis
        if not await test_meme_1000x_trending_analysis():
            return False
        
        # Test 3: Global duplicate prevention
        if not await test_global_duplicate_prevention():
            return False
        
        # Test 4: Anti-pump protection
        if not await test_anti_pump_protection_meme_1000x():
            return False
        
        print(f"\n🎉 All MEME 1000X integration tests passed!")
        print(f"\n📋 MEME 1000X Integration Summary:")
        print(f"✅ Group Classification: High-volume with trending analysis")
        print(f"✅ Trending Requirements: 6 mentions in 8 minutes")
        print(f"✅ Anti-Pump Protection: 120s spread, 3.0/min velocity, 3min organic growth")
        print(f"✅ Forwarding Logic: Only trending CAs to BonkBot, CLA v2.0, Monaco PNL")
        print(f"✅ WINNERS Exclusion: Globally disabled")
        print(f"✅ Global Duplicate Prevention: Cross-group tracking active")
        print(f"✅ Race Condition Protection: All fixes maintained")
        
        print(f"\n🎯 Expected Behavior:")
        print(f"🔥 MEME 1000X signals require trending qualification before forwarding")
        print(f"🛡️ Anti-pump filters prevent manipulation schemes")
        print(f"🔄 Global duplicate prevention across all 7 monitored groups")
        print(f"⚡ Low-volume groups maintain direct forwarding")
        
        return True
        
    except Exception as e:
        print(f"\n❌ MEME 1000X integration test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
