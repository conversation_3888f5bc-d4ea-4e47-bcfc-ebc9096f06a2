#!/usr/bin/env python3
"""
Test FREE WHALE SIGNALS CA Extraction
Reproduces the exact scenario where the CA was not extracted
"""

import asyncio
import sys
sys.path.append('.')

async def test_exact_scenario():
    """Test the exact scenario from the logs."""
    print("🔍 TESTING FREE WHALE SIGNALS CA EXTRACTION FAILURE")
    print("=" * 70)
    
    # Reconstruct the exact message that should have been processed
    # The logs show truncated version, but the full message should contain the complete CA
    test_messages = [
        # What was logged (truncated)
        "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2ud...",
        
        # What the full message likely was
        "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)",
        
        # Alternative formats that might be used
        "🔥 $NEMA https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups",
        
        # Just the CA alone
        "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
    ]
    
    try:
        from src.message_parser import MessageParser
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        parser = MessageParser()
        db_manager = DatabaseManager()
        await db_manager.initialize()
        ca_detector = CADetector(db_manager)
        
        print("✅ Components initialized successfully")
        print()
        
        for i, message in enumerate(test_messages, 1):
            print(f"🧪 TEST {i}: {message[:60]}{'...' if len(message) > 60 else ''}")
            print("-" * 50)
            
            # Test message parser directly
            extracted_cas = parser.extract_contract_addresses(message)
            print(f"Parser result: {extracted_cas}")
            
            # Test CA detector (full pipeline)
            detected_cas = await ca_detector.process_message(message, 13368, -1002380594298)
            print(f"Detector result: {detected_cas}")
            
            # Test individual components
            print("Component analysis:")
            
            # Test standalone extraction
            cleaned_text = parser._clean_message_text_no_url_extraction(message)
            standalone_cas = parser._extract_standalone_cas(cleaned_text)
            print(f"  Standalone CAs: {standalone_cas}")
            
            # Test URL extraction
            url_cas = parser._extract_cas_from_urls(message)
            print(f"  URL CAs: {url_cas}")
            
            # Test validation
            if url_cas:
                for ca in url_cas:
                    is_valid = parser._validate_solana_ca(ca)
                    print(f"  Validation {ca}: {is_valid}")
            
            print()
        
        # Test the specific CA validation
        test_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
        print(f"🧪 SPECIFIC CA VALIDATION: {test_ca}")
        print("-" * 50)
        print(f"Length: {len(test_ca)}")
        print(f"Valid: {parser._validate_solana_ca(test_ca)}")
        
        try:
            import base58
            decoded = base58.b58decode(test_ca)
            print(f"Base58 decode: {len(decoded)} bytes")
            print(f"All zeros check: {decoded == b'\\x00' * 32}")
        except Exception as e:
            print(f"Base58 decode error: {e}")
        
        print()
        
        # Test the exact dexscreener pattern
        import re
        dex_pattern = re.compile(r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{43,44})')
        
        print("🧪 DEXSCREENER PATTERN TESTING")
        print("-" * 50)
        
        for message in test_messages:
            matches = dex_pattern.findall(message)
            print(f"Pattern matches in '{message[:40]}...': {matches}")
        
        await db_manager.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

async def test_forwarding_logic():
    """Test if the CA would be forwarded if extracted."""
    print("\n🧪 TESTING FORWARDING LOGIC FOR FREE WHALE SIGNALS")
    print("=" * 70)
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        from src.database import DatabaseManager
        
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        trending_analyzer = TrendingAnalyzer(db_manager)
        
        # Test group classification
        free_whale_signals_id = -1002380594298
        
        print(f"Group ID: {free_whale_signals_id}")
        print(f"Is high volume: {trending_analyzer.is_high_volume_group(free_whale_signals_id)}")
        print(f"Is low volume: {trending_analyzer.is_low_volume_group(free_whale_signals_id)}")
        
        # Test forwarding decisions
        test_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
        
        destinations = ['BONKBOT', 'CLA_V2', 'MONACO_PNL']
        
        print(f"\nForwarding decisions for CA: {test_ca}")
        for dest in destinations:
            should_forward = trending_analyzer.should_forward_to_destination(test_ca, dest, free_whale_signals_id)
            print(f"  {dest}: {should_forward}")
        
        await db_manager.close()
        
    except Exception as e:
        print(f"❌ Forwarding test failed: {e}")

async def main():
    """Run all tests."""
    await test_exact_scenario()
    await test_forwarding_logic()
    
    print("\n🎯 ANALYSIS SUMMARY")
    print("=" * 70)
    print("Based on the tests above, the issue is likely:")
    print("1. Message truncation in logs (cosmetic - doesn't affect processing)")
    print("2. CA extraction failure due to markdown link format")
    print("3. URL pattern not matching the specific message format")
    print("4. Or the message was actually truncated before reaching the parser")
    print()
    print("RECOMMENDATIONS:")
    print("1. Add debug logging to show full message text in CA detector")
    print("2. Improve URL extraction for markdown link formats")
    print("3. Add fallback extraction methods")
    print("4. Verify message truncation is not happening in Telegram client")

if __name__ == "__main__":
    asyncio.run(main())
