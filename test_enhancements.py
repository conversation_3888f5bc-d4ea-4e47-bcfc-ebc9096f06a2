"""Test all CLA v2.0 Bot enhancements."""

import os
import sys

# Add src to path
sys.path.insert(0, 'src')

from src.message_parser import MessageParser

def test_link_filtering():
    """Test link filtering enhancement."""
    print("🧪 Testing Link Filtering Enhancement...")
    
    parser = MessageParser()
    
    # Test message with links that should be ignored
    message_with_links = """🔥 NEW SIGNAL ALERT 🔥
    
Check this out on https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
Also visit www.pump.fun for more info
Follow us @telegram_channel

Contract Address:
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk

Don't miss out! Visit t.me/signals for more"""
    
    cas = parser.extract_contract_addresses(message_with_links)
    print(f"✅ Link filtering: {len(cas)} CAs extracted (ignoring links)")
    if cas:
        print(f"   CA: {cas[0]}")
        expected_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
        if cas[0] == expected_ca:
            print("   ✅ Correct CA extracted despite links")
        else:
            print(f"   ❌ Expected {expected_ca}, got {cas[0]}")
    
    # Test Solana Activity Tracker with links
    activity_message = """🔥 ACTIVITY DETECTED 🔥

├ $JOHNBUBU
├ 9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
└| ⏳ 25m | 👁️ 1.3K

📊 Token details
├ PRICE:    $0.00055539
├ MC:       $555.4K /20.3X from VIP/

📈 Charts + Exchanges
├ Axiom (https://axiom.trade/t/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

Follow @premium_signals for more!"""
    
    cas2 = parser.extract_contract_addresses(activity_message)
    print(f"✅ Activity Tracker with links: {len(cas2)} CAs extracted")
    if cas2:
        print(f"   CA: {cas2[0]}")
    
    return True

def test_cross_source_duplicate_prevention():
    """Test cross-source duplicate prevention."""
    print("\n🧪 Testing Cross-Source Duplicate Prevention...")
    
    try:
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        import tempfile
        import asyncio
        
        # Create temporary database for testing
        temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        temp_db.close()
        
        async def test_duplicate_prevention():
            # Setup test database
            db_manager = DatabaseManager()
            db_manager.db_path = temp_db.name
            await db_manager.initialize()
            
            ca_detector = CADetector(db_manager)
            
            test_ca = "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"
            
            # Test first processing from group 1
            new_cas1 = await ca_detector.process_message(
                f"Signal: {test_ca}", 
                123, 
                -1002380594298  # FREE WHALE SIGNALS
            )
            
            # Test second processing from group 2 (should be duplicate)
            new_cas2 = await ca_detector.process_message(
                f"Activity: {test_ca}", 
                456, 
                -1002270988204  # Solana Activity Tracker
            )
            
            # Cleanup
            await db_manager.close()
            os.unlink(temp_db.name)
            
            return len(new_cas1), len(new_cas2)
        
        # Run async test
        result1, result2 = asyncio.run(test_duplicate_prevention())
        
        print(f"✅ First processing (Group 1): {result1} CAs")
        print(f"✅ Second processing (Group 2): {result2} CAs (should be 0)")
        
        if result1 == 1 and result2 == 0:
            print("   ✅ Cross-source duplicate prevention working correctly")
        else:
            print("   ❌ Duplicate prevention not working as expected")
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False
    
    return True

def test_multi_destination_config():
    """Test multi-destination configuration."""
    print("\n🧪 Testing Multi-Destination Configuration...")
    
    try:
        from config import config
        
        print("✅ Configuration loaded successfully!")
        print(f"BonkBot: {config.bonkbot.username}")
        print(f"WINNERS Group: {config.winners_group.group_name} ({config.winners_group.group_id})")
        
        # Test all monitored groups
        print(f"\nMonitored Groups: {len(config.target_group.all_group_ids)}")
        print(f"  - {config.target_group.group_name} ({config.target_group.group_id})")
        for i, group_id in enumerate(config.target_group.additional_group_ids):
            group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {group_id}"
            print(f"  - {group_name} ({group_id})")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_message_parsing_enhancements():
    """Test enhanced message parsing."""
    print("\n🧪 Testing Enhanced Message Parsing...")
    
    parser = MessageParser()
    
    # Test various message formats with links
    test_cases = [
        {
            'name': 'Standard format with links',
            'message': """🔥 SIGNAL 🔥
            
Visit https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk

67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk

Follow @signals""",
            'expected_cas': 1
        },
        {
            'name': 'Activity Tracker with multiple links',
            'message': """🔥 ACTIVITY DETECTED 🔥

├ $TOKEN
├ 9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
└| ⏳ 25m

📈 Charts
├ Axiom (https://axiom.trade/t/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk/@user)
├ DexScreener (https://dexscreener.com/solana/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk)

@follow_us""",
            'expected_cas': 1
        },
        {
            'name': 'Message with only links (no standalone CA)',
            'message': """Check out:
https://pump.fun/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
www.solscan.io/token/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
Follow @channel""",
            'expected_cas': 1  # Should extract from URLs before filtering
        }
    ]
    
    for test_case in test_cases:
        cas = parser.extract_contract_addresses(test_case['message'])
        print(f"✅ {test_case['name']}: {len(cas)} CAs (expected: {test_case['expected_cas']})")
        
        if len(cas) == test_case['expected_cas']:
            print(f"   ✅ Correct number of CAs extracted")
        else:
            print(f"   ❌ Expected {test_case['expected_cas']}, got {len(cas)}")
    
    return True

def main():
    """Run all enhancement tests."""
    print("🚀 CLA v2.0 Bot - Enhancement Testing\n")
    
    try:
        # Test 1: Link Filtering
        if not test_link_filtering():
            return False
        
        # Test 2: Cross-Source Duplicate Prevention
        if not test_cross_source_duplicate_prevention():
            return False
        
        # Test 3: Multi-Destination Configuration
        if not test_multi_destination_config():
            return False
        
        # Test 4: Enhanced Message Parsing
        if not test_message_parsing_enhancements():
            return False
        
        print("\n🎉 All enhancement tests passed!")
        print("\n📋 Enhancement Summary:")
        print("✅ Link Filtering: Ignores clickable links, extracts CAs only")
        print("✅ Cross-Source Duplicate Prevention: Global 72-hour cache")
        print("✅ Multi-Destination Forwarding: BonkBot + WINNERS group")
        print("✅ Enhanced Parsing: Handles all formats with link filtering")
        
        print("\n🔧 The enhanced bot will:")
        print("  - Monitor: FREE WHALE SIGNALS + Solana Activity Tracker")
        print("  - Forward CAs to: BonkBot + WINNERS group")
        print("  - Prevent duplicates: Globally across all sources")
        print("  - Ignore links: Focus on CA extraction only")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Enhancement test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
