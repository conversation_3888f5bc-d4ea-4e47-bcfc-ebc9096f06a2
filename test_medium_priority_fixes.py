"""Test script to verify MEDIUM PRIORITY fixes are working correctly."""

import asyncio
import sys
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_refactored_architecture():
    """Test the refactored bot architecture."""
    print("🏗️ Testing Refactored Architecture...")
    
    try:
        from src.dependency_container import dependency_container
        from src.refactored_bot import RefactoredCLABot
        
        # Test dependency container initialization
        print("  Testing dependency container...")
        await dependency_container.initialize()
        
        # Check if all components are initialized
        components = dependency_container.get_all_components()
        print(f"  ✅ Initialized {len(components)} components")
        
        # Test component retrieval
        ca_analyzer = dependency_container.get('ca_analyzer')
        message_processor = dependency_container.get('message_processor')
        forwarding_manager = dependency_container.get('forwarding_manager')
        
        if all([ca_analyzer, message_processor, forwarding_manager]):
            print("  ✅ Core components accessible")
        else:
            print("  ❌ Some core components missing")
        
        # Test refactored bot
        print("  Testing refactored bot...")
        bot = RefactoredCLABot()
        
        # Test initialization (without starting)
        await bot.initialize()
        print("  ✅ Refactored bot initialized successfully")
        
        # Test statistics
        stats = bot.get_stats()
        print(f"  📊 Bot stats: {len(stats)} categories")
        
        # Cleanup
        await dependency_container.cleanup()
        print("  ✅ Cleanup completed")
        
        print("  ✅ Refactored architecture working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing refactored architecture: {e}")

async def test_error_handling():
    """Test the enhanced error handling system."""
    print("\n🚨 Testing Enhanced Error Handling...")
    
    try:
        from src.error_handler import ErrorHandler
        
        # Test error handler initialization
        error_handler = ErrorHandler()
        print("  ✅ Error handler initialized")
        
        # Test retry mechanism
        print("  Testing retry mechanism...")
        
        attempt_count = 0
        async def failing_operation():
            nonlocal attempt_count
            attempt_count += 1
            if attempt_count < 3:
                raise ValueError(f"Simulated failure {attempt_count}")
            return f"Success on attempt {attempt_count}"
        
        result = await error_handler.handle_error_with_retry(
            operation=failing_operation,
            operation_name="test_operation",
            max_retries=3
        )
        
        print(f"  ✅ Retry mechanism worked: {result}")
        
        # Test error statistics
        stats = error_handler.stats
        print(f"  📊 Error stats: {stats}")
        
        if stats['successful_retries'] > 0:
            print("  ✅ Retry statistics tracking working")
        
        print("  ✅ Enhanced error handling working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing error handling: {e}")

async def test_database_transactions():
    """Test database transaction management."""
    print("\n💾 Testing Database Transaction Management...")
    
    try:
        from src.database import DatabaseManager
        
        # Test database manager initialization
        db_manager = DatabaseManager()
        await db_manager.initialize()
        print("  ✅ Database manager initialized")
        
        # Test transaction context manager
        print("  Testing transaction management...")
        
        try:
            async with db_manager.transaction() as conn:
                # Test transaction
                await conn.execute("SELECT 1")
                print("  ✅ Transaction executed successfully")
        except Exception as e:
            print(f"  ⚠️ Transaction test error: {e}")
        
        # Test retry mechanism
        print("  Testing database retry mechanism...")
        
        try:
            result = await db_manager.execute_with_retry(
                query="SELECT COUNT(*) FROM contract_addresses",
                operation_name="test_count_query"
            )
            print("  ✅ Database retry mechanism working")
        except Exception as e:
            print(f"  ⚠️ Database retry test error: {e}")
        
        # Test transaction statistics
        stats = db_manager.transaction_stats
        print(f"  📊 Transaction stats: {stats}")
        
        # Cleanup
        await db_manager.close()
        print("  ✅ Database connection closed")
        
        print("  ✅ Database transaction management working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing database transactions: {e}")

async def test_configuration_system():
    """Test the enhanced configuration system."""
    print("\n⚙️ Testing Enhanced Configuration System...")
    
    try:
        from config import config
        
        # Test configuration loading
        print("  Testing configuration loading...")
        
        # Test trending configuration
        trending_config = config.trending
        print(f"  📊 Trending config: {trending_config.min_mentions} mentions, {trending_config.time_window_minutes} minutes")
        
        # Test rescue configuration
        rescue_config = config.rescue
        print(f"  🛡️ Rescue config: enabled={rescue_config.enabled}, window={rescue_config.rescue_window_hours}h")
        
        # Test performance configuration
        performance_config = config.performance
        print(f"  ⚡ Performance config: dedup={performance_config.message_dedup_window_seconds}s")
        
        # Test integration configuration
        integration_config = config.integration
        print(f"  🔗 Integration config: timeout={integration_config.forwarding_timeout_seconds}s")
        
        # Test configuration summary
        summary = config.get_configuration_summary()
        print(f"  📋 Configuration summary: {len(summary)} sections")
        
        # Test group configuration
        high_volume_count = len(config.trending.high_volume_groups)
        low_volume_count = len(config.trending.low_volume_groups)
        print(f"  🏷️ Groups: {high_volume_count} high-volume, {low_volume_count} low-volume")
        
        if high_volume_count > 0 and low_volume_count > 0:
            print("  ✅ Group configuration loaded correctly")
        else:
            print("  ⚠️ Group configuration may need review")
        
        print("  ✅ Enhanced configuration system working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing configuration system: {e}")

async def test_component_integration():
    """Test integration between refactored components."""
    print("\n🔗 Testing Component Integration...")
    
    try:
        from src.message_processor import MessageProcessor, MessageValidator
        from src.ca_analyzer import CAAnalyzer
        from src.forwarding_manager import ForwardingManager
        
        # Test component creation
        print("  Testing component creation...")
        
        # Create mock components for testing
        class MockCAAnalyzer:
            async def analyze_message(self, **kwargs):
                return {'new_cas': [], 'trending_cas': [], 'rescue_cas': []}
            
            def get_stats(self):
                return {'messages_analyzed': 0}
        
        class MockForwardingManager:
            async def forward_trending_cas(self, **kwargs):
                return {'forwarded_count': 0}
            
            def get_stats(self):
                return {'total_forwarding_attempts': 0}
        
        # Test message processor with mock dependencies
        ca_analyzer = MockCAAnalyzer()
        forwarding_manager = MockForwardingManager()
        message_processor = MessageProcessor(ca_analyzer, forwarding_manager)
        
        print("  ✅ Components created successfully")
        
        # Test component statistics
        processor_stats = message_processor.get_stats()
        analyzer_stats = ca_analyzer.get_stats()
        forwarding_stats = forwarding_manager.get_stats()
        
        print(f"  📊 Component stats collected: {len(processor_stats)} + {len(analyzer_stats)} + {len(forwarding_stats)} metrics")
        
        # Test message validator
        validator = MessageValidator()
        is_valid, reason = await validator.validate_message("Test message", -1002380594298)
        print(f"  ✅ Message validation: {is_valid} ({reason})")
        
        print("  ✅ Component integration working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing component integration: {e}")

async def test_backward_compatibility():
    """Test that existing functionality still works."""
    print("\n🔄 Testing Backward Compatibility...")
    
    try:
        # Test that old imports still work
        from src.group_manager import group_manager
        from src.ca_rescue_tracker import ca_rescue_tracker
        from src.enhanced_stats_tracker import enhanced_stats
        
        print("  ✅ Legacy imports working")
        
        # Test group manager functionality
        test_group_id = -1002380594298
        is_low_volume = group_manager.is_low_volume_group(test_group_id)
        group_name = group_manager.get_group_name(test_group_id)
        
        print(f"  ✅ Group manager: {group_name} is low-volume: {is_low_volume}")
        
        # Test rescue tracker functionality
        rescue_stats = ca_rescue_tracker.get_rescue_statistics()
        print(f"  ✅ Rescue tracker stats: {len(rescue_stats)} metrics")
        
        # Test enhanced stats functionality
        current_stats = enhanced_stats.get_current_stats()
        print(f"  ✅ Enhanced stats: {len(current_stats)} categories")
        
        print("  ✅ Backward compatibility maintained!")
        
    except Exception as e:
        print(f"  ❌ Error testing backward compatibility: {e}")

async def run_comprehensive_test():
    """Run all medium priority fix tests."""
    print("🔧 MEDIUM PRIORITY FIXES VERIFICATION")
    print("=" * 60)
    print(f"📅 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # Run all tests
    await test_refactored_architecture()
    await test_error_handling()
    await test_database_transactions()
    await test_configuration_system()
    await test_component_integration()
    await test_backward_compatibility()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print()
    print("=" * 60)
    print(f"✅ All tests completed in {duration:.2f} seconds")
    print("🎯 MEDIUM PRIORITY FIXES VERIFICATION COMPLETE")

def main():
    """Main test function."""
    try:
        asyncio.run(run_comprehensive_test())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")

if __name__ == "__main__":
    main()
