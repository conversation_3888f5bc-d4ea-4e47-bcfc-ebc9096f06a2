#!/bin/bash
# CLA v2.0 Telegram Bot - AWS EC2 Installation Script
# ===================================================
# This script installs the CLA v2.0 bot on AWS EC2 Ubuntu 22.04
# Run as: sudo bash aws-install.sh

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BOT_USER="cla-bot"
BOT_GROUP="cla-bot"
INSTALL_DIR="/opt/cla-bot"
SERVICE_NAME="cla-bot"
PYTHON_VERSION="3.11"

echo -e "${BLUE}🚀 CLA v2.0 Telegram Bot - AWS EC2 Installation${NC}"
echo -e "${BLUE}===============================================${NC}"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root (use sudo)${NC}"
   exit 1
fi

# Check if running on Ubuntu
if ! grep -q "Ubuntu" /etc/os-release; then
    echo -e "${RED}❌ This script is designed for Ubuntu. Current OS:${NC}"
    cat /etc/os-release | grep PRETTY_NAME
    exit 1
fi

echo -e "${YELLOW}📋 Installation Configuration:${NC}"
echo -e "   User: ${BOT_USER}"
echo -e "   Group: ${BOT_GROUP}"
echo -e "   Install Directory: ${INSTALL_DIR}"
echo -e "   Service Name: ${SERVICE_NAME}"
echo -e "   Python Version: ${PYTHON_VERSION}"
echo -e "   OS: $(lsb_release -d | cut -f2)"
echo ""

read -p "Continue with installation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Installation cancelled${NC}"
    exit 0
fi

# Update system packages
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt update && apt upgrade -y

# Install required system packages
echo -e "${BLUE}📦 Installing system dependencies...${NC}"
apt install -y \
    software-properties-common \
    curl \
    wget \
    git \
    htop \
    unzip \
    sqlite3 \
    logrotate \
    fail2ban \
    ufw \
    supervisor \
    nginx \
    awscli

# Install Python 3.11
echo -e "${BLUE}🐍 Installing Python 3.11...${NC}"
add-apt-repository ppa:deadsnakes/ppa -y
apt update
apt install -y \
    python3.11 \
    python3.11-venv \
    python3.11-dev \
    python3-pip

# Verify Python installation
python3.11 --version
echo -e "${GREEN}✅ Python 3.11 installed successfully${NC}"

# Create bot user and group
echo -e "${BLUE}👤 Creating bot user and group...${NC}"
if ! getent group "$BOT_GROUP" > /dev/null 2>&1; then
    groupadd --system "$BOT_GROUP"
    echo -e "${GREEN}✅ Created group: $BOT_GROUP${NC}"
fi

if ! getent passwd "$BOT_USER" > /dev/null 2>&1; then
    useradd --system --gid "$BOT_GROUP" --home-dir "$INSTALL_DIR" \
            --shell /bin/bash --comment "CLA v2.0 Bot User" "$BOT_USER"
    echo -e "${GREEN}✅ Created user: $BOT_USER${NC}"
fi

# Create installation directory structure
echo -e "${BLUE}📁 Creating installation directories...${NC}"
mkdir -p "$INSTALL_DIR"
mkdir -p "$INSTALL_DIR/data"
mkdir -p "$INSTALL_DIR/logs"
mkdir -p "$INSTALL_DIR/backups"
mkdir -p "$INSTALL_DIR/config"
mkdir -p "$INSTALL_DIR/scripts"

# Set up Python virtual environment
echo -e "${BLUE}🐍 Setting up Python virtual environment...${NC}"
cd "$INSTALL_DIR"
sudo -u "$BOT_USER" python3.11 -m venv .venv
sudo -u "$BOT_USER" .venv/bin/pip install --upgrade pip

echo -e "${GREEN}✅ Virtual environment created successfully${NC}"

# Create requirements.txt if not exists
if [[ ! -f "$INSTALL_DIR/requirements.txt" ]]; then
    echo -e "${BLUE}📋 Creating requirements.txt...${NC}"
    cat > "$INSTALL_DIR/requirements.txt" << 'EOF'
telethon==1.34.0
aiohttp==3.9.1
aiosqlite==0.19.0
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0
loguru==0.7.2
base58==2.1.1
asyncio-throttle==1.0.2
EOF
fi

# Install Python dependencies
echo -e "${BLUE}📦 Installing Python dependencies...${NC}"
sudo -u "$BOT_USER" .venv/bin/pip install -r requirements.txt

# Create environment file template
echo -e "${BLUE}⚙️ Creating environment configuration template...${NC}"
if [[ ! -f "$INSTALL_DIR/.env" ]]; then
    cat > "$INSTALL_DIR/.env" << 'EOF'
# CLA v2.0 Telegram Bot - AWS EC2 Configuration
# ==============================================

# Environment
ENVIRONMENT=production

# Telegram API Configuration (REQUIRED - Replace with your values)
TELEGRAM_API_ID=YOUR_API_ID
TELEGRAM_API_HASH=YOUR_API_HASH
TELEGRAM_PHONE=YOUR_PHONE_NUMBER
TELEGRAM_SESSION_NAME=cla_bot_session

# Target Groups (REQUIRED - Replace with your group IDs)
TARGET_GROUP_ID=-1002380594298
ADDITIONAL_GROUP_IDS=-1002356333152,-1002139128702,-1002064145465,-1001763265784,-1002270988204,-1002202241417,-1002333406905
ADDITIONAL_GROUP_NAMES=Group1,Group2,Group3,Group4,Group5,Group6,Group7
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE

# Integration Channels (REQUIRED - Replace with your values)
BONKBOT_USERNAME=@BonkBot_bot
BONKBOT_CHAT_ID=YOUR_BONKBOT_CHAT_ID
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391

# Database Configuration
DATABASE_PATH=/opt/cla-bot/data/cla_bot.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/opt/cla-bot/logs/cla_bot.log

# Performance Configuration
MESSAGE_RATE_LIMIT=30
API_RATE_LIMIT=20
CA_CACHE_EXPIRY_HOURS=72
MAX_CACHE_SIZE=10000

# Trending Analysis Configuration
TRENDING_ENABLED=true
TRENDING_TIME_WINDOW_MINUTES=8
TRENDING_MIN_MENTIONS=6
TRENDING_HIGH_VOLUME_GROUPS=-1002333406905,-1002202241417
TRENDING_LOW_VOLUME_GROUPS=-1002380594298,-1002270988204,-1002064145465,-1001763265784,-1002139128702

# Rescue Mechanism Configuration
RESCUE_ENABLED=true
RESCUE_WINDOW_HOURS=24
RESCUE_MAX_CACHE_SIZE=1000

# AWS CloudWatch Configuration (Optional)
AWS_REGION=us-east-1
CLOUDWATCH_NAMESPACE=CLA-Bot
CLOUDWATCH_LOG_GROUP=/aws/ec2/cla-bot
EOF

    echo -e "${YELLOW}⚠️ IMPORTANT: Edit $INSTALL_DIR/.env with your actual configuration!${NC}"
fi

# Set proper file permissions
echo -e "${BLUE}🔒 Setting file permissions...${NC}"
chown -R "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR"
chmod 755 "$INSTALL_DIR"
chmod 750 "$INSTALL_DIR/data"
chmod 750 "$INSTALL_DIR/logs"
chmod 750 "$INSTALL_DIR/backups"
chmod 640 "$INSTALL_DIR/.env"

# Create systemd service file
echo -e "${BLUE}🔧 Creating systemd service...${NC}"
cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=CLA v2.0 Telegram Bot - AWS EC2 Deployment
Documentation=https://github.com/your-repo/cla-v2-bot
After=network.target network-online.target
Wants=network-online.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=$BOT_USER
Group=$BOT_GROUP
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/.venv/bin/python main.py
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=/bin/kill -TERM \$MAINPID

# Environment
Environment=PYTHONPATH=$INSTALL_DIR
Environment=ENVIRONMENT=production
EnvironmentFile=$INSTALL_DIR/.env

# Restart policy
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits (AWS t2.micro: 1 vCPU, 1GB RAM)
LimitNOFILE=65536
LimitNPROC=2048
MemoryMax=800M
CPUQuota=100%

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR/data $INSTALL_DIR/logs $INSTALL_DIR/backups
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF

# Enable systemd service
systemctl daemon-reload
systemctl enable "$SERVICE_NAME"

# Set up log rotation
echo -e "${BLUE}📝 Setting up log rotation...${NC}"
cat > "/etc/logrotate.d/$SERVICE_NAME" << EOF
$INSTALL_DIR/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $BOT_USER $BOT_GROUP
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF

# Create backup script
echo -e "${BLUE}💾 Creating backup script...${NC}"
cat > "$INSTALL_DIR/scripts/backup.sh" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Backup Script for AWS EC2

BACKUP_DIR="/opt/cla-bot/backups"
DB_PATH="/opt/cla-bot/data/cla_bot.db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/cla_bot_backup_$DATE.tar.gz"

# Create backup
mkdir -p "$BACKUP_DIR"
tar -czf "$BACKUP_FILE" -C /opt/cla-bot data/ logs/ .env

# Keep only last 30 days of backups
find "$BACKUP_DIR" -name "cla_bot_backup_*.tar.gz" -mtime +30 -delete

# Optional: Upload to S3 (uncomment and configure)
# aws s3 cp "$BACKUP_FILE" s3://your-backup-bucket/cla-bot/

echo "Backup created: $BACKUP_FILE"
EOF

chmod +x "$INSTALL_DIR/scripts/backup.sh"
chown "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/scripts/backup.sh"

# Create monitoring script
echo -e "${BLUE}📊 Creating monitoring script...${NC}"
cat > "$INSTALL_DIR/scripts/monitor.sh" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Monitoring Script for AWS EC2

SERVICE_NAME="cla-bot"
HEALTH_URL="http://127.0.0.1:8080/health"
LOG_FILE="/opt/cla-bot/logs/monitor.log"

# Check if service is running
if ! systemctl is-active --quiet "$SERVICE_NAME"; then
    echo "$(date): Service $SERVICE_NAME is not running, attempting restart" >> "$LOG_FILE"
    systemctl restart "$SERVICE_NAME"
    sleep 10
fi

# Check health endpoint
if ! curl -f "$HEALTH_URL" > /dev/null 2>&1; then
    echo "$(date): Health check failed for $SERVICE_NAME" >> "$LOG_FILE"
    systemctl restart "$SERVICE_NAME"
fi

# Optional: Send metrics to CloudWatch
# aws cloudwatch put-metric-data --namespace "CLA-Bot" --metric-data MetricName=ServiceStatus,Value=1,Unit=Count
EOF

chmod +x "$INSTALL_DIR/scripts/monitor.sh"
chown "$BOT_USER:$BOT_GROUP" "$INSTALL_DIR/scripts/monitor.sh"

# Set up cron jobs
echo -e "${BLUE}⏰ Setting up cron jobs...${NC}"
(crontab -u "$BOT_USER" -l 2>/dev/null; echo "0 */6 * * * $INSTALL_DIR/scripts/backup.sh") | crontab -u "$BOT_USER" -
(crontab -u "$BOT_USER" -l 2>/dev/null; echo "*/5 * * * * $INSTALL_DIR/scripts/monitor.sh") | crontab -u "$BOT_USER" -

# Create status script
echo -e "${BLUE}📋 Creating status script...${NC}"
cat > "/usr/local/bin/cla-bot-status" << 'EOF'
#!/bin/bash
# CLA v2.0 Bot Status Script for AWS EC2

echo "🤖 CLA v2.0 Telegram Bot Status (AWS EC2)"
echo "=========================================="

# Instance information
echo "🖥️ Instance Information:"
curl -s http://***************/latest/meta-data/instance-id 2>/dev/null && echo " (Instance ID)"
curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null && echo " (Public IP)"
curl -s http://***************/latest/meta-data/instance-type 2>/dev/null && echo " (Instance Type)"

echo ""
echo "📊 Service Status:"
systemctl status cla-bot --no-pager -l

echo ""
echo "🏥 Health Check:"
curl -s http://127.0.0.1:8080/health | python3 -m json.tool 2>/dev/null || echo "Health check unavailable"

echo ""
echo "💾 Disk Usage:"
df -h /opt/cla-bot

echo ""
echo "🧠 Memory Usage:"
free -h

echo ""
echo "📝 Recent Logs (last 10 lines):"
tail -n 10 /opt/cla-bot/logs/cla_bot.log 2>/dev/null || echo "No logs available"
EOF

chmod +x "/usr/local/bin/cla-bot-status"

echo -e "${GREEN}✅ Installation completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo -e "1. Edit configuration: ${BLUE}sudo nano $INSTALL_DIR/.env${NC}"
echo -e "2. Copy your bot files to: ${BLUE}$INSTALL_DIR/${NC}"
echo -e "3. Start the service: ${BLUE}sudo systemctl start $SERVICE_NAME${NC}"
echo -e "4. Check status: ${BLUE}cla-bot-status${NC}"
echo -e "5. View logs: ${BLUE}sudo journalctl -u $SERVICE_NAME -f${NC}"
echo ""
echo -e "${YELLOW}🔧 Useful Commands:${NC}"
echo -e "   Status: ${BLUE}cla-bot-status${NC}"
echo -e "   Start: ${BLUE}sudo systemctl start $SERVICE_NAME${NC}"
echo -e "   Stop: ${BLUE}sudo systemctl stop $SERVICE_NAME${NC}"
echo -e "   Restart: ${BLUE}sudo systemctl restart $SERVICE_NAME${NC}"
echo -e "   Logs: ${BLUE}sudo journalctl -u $SERVICE_NAME -f${NC}"
echo -e "   Health: ${BLUE}curl http://127.0.0.1:8080/health${NC}"
echo ""
echo -e "${RED}⚠️ CRITICAL: Edit $INSTALL_DIR/.env with your actual API keys and configuration!${NC}"
echo -e "${BLUE}💡 Use the deployment guide for step-by-step instructions.${NC}"