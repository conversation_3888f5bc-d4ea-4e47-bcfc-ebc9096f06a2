"""Real-time performance monitoring for CLA v2.0 bot."""

import re
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from typing import Dict, List, Tu<PERSON>

def monitor_performance():
    """Monitor bot performance in real-time."""
    print("📊 CLA v2.0 Bot - Real-Time Performance Monitor")
    print("=" * 60)
    print("Monitoring for:")
    print("⚠️ Slow message handling (>100ms)")
    print("⚠️ Slow CA detection (>200ms)")
    print("⚠️ Slow trending analysis (>300ms)")
    print("⚠️ Slow forwarding (>500ms)")
    print("⚠️ Slow pipeline (>1000ms)")
    print("📊 Performance statistics")
    print("=" * 60)
    
    # Performance tracking
    performance_data = {
        'message_handling': deque(maxlen=100),
        'ca_detection': deque(maxlen=100),
        'trending_analysis': deque(maxlen=100),
        'forwarding': deque(maxlen=100),
        'pipeline_total': deque(maxlen=100)
    }
    
    bottleneck_counts = defaultdict(int)
    last_stats_time = datetime.now()
    
    try:
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            # Go to end of file
            f.seek(0, 2)
            
            print(f"Started monitoring at {datetime.now().strftime('%H:%M:%S')}")
            print("Waiting for performance data...\n")
            
            while True:
                line = f.readline()
                if not line:
                    time.sleep(0.1)
                    continue
                
                line = line.strip()
                timestamp = datetime.now().strftime('%H:%M:%S')
                
                # Parse performance data
                if "MESSAGE TIMING:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['message_handling'].append(duration)
                        print(f"[{timestamp}] 📊 Message: {duration:.1f}ms")
                
                elif "SLOW MESSAGE HANDLING:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['message_handling'].append(duration)
                        bottleneck_counts['message_handling'] += 1
                        print(f"[{timestamp}] ⚠️ SLOW MESSAGE: {duration:.1f}ms")
                
                elif "CA DETECTION:" in line and "ms:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['ca_detection'].append(duration)
                        if duration > 200:
                            bottleneck_counts['ca_detection'] += 1
                            print(f"[{timestamp}] ⚠️ SLOW CA DETECTION: {duration:.1f}ms")
                        else:
                            print(f"[{timestamp}] 🔍 CA Detection: {duration:.1f}ms")
                
                elif "SLOW CA DETECTION:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['ca_detection'].append(duration)
                        bottleneck_counts['ca_detection'] += 1
                        print(f"[{timestamp}] ⚠️ SLOW CA DETECTION: {duration:.1f}ms")
                
                elif "Analysis:" in line and "ms" in line:
                    timing_match = re.search(r'Analysis: (\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['trending_analysis'].append(duration)
                        if duration > 300:
                            bottleneck_counts['trending_analysis'] += 1
                            print(f"[{timestamp}] ⚠️ SLOW TRENDING: {duration:.1f}ms")
                        else:
                            print(f"[{timestamp}] 📊 Trending: {duration:.1f}ms")
                
                elif "SLOW TRENDING ANALYSIS:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['trending_analysis'].append(duration)
                        bottleneck_counts['trending_analysis'] += 1
                        print(f"[{timestamp}] ⚠️ SLOW TRENDING: {duration:.1f}ms")
                
                elif "SLOW FORWARDING:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['forwarding'].append(duration)
                        bottleneck_counts['forwarding'] += 1
                        print(f"[{timestamp}] ⚠️ SLOW FORWARDING: {duration:.1f}ms")
                
                elif "PIPELINE TIMING:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['pipeline_total'].append(duration)
                        print(f"[{timestamp}] 📊 Pipeline: {duration:.1f}ms")
                
                elif "SLOW PIPELINE:" in line:
                    timing_match = re.search(r'(\d+\.\d+)ms', line)
                    if timing_match:
                        duration = float(timing_match.group(1))
                        performance_data['pipeline_total'].append(duration)
                        bottleneck_counts['pipeline_total'] += 1
                        print(f"[{timestamp}] ⚠️ SLOW PIPELINE: {duration:.1f}ms")
                
                # Show performance statistics every 60 seconds
                now = datetime.now()
                if (now - last_stats_time).total_seconds() >= 60:
                    show_performance_stats(performance_data, bottleneck_counts)
                    last_stats_time = now
                
    except KeyboardInterrupt:
        print(f"\n🛑 Monitoring stopped at {datetime.now().strftime('%H:%M:%S')}")
        show_performance_stats(performance_data, bottleneck_counts)
    except FileNotFoundError:
        print("❌ Log file not found. Make sure the bot is running.")
    except Exception as e:
        print(f"❌ Error monitoring performance: {e}")

def show_performance_stats(performance_data: Dict, bottleneck_counts: Dict):
    """Show performance statistics summary."""
    print(f"\n📊 Performance Statistics Summary")
    print("-" * 50)
    
    for operation, timings in performance_data.items():
        if not timings:
            continue
        
        avg_time = sum(timings) / len(timings)
        max_time = max(timings)
        min_time = min(timings)
        recent_avg = sum(list(timings)[-10:]) / min(10, len(timings))
        
        operation_name = operation.replace('_', ' ').title()
        bottlenecks = bottleneck_counts.get(operation, 0)
        
        print(f"{operation_name}:")
        print(f"  Samples: {len(timings)} | Bottlenecks: {bottlenecks}")
        print(f"  Avg: {avg_time:.1f}ms | Recent: {recent_avg:.1f}ms")
        print(f"  Min: {min_time:.1f}ms | Max: {max_time:.1f}ms")
        
        # Performance rating
        if operation == 'message_handling' and avg_time < 50:
            print(f"  Status: ✅ EXCELLENT")
        elif operation == 'ca_detection' and avg_time < 100:
            print(f"  Status: ✅ EXCELLENT")
        elif operation == 'trending_analysis' and avg_time < 200:
            print(f"  Status: ✅ EXCELLENT")
        elif operation == 'forwarding' and avg_time < 300:
            print(f"  Status: ✅ EXCELLENT")
        elif operation == 'pipeline_total' and avg_time < 500:
            print(f"  Status: ✅ EXCELLENT")
        elif bottlenecks == 0:
            print(f"  Status: ✅ GOOD")
        elif bottlenecks < 5:
            print(f"  Status: ⚠️ FAIR")
        else:
            print(f"  Status: ❌ NEEDS ATTENTION")
        
        print()
    
    # Overall system health
    total_bottlenecks = sum(bottleneck_counts.values())
    if total_bottlenecks == 0:
        print("🎉 System Health: EXCELLENT - No bottlenecks detected!")
    elif total_bottlenecks < 10:
        print("✅ System Health: GOOD - Minor performance issues")
    elif total_bottlenecks < 25:
        print("⚠️ System Health: FAIR - Some performance concerns")
    else:
        print("❌ System Health: POOR - Significant performance issues")
    
    print("-" * 50)

def analyze_recent_performance():
    """Analyze recent performance from log file."""
    print("📊 Recent Performance Analysis")
    print("=" * 60)
    
    try:
        with open('logs/cla_bot.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Analyze last 500 lines
        recent_lines = lines[-500:]
        
        # Extract timing data
        timings = {
            'message_handling': [],
            'ca_detection': [],
            'trending_analysis': [],
            'forwarding': [],
            'pipeline_total': []
        }
        
        bottlenecks = defaultdict(int)
        
        for line in recent_lines:
            if "MESSAGE TIMING:" in line or "SLOW MESSAGE HANDLING:" in line:
                timing_match = re.search(r'(\d+\.\d+)ms', line)
                if timing_match:
                    duration = float(timing_match.group(1))
                    timings['message_handling'].append(duration)
                    if duration > 100:
                        bottlenecks['message_handling'] += 1
            
            elif "CA DETECTION:" in line and "ms:" in line:
                timing_match = re.search(r'(\d+\.\d+)ms', line)
                if timing_match:
                    duration = float(timing_match.group(1))
                    timings['ca_detection'].append(duration)
                    if duration > 200:
                        bottlenecks['ca_detection'] += 1
            
            elif "Analysis:" in line and "ms" in line:
                timing_match = re.search(r'Analysis: (\d+\.\d+)ms', line)
                if timing_match:
                    duration = float(timing_match.group(1))
                    timings['trending_analysis'].append(duration)
                    if duration > 300:
                        bottlenecks['trending_analysis'] += 1
            
            elif "PIPELINE TIMING:" in line or "SLOW PIPELINE:" in line:
                timing_match = re.search(r'(\d+\.\d+)ms', line)
                if timing_match:
                    duration = float(timing_match.group(1))
                    timings['pipeline_total'].append(duration)
                    if duration > 1000:
                        bottlenecks['pipeline_total'] += 1
        
        show_performance_stats(timings, bottlenecks)
        
        # MEME 1000X specific analysis
        print(f"\n🚀 MEME 1000X Performance Analysis:")
        meme_lines = [line for line in recent_lines if 'MEME 1000X' in line]
        
        if meme_lines:
            latest_meme = meme_lines[-1]
            print(f"   Latest activity: {latest_meme.strip()}")
            
            # Check for timing data in MEME 1000X messages
            meme_timings = []
            for line in meme_lines:
                timing_match = re.search(r'(\d+\.\d+)ms', line)
                if timing_match:
                    meme_timings.append(float(timing_match.group(1)))
            
            if meme_timings:
                avg_meme_time = sum(meme_timings) / len(meme_timings)
                print(f"   Average processing time: {avg_meme_time:.1f}ms")
                print(f"   Performance: {'✅ EXCELLENT' if avg_meme_time < 500 else '⚠️ NEEDS ATTENTION'}")
            else:
                print(f"   No timing data found in recent MEME 1000X messages")
        else:
            print(f"   No recent MEME 1000X activity found")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

def main():
    """Main performance monitoring function."""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'analyze':
        analyze_recent_performance()
    else:
        print("🚀 CLA v2.0 Bot Performance Monitor")
        print("\nOptions:")
        print("  python monitor_performance.py         - Real-time monitoring")
        print("  python monitor_performance.py analyze - Analyze recent performance")
        print()
        
        choice = input("Start real-time monitoring? (y/n): ").lower()
        if choice in ['y', 'yes']:
            monitor_performance()
        else:
            analyze_recent_performance()

if __name__ == "__main__":
    main()
