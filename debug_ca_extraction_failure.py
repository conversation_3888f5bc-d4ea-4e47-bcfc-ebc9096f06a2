#!/usr/bin/env python3
"""
Debug CA Extraction Failure for FREE WHALE SIGNALS
Analyzes why 5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups was not extracted
"""

import sys
import re
sys.path.append('.')

def test_ca_extraction():
    """Test CA extraction with the exact message from logs."""
    print("🔍 DEBUGGING CA EXTRACTION FAILURE")
    print("=" * 60)
    
    # The exact message from logs (truncated)
    truncated_message = "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2ud..."
    
    # The complete message (reconstructed)
    complete_message = "🔥[$NEMA](https://dexscreener.com/solana/5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups)"
    
    # Test CA
    test_ca = "5hUL8iHMXcUj9AS7yBErJmmTXyRvbdwwUqbtB2udjups"
    
    print(f"Test CA: {test_ca}")
    print(f"CA Length: {len(test_ca)}")
    print()
    
    # Test CA validation
    try:
        import base58
        decoded = base58.b58decode(test_ca)
        print(f"✅ CA is valid base58: {len(decoded)} bytes")
    except Exception as e:
        print(f"❌ CA validation failed: {e}")
        return
    
    # Test URL patterns
    url_patterns = {
        'dexscreener': re.compile(r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{43,44})'),
        'pump_fun': re.compile(r'pump\.fun/([1-9A-HJ-NP-Za-km-z]{43,44})'),
        'solscan': re.compile(r'solscan\.io/token/([1-9A-HJ-NP-Za-km-z]{43,44})'),
        'axiom': re.compile(r'axiom\.trade/t/([1-9A-HJ-NP-Za-km-z]{43,44})'),
        'gmgn': re.compile(r'gmgn\.ai/sol/token/([1-9A-HJ-NP-Za-km-z]{43,44})')
    }
    
    print("🧪 TESTING URL PATTERN EXTRACTION")
    print("-" * 40)
    
    # Test with truncated message (what the bot received)
    print("Testing truncated message:")
    print(f"Message: {truncated_message}")
    
    for platform, pattern in url_patterns.items():
        matches = pattern.findall(truncated_message)
        print(f"  {platform}: {matches}")
    
    print()
    
    # Test with complete message (what should work)
    print("Testing complete message:")
    print(f"Message: {complete_message}")
    
    for platform, pattern in url_patterns.items():
        matches = pattern.findall(complete_message)
        print(f"  {platform}: {matches}")
    
    print()
    
    # Test standalone CA extraction
    print("🧪 TESTING STANDALONE CA EXTRACTION")
    print("-" * 40)
    
    ca_pattern = re.compile(r'\b[1-9A-HJ-NP-Za-km-z]{43,44}\b')
    
    # Test in truncated message
    standalone_matches_truncated = ca_pattern.findall(truncated_message)
    print(f"Standalone CAs in truncated: {standalone_matches_truncated}")
    
    # Test in complete message
    standalone_matches_complete = ca_pattern.findall(complete_message)
    print(f"Standalone CAs in complete: {standalone_matches_complete}")
    
    print()
    
    # Test message cleaning
    print("🧪 TESTING MESSAGE CLEANING")
    print("-" * 40)
    
    def clean_message_no_url_extraction(text):
        """Simulate the cleaning process."""
        # Remove emoji prefixes
        emoji_prefixes = ['🔥', '🚨', '🛎️', '💸', '🥵', '🎉', '⚡', '🚀', '💎']
        for emoji in emoji_prefixes:
            text = text.replace(emoji, '')
        
        # Remove markdown links but keep the text
        text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    cleaned_truncated = clean_message_no_url_extraction(truncated_message)
    cleaned_complete = clean_message_no_url_extraction(complete_message)
    
    print(f"Cleaned truncated: '{cleaned_truncated}'")
    print(f"Cleaned complete: '{cleaned_complete}'")
    
    # Test standalone extraction on cleaned text
    standalone_cleaned_truncated = ca_pattern.findall(cleaned_truncated)
    standalone_cleaned_complete = ca_pattern.findall(cleaned_complete)
    
    print(f"Standalone in cleaned truncated: {standalone_cleaned_truncated}")
    print(f"Standalone in cleaned complete: {standalone_cleaned_complete}")
    
    print()
    
    # Root cause analysis
    print("🔍 ROOT CAUSE ANALYSIS")
    print("=" * 60)
    
    if not standalone_matches_truncated and not url_patterns['dexscreener'].findall(truncated_message):
        print("❌ ISSUE IDENTIFIED: Message truncation prevents CA extraction")
        print("   - The message was truncated with '...' at the end")
        print("   - Truncated CA cannot be validated or extracted")
        print("   - URL pattern matching fails on incomplete CA")
        print("   - Standalone pattern matching fails on incomplete CA")
        print()
        print("💡 SOLUTION NEEDED:")
        print("   1. Fix message truncation in logging/processing")
        print("   2. Ensure full message text is passed to CA extraction")
        print("   3. Add handling for truncated messages")
    
    if url_patterns['dexscreener'].findall(complete_message):
        print("✅ VERIFICATION: Complete message would extract CA correctly")
        print(f"   - Extracted CA: {url_patterns['dexscreener'].findall(complete_message)[0]}")
    
    print()
    
    # Test the actual message parser
    print("🧪 TESTING ACTUAL MESSAGE PARSER")
    print("-" * 40)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test with both messages
        extracted_truncated = parser.extract_contract_addresses(truncated_message)
        extracted_complete = parser.extract_contract_addresses(complete_message)
        
        print(f"Parser result (truncated): {extracted_truncated}")
        print(f"Parser result (complete): {extracted_complete}")
        
        if not extracted_truncated and extracted_complete:
            print("✅ CONFIRMED: Truncation is the root cause")
        elif not extracted_truncated and not extracted_complete:
            print("❌ DEEPER ISSUE: Parser fails even with complete message")
        
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
    
    print()
    print("🎯 CONCLUSION")
    print("=" * 60)
    print("The CA extraction failed because:")
    print("1. The message was truncated in the logs (ending with '...')")
    print("2. The incomplete CA cannot be validated or extracted")
    print("3. Both URL and standalone extraction require complete CAs")
    print()
    print("NEXT STEPS:")
    print("1. Investigate why message text is being truncated")
    print("2. Ensure full message content reaches the CA extraction")
    print("3. Add better handling for edge cases")

if __name__ == "__main__":
    test_ca_extraction()
