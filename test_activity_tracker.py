"""Test Solana Activity Tracker message parsing."""

import os
import sys

# Add src to path
sys.path.insert(0, 'src')

from src.message_parser import MessageParser

def test_activity_tracker_parsing():
    """Test parsing of Solana Activity Tracker messages."""
    print("🧪 Testing Solana Activity Tracker Message Parsing...")
    
    parser = MessageParser()
    
    # Test message from Solana Activity Tracker
    message = """🔥 ACTIVITY DETECTED 🔥

├ $JOHNBUBU
├ 9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
└| ⏳ 25m | 👁️ 1.3K

📊 Token details
├ PRICE:    $0.00055539
├ MC:       $555.4K /20.3X from VIP/
├ Vol:      $1.13M


🔒 Security
├ Dev S:    🟢
├ Dex P:    🟢
├ Risk:     only available for Premium users
├ Bundled:  only available for Premium users


🤖 Bots
└ only available for premium users

📈 Charts + Exchanges
└ only available for premium users
├ Axiom (https://axiom.trade/t/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

💰 JOIN PREMIUM  TO CATCH THE NEXT 20.3X! 🔥🚀"""
    
    # Test CA extraction
    cas = parser.extract_contract_addresses(message)
    print(f"✅ CA extraction: {len(cas)} CAs found")
    if cas:
        print(f"   CA: {cas[0]}")
        expected_ca = "9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk"
        if cas[0] == expected_ca:
            print("   ✅ Correct CA extracted")
        else:
            print(f"   ❌ Expected {expected_ca}, got {cas[0]}")
    
    # Test format detection
    is_activity_tracker = parser._is_activity_tracker_format(message)
    print(f"✅ Activity Tracker detection: {is_activity_tracker}")
    
    # Test PNL data extraction
    pnl_data = parser.extract_pnl_data(message)
    print(f"✅ PNL extraction:")
    print(f"   Multiplier: {pnl_data.get('multiplier')}")
    print(f"   Price: {pnl_data.get('price_price')}")
    print(f"   MC: {pnl_data.get('mc_price')}")
    
    # Test signal detection
    is_signal = parser.is_signal_message(message)
    print(f"✅ Signal detection: {is_signal}")
    
    # Test second format
    print("\n🧪 Testing second format...")
    
    message2 = """🔥 ACTIVITY DETECTED 🔥

├ $JOHNBUBU
├ 9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk
└| ⏳ 32m | 👁️ 1.6K

📊 Token details
├ PRICE:    $0.00065307
├ MC:       $653.2K /23.9X from VIP/
├ Vol:      $1.54M


🔒 Security
├ Dev S:    🟢
├ Dex P:    🟢
├ Risk:     only available for Premium users
├ Bundled:  only available for Premium users


🤖 Bots
└ only available for premium users

📈 Charts + Exchanges
└ only available for premium users
├ Axiom (https://axiom.trade/t/9SaKCMUd5D1uE39RkasJw7UtEtRTaBWykQvFQbgcbonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

💰 JOIN PREMIUM  TO CATCH THE NEXT 23.9X! 🔥🚀"""
    
    cas2 = parser.extract_contract_addresses(message2)
    pnl_data2 = parser.extract_pnl_data(message2)
    
    print(f"✅ Second format CA extraction: {len(cas2)} CAs found")
    print(f"✅ Second format multiplier: {pnl_data2.get('multiplier')}")
    
    return True

def test_configuration():
    """Test updated configuration."""
    print("\n🧪 Testing Updated Configuration...")
    
    try:
        from config import config
        print("✅ Configuration loaded successfully!")
        print(f"Primary group: {config.target_group.group_name} ({config.target_group.group_id})")
        print(f"Additional groups: {len(config.target_group.additional_group_ids)}")
        for i, group_id in enumerate(config.target_group.additional_group_ids):
            group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {group_id}"
            print(f"  - {group_name} ({group_id})")
        print(f"Total groups monitored: {len(config.target_group.all_group_ids)}")
        
    except Exception as e:
        print(f"❌ Configuration failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🚀 CLA v2.0 Bot - Solana Activity Tracker Integration Test\n")
    
    try:
        # Test configuration
        if not test_configuration():
            return False
        
        # Test message parsing
        if not test_activity_tracker_parsing():
            return False
        
        print("\n🎉 All tests passed!")
        print("\n📋 Summary:")
        print("✅ Configuration supports multiple groups")
        print("✅ Solana Activity Tracker format detection")
        print("✅ CA extraction from tree format")
        print("✅ Multiplier extraction from VIP format")
        print("✅ Axiom URL parsing")
        print("✅ Signal detection")
        
        print("\n🔧 The bot will now monitor:")
        print("  - FREE WHALE SIGNALS (-1002380594298)")
        print("  - Solana Activity Tracker (-1002270988204)")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
