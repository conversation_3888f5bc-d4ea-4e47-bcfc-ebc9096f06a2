"""Tests for message parsing functionality."""

import unittest
import asyncio
import os
from src.message_parser import MessageParser

# Set test environment variables to avoid config validation errors
os.environ.setdefault('TELEGRAM_API_ID', '12345')
os.environ.setdefault('TELEGRAM_API_HASH', 'test_hash')
os.environ.setdefault('TELEGRAM_PHONE', '+1234567890')

class TestMessageParser(unittest.TestCase):
    """Test cases for MessageParser class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.parser = MessageParser()
    
    def test_extract_standalone_ca(self):
        """Test extraction of standalone contract addresses."""
        # Test message with CA at the end
        message = """🔥 NEW SIGNAL ALERT 🔥
        
Check this out on pump.fun!
        
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 1)
        self.assertEqual(cas[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")
    
    def test_extract_ca_from_dexscreener_url(self):
        """Test extraction from dexscreener URL."""
        message = """🚨 PUMP ALERT 🚨
        
https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
        
Get in now!"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 1)
        self.assertEqual(cas[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")
    
    def test_extract_ca_from_pump_fun_url(self):
        """Test extraction from pump.fun URL."""
        message = """💸 MOON SHOT 💸
        
https://pump.fun/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
        
LFG!"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 1)
        self.assertEqual(cas[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")
    
    def test_extract_ca_from_solscan_url(self):
        """Test extraction from solscan URL."""
        message = """🛎️ SIGNAL UPDATE 🛎️
        
https://solscan.io/token/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
        
Check the chart!"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 1)
        self.assertEqual(cas[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")
    
    def test_extract_multiple_cas(self):
        """Test extraction of multiple CAs from one message."""
        message = """🔥 MULTIPLE SIGNALS 🔥
        
First: https://dexscreener.com/solana/67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk
Second: https://pump.fun/8sAXHqjJkwjKqYrKtXkKZjZjZjZjZjZjZjZjZjZjZjZj
        
Both are pumping!"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 2)
        self.assertIn("67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk", cas)
        self.assertIn("8sAXHqjJkwjKqYrKtXkKZjZjZjZjZjZjZjZjZjZjZjZj", cas)
    

    
    def test_is_signal_message(self):
        """Test signal message detection."""
        signal_message = """🔥 NEW SIGNAL 🔥
        
Pump incoming!
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        non_signal_message = """Just chatting about the weather today.
Nothing special happening."""
        
        self.assertTrue(self.parser.is_signal_message(signal_message))
        self.assertFalse(self.parser.is_signal_message(non_signal_message))
    
    def test_is_update_message(self):
        """Test update message detection."""
        update_message = """🎉 UPDATE 🎉
        
Signal is up 2.3X!
Now at: $0.115k"""
        
        regular_message = """Hello everyone!
How is your day going?"""
        
        self.assertTrue(self.parser.is_update_message(update_message))
        self.assertFalse(self.parser.is_update_message(regular_message))
    
    def test_invalid_ca_rejection(self):
        """Test that invalid CAs are rejected."""
        # Too short
        message1 = "Check this: 123456789"
        
        # Too long
        message2 = "Check this: 67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk123"
        
        # Invalid characters
        message3 = "Check this: 67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjyb0nk"
        
        self.assertEqual(len(self.parser.extract_contract_addresses(message1)), 0)
        self.assertEqual(len(self.parser.extract_contract_addresses(message2)), 0)
        self.assertEqual(len(self.parser.extract_contract_addresses(message3)), 0)
    
    def test_emoji_cleaning(self):
        """Test that emojis are properly handled."""
        message = """🔥🚨🛎️💸🥵🎉 SIGNAL ALERT 🔥🚨🛎️💸🥵🎉
        
67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk"""
        
        cas = self.parser.extract_contract_addresses(message)
        self.assertEqual(len(cas), 1)
        self.assertEqual(cas[0], "67Gcb2zHQZKAv7kLXhNkDQRvP6MYW6MZRAcahKjybonk")

if __name__ == '__main__':
    unittest.main()
