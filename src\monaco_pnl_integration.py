"""Monaco PNL channel integration for CLA v2.0 Bot."""

import asyncio
from typing import Optional, List
from datetime import datetime
from loguru import logger
from telethon.tl.types import ChannelParticipantAdmin, ChannelParticipantCreator
from telethon.errors import Chat<PERSON>dminRequired<PERSON><PERSON><PERSON>, UserNotParticipantError

from src.telegram_client import TelegramClientManager
from config import config

class MonacoPNLIntegration:
    """Handles integration with Monaco PNL channel for CA forwarding."""
    
    def __init__(self, telegram_client: TelegramClientManager):
        self.telegram_client = telegram_client
        self.monaco_pnl_entity = None
        self.message_queue = asyncio.Queue()
        self.processing_queue = False

        # Queue-level duplicate prevention
        self.queued_cas = set()  # Track CAs currently in queue
        self.recent_sent_cas = {}  # CA -> timestamp for recent sends

        # Admin identity configuration
        self.admin_permissions_verified = False
        self.admin_identity_available = False
        self.use_admin_identity = True  # Flag to enable admin identity forwarding
    
    async def initialize(self):
        """Initialize Monaco PNL channel integration."""
        try:
            # Try to get Monaco PNL channel entity by chat ID first
            try:
                self.monaco_pnl_entity = await self.telegram_client.get_entity(config.monaco_pnl.group_id)
            except:
                # If chat ID fails, try username
                if config.monaco_pnl.username:
                    self.monaco_pnl_entity = await self.telegram_client.get_entity(config.monaco_pnl.username)
            
            if not self.monaco_pnl_entity:
                logger.error(f"Failed to find Monaco PNL channel: {config.monaco_pnl.group_id} / {config.monaco_pnl.username}")
                return False
            
            logger.info(f"Monaco PNL integration initialized: {config.monaco_pnl.group_name} ({config.monaco_pnl.group_id})")

            # Verify admin permissions for admin identity forwarding
            await self._verify_admin_permissions()

            # Start message queue processor
            asyncio.create_task(self._process_message_queue())

            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Monaco PNL integration: {e}")
            return False

    async def _verify_admin_permissions(self):
        """Verify if the current user has admin permissions in Monaco PNL channel."""
        try:
            if not self.monaco_pnl_entity:
                logger.warning("Monaco PNL entity not available for admin verification")
                return False

            # Get current user
            me = await self.telegram_client.get_me()
            if not me:
                logger.warning("Could not get current user for admin verification")
                return False

            # Check if user is participant and get permissions
            try:
                participant = await self.telegram_client.get_permissions(
                    self.monaco_pnl_entity, me.id
                )

                # Check if user is admin or creator
                if participant.is_admin or participant.is_creator:
                    self.admin_permissions_verified = True
                    self.admin_identity_available = True
                    logger.info(f"✅ Admin permissions verified for Monaco PNL channel (Admin: {participant.is_admin}, Creator: {participant.is_creator})")
                    return True
                else:
                    logger.warning(f"⚠️ No admin permissions in Monaco PNL channel - using regular identity")
                    self.admin_permissions_verified = False
                    self.admin_identity_available = False
                    return False

            except (ChatAdminRequiredError, UserNotParticipantError) as e:
                logger.warning(f"⚠️ Cannot verify admin permissions in Monaco PNL channel: {e}")
                self.admin_permissions_verified = False
                self.admin_identity_available = False
                return False

        except Exception as e:
            logger.error(f"Error verifying admin permissions for Monaco PNL: {e}")
            self.admin_permissions_verified = False
            self.admin_identity_available = False
            return False

    async def get_admin_status_report(self) -> str:
        """Get a detailed report of admin status for Monaco PNL channel."""
        try:
            if not self.monaco_pnl_entity:
                return "❌ Monaco PNL entity not initialized"

            # Get current user
            me = await self.telegram_client.get_me()
            if not me:
                return "❌ Could not get current user information"

            # Check permissions
            try:
                participant = await self.telegram_client.get_permissions(
                    self.monaco_pnl_entity, me.id
                )

                status_parts = [
                    f"👤 User: {me.first_name} (@{me.username})",
                    f"🏢 Channel: {config.monaco_pnl.group_name}",
                    f"🆔 Channel ID: {config.monaco_pnl.group_id}",
                    f"👑 Is Admin: {'✅' if participant.is_admin else '❌'}",
                    f"🔱 Is Creator: {'✅' if participant.is_creator else '❌'}",
                    f"📝 Can Send Messages: {'✅' if participant.send_messages else '❌'}",
                    f"🔧 Admin Identity Available: {'✅' if self.admin_identity_available else '❌'}",
                    f"✅ Admin Permissions Verified: {'✅' if self.admin_permissions_verified else '❌'}"
                ]

                return "\n".join(status_parts)

            except Exception as perm_error:
                return f"❌ Permission check failed: {perm_error}"

        except Exception as e:
            return f"❌ Error generating admin status report: {e}"

    async def test_admin_message_sending(self) -> bool:
        """Test if admin message sending is working properly."""
        try:
            if not self.monaco_pnl_entity:
                logger.error("Monaco PNL entity not available for testing")
                return False

            if not self.admin_identity_available:
                logger.warning("Admin identity not available - testing regular message sending")

            # Create a test message
            test_message = f"🧪 Admin Identity Test - {datetime.now().strftime('%H:%M:%S')}"

            # Test message sending
            if self.use_admin_identity and self.admin_identity_available:
                success = await self.telegram_client.send_message_as_admin(
                    self.monaco_pnl_entity,
                    test_message
                )
                if success:
                    logger.info("✅ Admin identity test message sent successfully")
                    return True
                else:
                    logger.warning("❌ Admin identity test message failed")
                    return False
            else:
                success = await self.telegram_client.send_message(
                    self.monaco_pnl_entity,
                    test_message
                )
                if success:
                    logger.info("✅ Regular identity test message sent successfully")
                    return True
                else:
                    logger.warning("❌ Regular identity test message failed")
                    return False

        except Exception as e:
            logger.error(f"Error testing admin message sending: {e}")
            return False
    
    async def send_ca_to_monaco_pnl(self, ca: str, source_message: str = "", source_group: str = "") -> bool:
        """Send contract address to Monaco PNL channel."""
        try:
            # Format message for Monaco PNL channel
            formatted_message = self._format_ca_message(ca, source_message, source_group)
            
            # Add to queue for processing
            await self.message_queue.put({
                'ca': ca,
                'message': formatted_message,
                'timestamp': datetime.now(),
                'source': source_message[:100] if source_message else "",
                'source_group': source_group
            })
            
            logger.info(f"CA queued for Monaco PNL channel: {ca}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue CA for Monaco PNL channel: {e}")
            return False
    
    def _format_ca_message(self, ca: str, source_message: str = "", source_group: str = "") -> str:
        """Format contract address message for Monaco PNL channel - concise format."""
        # Concise format: just the CA
        return ca
    
    async def _process_message_queue(self):
        """Process queued messages to Monaco PNL channel."""
        self.processing_queue = True
        logger.info("Started Monaco PNL channel message queue processor")
        
        while self.processing_queue:
            try:
                # Get message from queue (wait up to 1 second)
                try:
                    message_data = await asyncio.wait_for(
                        self.message_queue.get(), 
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue
                
                # Send message to Monaco PNL channel
                success = await self._send_message_to_monaco_pnl(message_data)
                
                if success:
                    logger.info(f"Successfully sent CA to Monaco PNL channel: {message_data['ca']}")
                else:
                    logger.error(f"Failed to send CA to Monaco PNL channel: {message_data['ca']}")
                
                # Rate limiting - wait between messages
                await asyncio.sleep(2)  # 2 second delay between messages
                
            except Exception as e:
                logger.error(f"Error in Monaco PNL message queue processor: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def _send_message_to_monaco_pnl(self, message_data: dict) -> bool:
        """Send individual message to Monaco PNL channel with admin identity if available."""
        try:
            if not self.monaco_pnl_entity:
                logger.error("Monaco PNL channel entity not initialized")
                return False

            # Check admin permissions before sending
            if self.use_admin_identity and not self.admin_permissions_verified:
                logger.warning("Admin identity requested but not verified - attempting re-verification")
                await self._verify_admin_permissions()

            # Send the message with admin identity if available
            if self.use_admin_identity and self.admin_identity_available:
                try:
                    # Send message with admin privileges
                    success = await self.telegram_client.send_message_as_admin(
                        self.monaco_pnl_entity,
                        message_data['message']
                    )

                    if success:
                        logger.debug(f"✅ Message sent to Monaco PNL channel as Admin: {message_data['message'][:50]}...")
                        return True
                    else:
                        logger.warning("Failed to send as admin, falling back to regular identity")

                except ChatAdminRequiredError:
                    logger.warning("Admin privileges required but not available, falling back to regular identity")
                    self.admin_identity_available = False
                except Exception as admin_error:
                    logger.warning(f"Admin identity send failed: {admin_error}, falling back to regular identity")

            # Fallback to regular identity
            success = await self.telegram_client.send_message(
                self.monaco_pnl_entity,
                message_data['message']
            )

            if success:
                identity_type = "Admin" if (self.use_admin_identity and self.admin_identity_available) else "Bot"
                logger.debug(f"Message sent to Monaco PNL channel as {identity_type}: {message_data['message'][:50]}...")

            return success

        except Exception as e:
            logger.error(f"Error sending message to Monaco PNL channel: {e}")
            return False
    
    async def send_multiple_cas(self, cas: List[str], source_message: str = "", source_group: str = "") -> int:
        """Send multiple contract addresses to Monaco PNL channel with optimized performance."""
        success_count = 0

        for ca in cas:
            if await self.send_ca_to_monaco_pnl(ca, source_message, source_group):
                success_count += 1

            # PERFORMANCE OPTIMIZATION: Reduced delay from 500ms to 50ms
            # Queue-based system doesn't need aggressive rate limiting
            await asyncio.sleep(0.05)  # 50ms instead of 500ms

        logger.info(f"Queued {success_count}/{len(cas)} CAs for Monaco PNL channel")
        return success_count
    
    async def test_monaco_pnl_connection(self) -> bool:
        """Test connection to Monaco PNL channel."""
        try:
            if not self.monaco_pnl_entity:
                logger.error("Monaco PNL channel entity not initialized")
                return False
            
            logger.info("Monaco PNL channel connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Monaco PNL channel connection test error: {e}")
            return False
    
    async def get_queue_status(self) -> dict:
        """Get status of the message queue."""
        return {
            'queue_size': self.message_queue.qsize(),
            'processing': self.processing_queue,
            'monaco_pnl_connected': self.monaco_pnl_entity is not None
        }
    
    async def clear_queue(self):
        """Clear the message queue."""
        try:
            while not self.message_queue.empty():
                await self.message_queue.get()
            logger.info("Monaco PNL channel message queue cleared")
        except Exception as e:
            logger.error(f"Error clearing Monaco PNL queue: {e}")
    
    async def pause_processing(self):
        """Pause message queue processing."""
        self.processing_queue = False
        logger.info("Monaco PNL channel message processing paused")
    
    async def resume_processing(self):
        """Resume message queue processing."""
        if not self.processing_queue:
            self.processing_queue = True
            asyncio.create_task(self._process_message_queue())
            logger.info("Monaco PNL channel message processing resumed")
    
    async def send_custom_message(self, message: str) -> bool:
        """Send a custom message to Monaco PNL channel."""
        try:
            if not self.monaco_pnl_entity:
                logger.error("Monaco PNL channel entity not initialized")
                return False
            
            success = await self.telegram_client.send_message(
                self.monaco_pnl_entity, 
                message
            )
            
            if success:
                logger.info(f"Custom message sent to Monaco PNL channel: {message}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending custom message to Monaco PNL channel: {e}")
            return False
    
    def get_monaco_pnl_info(self) -> dict:
        """Get Monaco PNL channel configuration info."""
        return {
            'group_id': config.monaco_pnl.group_id,
            'group_name': config.monaco_pnl.group_name,
            'username': config.monaco_pnl.username,
            'entity_found': self.monaco_pnl_entity is not None,
            'queue_processing': self.processing_queue
        }
    
    async def shutdown(self):
        """Shutdown Monaco PNL channel integration."""
        logger.info("Shutting down Monaco PNL channel integration")
        self.processing_queue = False
        await self.clear_queue()
        logger.info("Monaco PNL channel integration shutdown complete")
