#!/usr/bin/env python3
"""
Analyze BUGSIE CA Extraction Failure
Investigates why GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk was not extracted
"""

import sys
import re
sys.path.append('.')

def analyze_bugsie_message():
    """Analyze the exact BUGSIE message format that failed CA extraction."""
    print("🔍 ANALYZING BUGSIE CA EXTRACTION FAILURE")
    print("=" * 70)
    
    # The exact message from BUGSIE (reconstructed from logs)
    bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472

📊 Token details
├ PRICE:    $0
├ MC:       $71.5K /3.5X from VIP/
├ Vol:      $3.2K


🔒 Security
├ Dev S:    🟢
├ Dex P:    🟢
├ Risk:     only available for Premium users
├ Bundled:  only available for Premium users


🤖 Bots
└ only available for premium users

📈 Charts + Exchanges
└ only available for premium users
├ Axiom (https://axiom.trade/t/GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk/@bugsie)
├ Don't have Axiom? Sign up for 10% off fees 🚀 (https://axiom.trade/@bugsie)

💰 JOIN PREMIUM  TO CATCH THE NEXT 3.5X! 🔥🚀"""
    
    # The expected CA
    expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
    
    print(f"Expected CA: {expected_ca}")
    print(f"CA Length: {len(expected_ca)} characters")
    print(f"Message Length: {len(bugsie_message)} characters")
    print()
    
    # Analyze CA format
    print("📊 CA FORMAT ANALYSIS")
    print("-" * 40)
    print(f"Starts with: {expected_ca[:5]}")
    print(f"Ends with: {expected_ca[-5:]}")
    print(f"Contains 'bonk': {'bonk' in expected_ca.lower()}")
    print()
    
    # Test base58 validation
    print("🧪 BASE58 VALIDATION TEST")
    print("-" * 40)
    
    try:
        import base58
        decoded = base58.b58decode(expected_ca)
        print(f"✅ Base58 decode successful: {len(decoded)} bytes")
        print(f"Valid 32-byte format: {len(decoded) == 32}")
        print(f"Not all zeros: {decoded != b'\\x00' * 32}")
    except Exception as e:
        print(f"❌ Base58 decode failed: {e}")
    
    print()
    
    # Test current message parser
    print("🧪 CURRENT MESSAGE PARSER TEST")
    print("-" * 40)
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test extraction
        extracted_cas = parser.extract_contract_addresses(bugsie_message)
        print(f"Extracted CAs: {extracted_cas}")
        
        if expected_ca in extracted_cas:
            print("✅ CA successfully extracted")
        else:
            print("❌ CA extraction failed - investigating...")
            
            # Test individual components
            print("\\nComponent analysis:")
            
            # Test standalone extraction
            cleaned_text = parser._clean_message_text_no_url_extraction(bugsie_message)
            print(f"Cleaned text (first 200 chars): {cleaned_text[:200]}...")
            
            standalone_cas = parser._extract_standalone_cas(cleaned_text)
            print(f"Standalone CAs: {standalone_cas}")
            
            # Test URL extraction
            url_cas = parser._extract_cas_from_urls(bugsie_message)
            print(f"URL CAs: {url_cas}")
            
            # Test validation of expected CA
            is_valid = parser._validate_solana_ca(expected_ca)
            print(f"CA validation: {is_valid}")
            
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
        import traceback
        traceback.print_exc()
    
    return bugsie_message, expected_ca

def test_regex_patterns():
    """Test regex patterns against the BUGSIE message format."""
    print("\\n🧪 REGEX PATTERN TESTING")
    print("=" * 70)
    
    bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472"""
    
    expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
    
    # Test different regex patterns
    patterns = {
        "Standard CA Pattern": r'\\b[1-9A-HJ-NP-Za-km-z]{43,44}\\b',
        "Relaxed CA Pattern": r'[1-9A-HJ-NP-Za-km-z]{43,44}',
        "Line-based CA Pattern": r'^.*([1-9A-HJ-NP-Za-km-z]{43,44}).*$',
        "Tree Symbol Pattern": r'├.*([1-9A-HJ-NP-Za-km-z]{43,44})',
        "Axiom URL Pattern": r'axiom\\.trade/t/([1-9A-HJ-NP-Za-km-z]{43,44})',
    }
    
    print(f"Testing patterns against message with CA: {expected_ca}")
    print()
    
    for pattern_name, pattern in patterns.items():
        print(f"Testing {pattern_name}:")
        print(f"Pattern: {pattern}")
        
        try:
            matches = re.findall(pattern, bugsie_message, re.MULTILINE)
            print(f"Matches: {matches}")
            
            if expected_ca in matches or any(expected_ca in match for match in matches if isinstance(match, str)):
                print("✅ Pattern successfully matches expected CA")
            else:
                print("❌ Pattern does not match expected CA")
                
        except Exception as e:
            print(f"❌ Pattern error: {e}")
        
        print("-" * 50)

def test_message_cleaning():
    """Test message cleaning process that might affect CA extraction."""
    print("\\n🧪 MESSAGE CLEANING ANALYSIS")
    print("=" * 70)
    
    bugsie_message = """🔥 ACTIVITY DETECTED 🔥

├ $BOOP
├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk
└| ⏳ 25m | 👁️ 472"""
    
    expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
    
    print("Original message:")
    print(bugsie_message)
    print()
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        # Test cleaning process
        cleaned = parser._clean_message_text_no_url_extraction(bugsie_message)
        print("After cleaning:")
        print(cleaned)
        print()
        
        # Check if CA is still present after cleaning
        if expected_ca in cleaned:
            print("✅ CA preserved after cleaning")
        else:
            print("❌ CA lost during cleaning process")
            
            # Check what happened to the CA
            ca_line = "├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
            if ca_line in bugsie_message:
                print(f"Original line: {ca_line}")
                
                # Test cleaning on just this line
                cleaned_line = parser._clean_message_text_no_url_extraction(ca_line)
                print(f"Cleaned line: {cleaned_line}")
                
                if expected_ca in cleaned_line:
                    print("✅ CA preserved in isolated line cleaning")
                else:
                    print("❌ CA lost in isolated line cleaning")
        
    except Exception as e:
        print(f"❌ Cleaning test failed: {e}")

def test_tree_symbol_handling():
    """Test handling of tree symbols (├, └) that might interfere with extraction."""
    print("\\n🧪 TREE SYMBOL HANDLING TEST")
    print("=" * 70)
    
    test_cases = [
        "├ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
        "└ GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk", 
        "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
        "CA: GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk",
        "Token GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk here"
    ]
    
    expected_ca = "GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk"
    
    try:
        from src.message_parser import MessageParser
        parser = MessageParser()
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"Test {i}: {test_case}")
            
            extracted = parser.extract_contract_addresses(test_case)
            print(f"Extracted: {extracted}")
            
            if expected_ca in extracted:
                print("✅ SUCCESS")
            else:
                print("❌ FAILED")
            
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ Tree symbol test failed: {e}")

def create_improved_extraction():
    """Create improved extraction logic for BUGSIE-style messages."""
    print("\\n🔧 IMPROVED EXTRACTION LOGIC")
    print("=" * 70)
    
    improved_code = '''
def _extract_standalone_cas_improved(self, text: str) -> List[str]:
    """Improved standalone CA extraction for tree-formatted messages."""
    cas = []
    
    # Standard pattern (existing)
    standard_pattern = r'\\b[1-9A-HJ-NP-Za-km-z]{43,44}\\b'
    standard_matches = re.findall(standard_pattern, text)
    cas.extend(standard_matches)
    
    # Tree symbol pattern (new for BUGSIE-style messages)
    tree_pattern = r'[├└]\\s*([1-9A-HJ-NP-Za-km-z]{43,44})'
    tree_matches = re.findall(tree_pattern, text)
    cas.extend(tree_matches)
    
    # Line-based pattern (new for structured messages)
    line_pattern = r'^.*?([1-9A-HJ-NP-Za-km-z]{43,44}).*?$'
    for line in text.split('\\n'):
        line_matches = re.findall(line_pattern, line.strip())
        cas.extend(line_matches)
    
    # Remove duplicates and validate
    unique_cas = []
    for ca in cas:
        if ca not in unique_cas and self._validate_solana_ca(ca):
            unique_cas.append(ca)
    
    return unique_cas
'''
    
    print("Improved extraction logic includes:")
    print("✅ Standard word boundary pattern (existing)")
    print("✅ Tree symbol pattern for ├ and └ prefixes")
    print("✅ Line-based pattern for structured messages")
    print("✅ Duplicate removal and validation")
    
    return improved_code

def main():
    """Run comprehensive BUGSIE CA extraction failure analysis."""
    print("🚀 BUGSIE CA EXTRACTION FAILURE ANALYSIS")
    print("=" * 80)
    print("Investigating: GNkqwiUd2CAHZKaxGRtU56rraqLM4WNcjT2aAML9bonk")
    print("=" * 80)
    
    # Run all analysis functions
    bugsie_message, expected_ca = analyze_bugsie_message()
    test_regex_patterns()
    test_message_cleaning()
    test_tree_symbol_handling()
    improved_code = create_improved_extraction()
    
    print("\\n🎯 ANALYSIS SUMMARY")
    print("=" * 80)
    print("Key findings from the analysis:")
    print("1. Check if tree symbols (├) interfere with word boundary regex")
    print("2. Verify message cleaning doesn't remove the CA")
    print("3. Test if the CA validation passes for this specific address")
    print("4. Implement improved extraction for structured message formats")
    print("=" * 80)

if __name__ == "__main__":
    main()
