"""Test configuration values to verify anti-pump settings."""

import sys
import os

# Add src to path
sys.path.insert(0, 'src')

def test_config_values():
    """Test the current configuration values."""
    print("🧪 Testing Configuration Values...")
    
    try:
        # Import config
        from config import config
        
        print(f"\n📋 Current Trending Configuration:")
        print(f"   Enabled: {config.trending.enabled}")
        print(f"   Time Window: {config.trending.time_window_minutes} minutes")
        print(f"   Min Mentions: {config.trending.min_mentions}")
        
        print(f"\n🛡️ Anti-Pump Protection Settings:")
        print(f"   Min Time Spread: {config.trending.min_time_spread_seconds} seconds")
        print(f"   Max Velocity: {config.trending.max_velocity_mentions_per_minute} mentions/minute")
        print(f"   Min Organic Growth: {config.trending.min_organic_growth_minutes} minutes")
        print(f"   Require Time Distribution: {config.trending.require_time_distribution}")
        print(f"   Pump Detection Enabled: {config.trending.pump_detection_enabled}")
        
        print(f"\n🌍 Environment Variables:")
        print(f"   TRENDING_TIME_WINDOW_MINUTES: {os.getenv('TRENDING_TIME_WINDOW_MINUTES', 'NOT SET')}")
        print(f"   TRENDING_MIN_MENTIONS: {os.getenv('TRENDING_MIN_MENTIONS', 'NOT SET')}")
        print(f"   TRENDING_MIN_TIME_SPREAD_SECONDS: {os.getenv('TRENDING_MIN_TIME_SPREAD_SECONDS', 'NOT SET')}")
        print(f"   TRENDING_MAX_VELOCITY: {os.getenv('TRENDING_MAX_VELOCITY', 'NOT SET')}")
        print(f"   TRENDING_MIN_ORGANIC_MINUTES: {os.getenv('TRENDING_MIN_ORGANIC_MINUTES', 'NOT SET')}")
        
        # Check if values match expected enhanced configuration
        expected_values = {
            'time_window_minutes': 8,
            'min_mentions': 6,
            'min_time_spread_seconds': 120,
            'max_velocity_mentions_per_minute': 3.0,
            'min_organic_growth_minutes': 3
        }
        
        print(f"\n✅ Configuration Validation:")
        all_correct = True
        
        for key, expected in expected_values.items():
            actual = getattr(config.trending, key)
            status = "✅" if actual == expected else "❌"
            print(f"   {status} {key}: {actual} (expected: {expected})")
            if actual != expected:
                all_correct = False
        
        if all_correct:
            print(f"\n🎉 All configuration values are correct!")
        else:
            print(f"\n🚨 Some configuration values need updating!")
            
        return all_correct
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_config_values()
    sys.exit(0 if success else 1)
