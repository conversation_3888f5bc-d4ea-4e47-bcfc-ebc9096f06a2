"""Debug script to verify group configuration and active group IDs."""

import sys
sys.path.insert(0, 'src')

def debug_group_configuration():
    """Debug group configuration to verify GMGN is properly included."""
    print("🔍 Debugging Group Configuration...")
    
    try:
        from config import config
        
        print(f"\n📊 Group Configuration Analysis:")
        print(f"Primary Group ID: {config.target_group.group_id}")
        print(f"Primary Group Name: {config.target_group.group_name}")
        
        print(f"\nAdditional Groups ({len(config.target_group.additional_group_ids)}):")
        for i, group_id in enumerate(config.target_group.additional_group_ids):
            group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {group_id}"
            status = config.target_group.group_status.get(group_id, 'UNKNOWN')
            print(f"  {i+1}. {group_name} ({group_id}) [{status}]")
        
        print(f"\n🎯 All Group IDs: {config.target_group.all_group_ids}")
        print(f"🟢 Active Group IDs: {config.target_group.active_group_ids}")
        
        # Check GMGN specifically
        gmgn_id = -1002202241417
        print(f"\n🧠 GMGN Analysis:")
        print(f"GMGN ID: {gmgn_id}")
        print(f"In all_group_ids: {gmgn_id in config.target_group.all_group_ids}")
        print(f"In active_group_ids: {gmgn_id in config.target_group.active_group_ids}")
        
        if gmgn_id in config.target_group.group_status:
            print(f"GMGN Status: {config.target_group.group_status[gmgn_id]}")
        else:
            print("GMGN Status: NOT FOUND in group_status")
        
        # Check group status mapping
        print(f"\n📋 Group Status Mapping:")
        for group_id, status in config.target_group.group_status.items():
            group_name = "PRIMARY" if group_id == config.target_group.group_id else "UNKNOWN"
            for i, gid in enumerate(config.target_group.additional_group_ids):
                if gid == group_id:
                    group_name = config.target_group.additional_group_names[i] if i < len(config.target_group.additional_group_names) else f"Group {gid}"
                    break
            print(f"  {group_name} ({group_id}): {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Group configuration debug failed: {e}")
        return False

def debug_telegram_client_setup():
    """Debug telegram client message handler setup."""
    print("\n🔍 Debugging Telegram Client Setup...")
    
    try:
        from src.telegram_client import TelegramClientManager
        from config import config
        
        # Check active group IDs that would be passed to message handler
        active_groups = config.target_group.active_group_ids
        print(f"Active groups for message handler: {active_groups}")
        
        # Verify GMGN is in the list
        gmgn_id = -1002202241417
        if gmgn_id in active_groups:
            print(f"✅ GMGN ({gmgn_id}) is in active_group_ids")
        else:
            print(f"❌ GMGN ({gmgn_id}) is NOT in active_group_ids")
            print(f"This means the message handler won't listen to GMGN!")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram client debug failed: {e}")
        return False

def main():
    """Run all debug checks."""
    print("🚨 CRITICAL DEBUG: GMGN Message Reception Issue\n")
    
    success = True
    
    if not debug_group_configuration():
        success = False
    
    if not debug_telegram_client_setup():
        success = False
    
    if success:
        print("\n🎯 Debug Summary:")
        print("All configuration checks completed.")
        print("Check the output above for any issues with GMGN group configuration.")
    else:
        print("\n❌ Debug failed - configuration issues detected")
    
    return success

if __name__ == "__main__":
    main()
