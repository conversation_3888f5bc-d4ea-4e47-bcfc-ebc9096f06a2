#!/usr/bin/env python3
"""
Test Message Handler Registration
Tests if the message handler is properly registered and receiving events
"""

import asyncio
import sys
import os
import time
from datetime import datetime

# Add project root to path
sys.path.append('.')

from config import *
from telethon import TelegramClient, events
from src.logger_setup import setup_logging

# Setup logging
logger = setup_logging()

class MessageHandlerTest:
    def __init__(self):
        self.message_count = 0
        self.handler_called = False
        
    async def test_handler(self, event):
        """Test message handler"""
        self.handler_called = True
        self.message_count += 1
        
        group_name = "Unknown"
        try:
            chat = await event.get_chat()
            group_name = getattr(chat, 'title', f'Group {event.chat_id}')
        except:
            pass
        
        message_text = event.message.text or "[Media/Sticker]"
        preview = message_text[:50] + "..." if len(message_text) > 50 else message_text
        
        print(f"🎯 HANDLER CALLED! Message #{self.message_count} from {group_name}: {preview}")
        logger.info(f"🎯 TEST HANDLER: Message received from {group_name} ({event.chat_id}): {preview}")

async def test_message_handler_registration():
    """Test message handler registration"""
    print("=" * 80)
    print("🔧 MESSAGE HANDLER REGISTRATION TEST")
    print("=" * 80)
    
    test_instance = MessageHandlerTest()
    
    try:
        # Create Telegram client
        telegram_config = TelegramConfig()
        client = TelegramClient(
            telegram_config.session_name,
            telegram_config.api_id,
            telegram_config.api_hash
        )
        
        print("📱 Connecting to Telegram...")
        await client.start()
        
        me = await client.get_me()
        print(f"✅ Connected as: {me.first_name} (@{me.username})")
        
        # Get groups to monitor (same as main bot)
        target_group_config = TargetGroupConfig()
        groups_to_test = [target_group_config.group_id] + target_group_config.additional_group_ids
        
        print(f"\n🔧 Registering message handler for {len(groups_to_test)} groups:")
        for group_id in groups_to_test:
            try:
                entity = await client.get_entity(group_id)
                group_name = getattr(entity, 'title', f'Group {group_id}')
                print(f"   - {group_name} ({group_id})")
            except Exception as e:
                print(f"   - Group {group_id} - ERROR: {e}")
        
        # Register message handler (same pattern as main bot)
        print("\n🎯 Registering message handler...")
        
        @client.on(events.NewMessage(chats=groups_to_test))
        async def message_handler_wrapper(event):
            """Message handler wrapper (same as main bot)"""
            try:
                await test_instance.test_handler(event)
            except Exception as e:
                print(f"❌ Error in message handler: {e}")
                logger.error(f"Error in test message handler: {e}")
        
        print("✅ Message handler registered")
        print("⏱️ Listening for 30 seconds...")
        print("💡 Send a test message in any monitored group")
        print("-" * 80)
        
        # Listen for messages
        start_time = time.time()
        await asyncio.sleep(30)
        
        print("-" * 80)
        print("📊 TEST RESULTS:")
        print(f"   Handler called: {'YES' if test_instance.handler_called else 'NO'}")
        print(f"   Messages received: {test_instance.message_count}")
        print(f"   Test duration: 30 seconds")
        
        if test_instance.handler_called:
            print("✅ MESSAGE HANDLER REGISTRATION: WORKING")
            print("   The message handler is properly registered and receiving events")
        else:
            print("❌ MESSAGE HANDLER REGISTRATION: FAILED")
            print("   The message handler is not receiving events")
            print("   Possible issues:")
            print("   1. Event handler not properly registered")
            print("   2. Group IDs incorrect")
            print("   3. Telethon version compatibility issue")
            print("   4. Session permissions issue")
        
        await client.disconnect()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 80)
    print("🎉 TEST COMPLETE")
    print("=" * 80)

if __name__ == "__main__":
    asyncio.run(test_message_handler_registration())
