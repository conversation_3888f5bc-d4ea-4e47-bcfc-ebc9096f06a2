# AWS Infrastructure Setup Guide - CLA v2.0 Telegram Bot

## 🚀 **STEP-BY-STEP AWS EC2 SETUP**

This guide provides detailed instructions for setting up AWS infrastructure for your Telegram bot deployment.

---

## 📋 **PREREQUISITES**

### **Required Tools**
- ✅ AWS Account with Free Tier access
- ✅ AWS CLI installed locally
- ✅ SSH client (PuTTY for Windows, Terminal for Mac/Linux)
- ✅ Your current public IP address

### **Before You Start**
1. **Check Your IP Address**: Visit https://whatismyipaddress.com/
2. **Prepare SSH Key Name**: Choose a name like `cla-bot-key`
3. **Choose AWS Region**: Recommend `us-east-1` (N. Virginia) for free tier

---

## 🔑 **STEP 1: CREATE SSH KEY PAIR**

### **Option A: Using AWS Console**
1. **Navigate to EC2 Dashboard**
   - Go to https://console.aws.amazon.com/ec2/
   - Select your preferred region (e.g., us-east-1)

2. **Create Key Pair**
   ```
   EC2 Dashboard → Key Pairs → Create key pair

   Name: cla-bot-key
   Key pair type: RSA
   Private key file format: .pem (for OpenSSH)

   Click "Create key pair"
   ```

3. **Secure Your Key**
   - Download will start automatically
   - Move to secure location: `~/.ssh/cla-bot-key.pem`
   - Set permissions: `chmod 400 ~/.ssh/cla-bot-key.pem`

### **Option B: Using AWS CLI**
```bash
# Create key pair
aws ec2 create-key-pair \
    --key-name cla-bot-key \
    --query 'KeyMaterial' \
    --output text > ~/.ssh/cla-bot-key.pem

# Set secure permissions
chmod 400 ~/.ssh/cla-bot-key.pem
```

---

## 🛡️ **STEP 2: CREATE SECURITY GROUP**

### **Using AWS Console**
1. **Navigate to Security Groups**
   ```
   EC2 Dashboard → Security Groups → Create security group
   ```

2. **Basic Configuration**
   ```
   Security group name: cla-bot-sg
   Description: Security group for CLA v2.0 Telegram Bot
   VPC: Default VPC (vpc-xxxxxxxx)
   ```

3. **Configure Inbound Rules**
   ```
   Rule 1 - SSH Access:
   Type: SSH
   Protocol: TCP
   Port Range: 22
   Source: My IP (YOUR_PUBLIC_IP/32)
   Description: SSH access from my IP

   Rule 2 - Health Check:
   Type: Custom TCP
   Protocol: TCP
   Port Range: 8080
   Source: My IP (YOUR_PUBLIC_IP/32)
   Description: Bot health check endpoint

   Rule 3 - HTTPS (Optional):
   Type: HTTPS
   Protocol: TCP
   Port Range: 443
   Source: Anywhere (0.0.0.0/0)
   Description: HTTPS for webhook (if needed)
   ```

4. **Outbound Rules** (Default - Allow All)
   ```
   Type: All traffic
   Protocol: All
   Port Range: All
   Destination: 0.0.0.0/0
   ```

### **Using AWS CLI**
```bash
# Create security group
aws ec2 create-security-group \
    --group-name cla-bot-sg \
    --description "Security group for CLA v2.0 Telegram Bot"

# Get your public IP
MY_IP=$(curl -s https://checkip.amazonaws.com)

# Add SSH rule
aws ec2 authorize-security-group-ingress \
    --group-name cla-bot-sg \
    --protocol tcp \
    --port 22 \
    --cidr ${MY_IP}/32

# Add health check rule
aws ec2 authorize-security-group-ingress \
    --group-name cla-bot-sg \
    --protocol tcp \
    --port 8080 \
    --cidr ${MY_IP}/32
```

---

## 🖥️ **STEP 3: LAUNCH EC2 INSTANCE**

### **Using AWS Console**
1. **Launch Instance**
   ```
   EC2 Dashboard → Instances → Launch instances
   ```

2. **Configure Instance**
   ```
   Name: cla-bot-server

   Application and OS Images:
   - Quick Start: Ubuntu
   - Ubuntu Server 22.04 LTS (HVM), SSD Volume Type
   - Architecture: 64-bit (x86)

   Instance type: t2.micro (Free tier eligible)

   Key pair: cla-bot-key (select existing)

   Network settings:
   - VPC: Default
   - Subnet: Default (any availability zone)
   - Auto-assign public IP: Enable
   - Security group: Select existing → cla-bot-sg

   Storage:
   - Volume type: gp3 (General Purpose SSD)
   - Size: 8 GiB (free tier eligible)
   - Delete on termination: Yes
   ```

3. **Advanced Details** (Optional but Recommended)
   ```
   User data (script to run on first boot):
   ```
   ```bash
   #!/bin/bash
   apt update
   apt upgrade -y
   apt install -y htop curl wget git
   ```

4. **Review and Launch**
   - Review all settings
   - Click "Launch instance"
   - Wait for instance to reach "running" state

### **Using AWS CLI**
```bash
# Get the latest Ubuntu 22.04 AMI ID
AMI_ID=$(aws ec2 describe-images \
    --owners 099720109477 \
    --filters "Name=name,Values=ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server-*" \
    --query 'Images[*].[ImageId,CreationDate]' \
    --output text | sort -k2 -r | head -n1 | cut -f1)

# Launch instance
aws ec2 run-instances \
    --image-id $AMI_ID \
    --count 1 \
    --instance-type t2.micro \
    --key-name cla-bot-key \
    --security-groups cla-bot-sg \
    --block-device-mappings DeviceName=/dev/sda1,Ebs='{VolumeSize=8,VolumeType=gp3,DeleteOnTermination=true}' \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=cla-bot-server}]'
```

---

## 🔗 **STEP 4: CONNECT TO YOUR INSTANCE**

### **Get Instance Information**
1. **Find Your Instance**
   ```
   EC2 Dashboard → Instances → Select your instance
   ```

2. **Note Important Information**
   ```
   Instance ID: i-xxxxxxxxxxxxxxxxx
   Public IPv4 address: XX.XX.XX.XX
   Public IPv4 DNS: ec2-XX-XX-XX-XX.compute-1.amazonaws.com
   ```

### **Connect via SSH**

#### **From Linux/Mac Terminal**
```bash
# Connect to your instance
ssh -i ~/.ssh/cla-bot-key.pem ubuntu@YOUR_PUBLIC_IP

# Example:
ssh -i ~/.ssh/cla-bot-key.pem ubuntu@************
```

#### **From Windows (using PuTTY)**
1. **Convert .pem to .ppk**
   - Open PuTTYgen
   - Load your .pem file
   - Save private key as .ppk format

2. **Connect with PuTTY**
   ```
   Host Name: ubuntu@YOUR_PUBLIC_IP
   Port: 22
   Connection Type: SSH

   SSH → Auth → Private key file: Browse to your .ppk file
   ```

#### **From Windows (using Windows Subsystem for Linux)**
```bash
# If you have WSL installed
ssh -i /mnt/c/path/to/cla-bot-key.pem ubuntu@YOUR_PUBLIC_IP
```

### **First Connection**
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential tools
sudo apt install -y curl wget git htop unzip

# Verify Python installation
python3 --version  # Should show Python 3.10+

# Check available disk space
df -h

# Check memory
free -h
```

---

## 🌐 **STEP 5: CONFIGURE ELASTIC IP (OPTIONAL)**

### **Why Use Elastic IP?**
- **Static IP**: Keeps same IP address even if instance restarts
- **Free Tier**: 1 Elastic IP is free when attached to running instance
- **DNS Stability**: Easier for webhook configurations

### **Create and Attach Elastic IP**
1. **Using AWS Console**
   ```
   EC2 Dashboard → Elastic IPs → Allocate Elastic IP address

   Network Border Group: us-east-1
   Public IPv4 address pool: Amazon's pool of IPv4 addresses

   Click "Allocate"
   ```

2. **Associate with Instance**
   ```
   Select your Elastic IP → Actions → Associate Elastic IP address

   Resource type: Instance
   Instance: Select your cla-bot-server
   Private IP address: (leave default)

   Click "Associate"
   ```

3. **Using AWS CLI**
   ```bash
   # Allocate Elastic IP
   aws ec2 allocate-address --domain vpc

   # Associate with instance (replace with your instance ID and allocation ID)
   aws ec2 associate-address \
       --instance-id i-xxxxxxxxxxxxxxxxx \
       --allocation-id eipalloc-xxxxxxxxxxxxxxxxx
   ```

---

## 📊 **STEP 6: SET UP CLOUDWATCH MONITORING**

### **Enable Detailed Monitoring**
1. **Using AWS Console**
   ```
   EC2 Dashboard → Instances → Select your instance
   Actions → Monitor and troubleshoot → Manage detailed monitoring

   Enable detailed monitoring → Confirm
   ```

2. **Create CloudWatch Alarms**
   ```
   CloudWatch Dashboard → Alarms → Create alarm

   Alarm 1 - High CPU:
   Metric: EC2 → Per-Instance Metrics → CPUUtilization
   Instance: Your instance ID
   Statistic: Average
   Period: 5 minutes
   Threshold: Greater than 80%

   Alarm 2 - High Memory (requires CloudWatch agent):
   Metric: CWAgent → MemoryUtilization
   Threshold: Greater than 85%
   ```

### **Set Up Billing Alerts**
```
AWS Billing Dashboard → Billing preferences
✓ Receive Billing Alerts

CloudWatch → Alarms → Create alarm
Metric: Billing → Total Estimated Charge
Currency: USD
Threshold: Greater than $1.00 (or your preferred limit)
```

---

## 🔒 **STEP 7: BASIC SECURITY HARDENING**

### **Update SSH Configuration**
```bash
# Connect to your instance
ssh -i ~/.ssh/cla-bot-key.pem ubuntu@YOUR_PUBLIC_IP

# Edit SSH configuration
sudo nano /etc/ssh/sshd_config

# Recommended changes:
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Port 22  # Consider changing to non-standard port
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2

# Restart SSH service
sudo systemctl restart sshd
```

### **Configure UFW Firewall**
```bash
# Enable UFW
sudo ufw enable

# Allow SSH (adjust port if changed)
sudo ufw allow 22/tcp

# Allow health check port
sudo ufw allow 8080/tcp

# Check status
sudo ufw status verbose
```

### **Install Fail2Ban**
```bash
# Install fail2ban
sudo apt install -y fail2ban

# Create local configuration
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Edit configuration
sudo nano /etc/fail2ban/jail.local

# Find [sshd] section and ensure:
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600

# Start and enable fail2ban
sudo systemctl start fail2ban
sudo systemctl enable fail2ban
```

---

## ✅ **STEP 8: VERIFICATION CHECKLIST**

### **Infrastructure Verification**
- [ ] EC2 instance is running and accessible via SSH
- [ ] Security group allows SSH from your IP only
- [ ] Security group allows port 8080 for health checks
- [ ] Elastic IP is attached (if using)
- [ ] CloudWatch monitoring is enabled
- [ ] Billing alerts are configured

### **Security Verification**
- [ ] SSH key authentication is working
- [ ] Password authentication is disabled
- [ ] UFW firewall is enabled and configured
- [ ] Fail2Ban is installed and running
- [ ] System packages are updated

### **System Verification**
```bash
# Check system resources
df -h          # Disk space
free -h        # Memory
htop           # CPU and processes
uname -a       # System information

# Check network connectivity
ping -c 4 *******
curl -I https://api.telegram.org

# Check security services
sudo systemctl status ufw
sudo systemctl status fail2ban
sudo fail2ban-client status sshd
```

---

## 🎯 **NEXT STEPS**

Your AWS infrastructure is now ready! The next steps are:

1. **Prepare Code for Deployment** - Package your bot for EC2
2. **Create Deployment Scripts** - Automate the installation process
3. **Deploy and Configure Bot** - Install and start your Telegram bot
4. **Set Up Monitoring** - Configure comprehensive monitoring and alerting

Your EC2 instance is now secure, monitored, and ready for bot deployment!