"""Test selective anti-pump protection based on group activity levels."""

import asyncio
import sys
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, 'src')

async def test_selective_protection_configuration():
    """Test selective protection configuration."""
    print("🧪 Testing Selective Protection Configuration...")
    
    try:
        from config import config
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        print(f"\n📋 Configuration:")
        print(f"   Selective Protection: {analyzer.selective_protection}")
        print(f"   High-Volume Groups: {list(analyzer.high_volume_groups)}")
        print(f"   Low-Volume Groups: {list(analyzer.low_volume_groups)}")
        
        # Test group classifications
        test_groups = {
            -1002202241417: "🧠 GMGN Featured Signals",
            -1002333406905: "🚀 MEME 1000X",
            -1002380594298: "FREE WHALE SIGNALS",
            -1002270988204: "Solana Activity Tracker",
            -1002064145465: "🌹 MANIFEST",
            -1001763265784: "👤 Mark Degens",
            -1002139128702: "💎 FINDERTRENDING"
        }
        
        print(f"\n🔍 Group Classifications:")
        for group_id, group_name in test_groups.items():
            is_high_volume = analyzer.is_high_volume_group(group_id)
            is_low_volume = analyzer.is_low_volume_group(group_id)
            requires_trending = analyzer.requires_trending_analysis(group_id)
            
            if is_high_volume:
                status = "🔥 HIGH-VOLUME (Trending + Anti-Pump)"
            elif is_low_volume:
                status = "⚡ LOW-VOLUME (Direct Forwarding)"
            else:
                status = "❓ UNCLASSIFIED"
            
            print(f"   {group_name}: {status}")
            print(f"      Requires Trending: {requires_trending}")
        
        # Verify expected classifications
        expected_high_volume = [-1002202241417, -1002333406905]
        expected_low_volume = [-1002380594298, -1002270988204, -1002064145465, -1001763265784, -1002139128702]
        
        print(f"\n✅ Verification:")
        all_correct = True
        
        for group_id in expected_high_volume:
            if not analyzer.is_high_volume_group(group_id):
                print(f"   ❌ {group_id} should be high-volume")
                all_correct = False
            else:
                print(f"   ✅ {group_id} correctly classified as high-volume")
        
        for group_id in expected_low_volume:
            if not analyzer.is_low_volume_group(group_id):
                print(f"   ❌ {group_id} should be low-volume")
                all_correct = False
            else:
                print(f"   ✅ {group_id} correctly classified as low-volume")
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_high_volume_group_behavior():
    """Test high-volume group behavior (trending analysis + anti-pump)."""
    print(f"\n🧪 Testing High-Volume Group Behavior...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        gmgn_group_id = -1002202241417  # GMGN Featured Signals
        test_ca = "HighVolumeTestCA123456789"
        
        print(f"   Testing GMGN Featured Signals ({gmgn_group_id})")
        
        # Test 1: Single mention (should not trend)
        result1 = await analyzer.analyze_ca_mention(test_ca, gmgn_group_id, "GMGN", 1001)
        print(f"   Single mention: Trending={result1.is_trending}, Count={result1.mention_count}")
        
        if result1.is_trending:
            print(f"   ❌ Single mention should not trend for high-volume groups")
            return False
        
        # Test 2: Check forwarding decision
        should_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", gmgn_group_id)
        print(f"   Should forward (not trending): {should_forward}")
        
        if should_forward:
            print(f"   ❌ Non-trending CA should not be forwarded from high-volume group")
            return False
        
        print(f"   ✅ High-volume group correctly requires trending for forwarding")
        return True
        
    except Exception as e:
        print(f"❌ High-volume group test failed: {e}")
        return False

async def test_low_volume_group_behavior():
    """Test low-volume group behavior (direct forwarding)."""
    print(f"\n🧪 Testing Low-Volume Group Behavior...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        whale_group_id = -1002380594298  # FREE WHALE SIGNALS
        test_ca = "LowVolumeTestCA123456789"
        
        print(f"   Testing FREE WHALE SIGNALS ({whale_group_id})")
        
        # Test 1: Single mention (should trend immediately for low-volume)
        result1 = await analyzer.analyze_ca_mention(test_ca, whale_group_id, "FREE WHALE SIGNALS", 2001)
        print(f"   Single mention: Trending={result1.is_trending}, Count={result1.mention_count}")
        
        if not result1.is_trending:
            print(f"   ❌ Single mention should trend immediately for low-volume groups")
            return False
        
        # Test 2: Check forwarding decision
        should_forward = analyzer.should_forward_to_destination(test_ca, "BONKBOT", whale_group_id)
        print(f"   Should forward (direct): {should_forward}")
        
        if not should_forward:
            print(f"   ❌ CA should be forwarded immediately from low-volume group")
            return False
        
        print(f"   ✅ Low-volume group correctly uses direct forwarding")
        return True
        
    except Exception as e:
        print(f"❌ Low-volume group test failed: {e}")
        return False

async def test_forwarding_logic():
    """Test forwarding logic for different group types."""
    print(f"\n🧪 Testing Forwarding Logic...")
    
    try:
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        # Test CAs
        trending_ca = "TrendingCA123456789"
        non_trending_ca = "NonTrendingCA123456789"
        
        # Simulate trending CA
        analyzer.trending_cas.add(trending_ca)
        
        test_cases = [
            # (CA, destination, source_group_id, expected_result, description)
            (trending_ca, "BONKBOT", -1002202241417, True, "Trending CA from high-volume group"),
            (non_trending_ca, "BONKBOT", -1002202241417, False, "Non-trending CA from high-volume group"),
            (trending_ca, "BONKBOT", -1002380594298, True, "Any CA from low-volume group"),
            (non_trending_ca, "BONKBOT", -1002380594298, True, "Any CA from low-volume group"),
            (trending_ca, "WINNERS", -1002202241417, False, "Any CA to excluded destination"),
            (trending_ca, "WINNERS", -1002380594298, False, "Any CA to excluded destination"),
        ]
        
        print(f"   Testing forwarding decisions:")
        all_correct = True
        
        for ca, destination, source_group, expected, description in test_cases:
            result = analyzer.should_forward_to_destination(ca, destination, source_group)
            status = "✅" if result == expected else "❌"
            print(f"   {status} {description}: {result} (expected: {expected})")

            # Debug failing cases
            if result != expected and "excluded destination" in description:
                from config import config
                excluded = getattr(config.trending, 'exclude_destinations', [])
                print(f"      DEBUG: destination='{destination}', excluded={excluded}")
                print(f"      DEBUG: is_high_volume={analyzer.is_high_volume_group(source_group)}")
                print(f"      DEBUG: is_low_volume={analyzer.is_low_volume_group(source_group)}")

            if result != expected:
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Forwarding logic test failed: {e}")
        return False

async def main():
    """Run all selective protection tests."""
    print("🚀 CLA v2.0 Bot - Selective Anti-Pump Protection Testing\n")
    
    try:
        # Test 1: Configuration
        if not await test_selective_protection_configuration():
            return False
        
        # Test 2: High-volume group behavior
        if not await test_high_volume_group_behavior():
            return False
        
        # Test 3: Low-volume group behavior
        if not await test_low_volume_group_behavior():
            return False
        
        # Test 4: Forwarding logic
        if not await test_forwarding_logic():
            return False
        
        print(f"\n🎉 All selective protection tests passed!")
        print(f"\n📋 Selective Protection Summary:")
        print(f"✅ High-Volume Groups (2): Enhanced protection with trending analysis")
        print(f"   - 🧠 GMGN Featured Signals (-1002202241417)")
        print(f"   - 🚀 MEME 1000X (-1002333406905) [PAUSED]")
        print(f"✅ Low-Volume Groups (5): Direct forwarding without trending")
        print(f"   - FREE WHALE SIGNALS (-1002380594298)")
        print(f"   - Solana Activity Tracker (-1002270988204)")
        print(f"   - 🌹 MANIFEST (-1002064145465)")
        print(f"   - 👤 Mark Degens (-1001763265784)")
        print(f"   - 💎 FINDERTRENDING (-1002139128702)")
        
        print(f"\n🎯 Protection Behavior:")
        print(f"🔥 High-Volume: 6 mentions in 8 minutes + anti-pump filters")
        print(f"⚡ Low-Volume: Immediate forwarding with global duplicate prevention only")
        print(f"🛡️ All Groups: Race condition protection maintained")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Selective protection test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
