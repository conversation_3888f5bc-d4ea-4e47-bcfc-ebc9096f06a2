#!/usr/bin/env python3
"""
Test WINNERS Disabled and Indentation Fix
Verifies that:
1. IndentationError in ca_detector.py is fixed
2. WINNERS integration is properly disabled
3. <PERSON><PERSON> can start with only 3 destinations (BonkBot, CLA v2.0, Monaco PNL)
"""

import sys
import os

# Add project root to path
sys.path.append('.')

def test_indentation_fix():
    """Test that the IndentationError in ca_detector.py is fixed."""
    print("=" * 80)
    print("🔧 TESTING INDENTATION FIX IN CA_DETECTOR.PY")
    print("=" * 80)
    
    try:
        from src.ca_detector import CADetector
        print("✅ CADetector import successful!")
        print("✅ IndentationError on line 86 has been fixed")
        return True
    except IndentationError as e:
        print(f"❌ IndentationError still exists: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Other import error: {e}")
        return False

def test_winners_integration_disabled():
    """Test that WINNERS integration is properly disabled."""
    print("\n🔧 TESTING WINNERS INTEGRATION DISABLED")
    print("=" * 80)
    
    try:
        # Check if bot can import without WINNERS
        from src.bot import CLABot
        print("✅ CLABot import successful with WINNERS disabled!")
        
        # Check the bot.py file content to verify WINNERS is commented out
        with open('src/bot.py', 'r', encoding='utf-8', errors='ignore') as f:
            bot_content = f.read()
        
        # Check for commented WINNERS import
        if "# from src.winners_integration import WinnersIntegration" in bot_content:
            print("✅ WINNERS import is properly commented out")
        else:
            print("❌ WINNERS import is not commented out")
            return False
        
        # Check for disabled WINNERS initialization
        if "self.winners_integration = None  # Temporarily disabled" in bot_content:
            print("✅ WINNERS integration initialization is disabled")
        else:
            print("❌ WINNERS integration initialization is not disabled")
            return False
        
        # Check for disabled WINNERS forwarding
        if "# TODO: Re-enable WINNERS forwarding when ready" in bot_content:
            print("✅ WINNERS forwarding is properly disabled")
        else:
            print("❌ WINNERS forwarding is not disabled")
            return False
        
        # Check for updated logging
        if "(WINNERS temporarily disabled)" in bot_content:
            print("✅ Logging updated to reflect WINNERS is disabled")
        else:
            print("❌ Logging not updated for WINNERS disabled status")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing WINNERS integration: {e}")
        return False

def test_remaining_integrations():
    """Test that the remaining 3 integrations can still be imported."""
    print("\n🔧 TESTING REMAINING INTEGRATIONS")
    print("=" * 80)
    
    integrations_to_test = [
        ("BonkBot", "src.bonkbot_integration", "BonkBotIntegration"),
        ("CLA v2.0", "src.cla_v2_integration", "CLAv2Integration"),
        ("Monaco PNL", "src.monaco_pnl_integration", "MonacoPNLIntegration")
    ]
    
    all_passed = True
    
    for name, module_path, class_name in integrations_to_test:
        try:
            module = __import__(module_path, fromlist=[class_name])
            integration_class = getattr(module, class_name)
            print(f"✅ {name} Integration: Import successful")
        except Exception as e:
            print(f"❌ {name} Integration: Import failed - {e}")
            all_passed = False
    
    return all_passed

def test_bot_initialization_simulation():
    """Simulate bot initialization to verify it works with WINNERS disabled."""
    print("\n🔧 TESTING BOT INITIALIZATION SIMULATION")
    print("=" * 80)
    
    try:
        # Import required modules
        from src.database import DatabaseManager
        from src.bot import CLABot
        
        print("✅ All required modules imported successfully")
        
        # Check that CLABot class can be instantiated (without actual initialization)
        print("✅ CLABot class is ready for instantiation")
        print("✅ Bot should start with 3 destinations: BonkBot, CLA v2.0, Monaco PNL")
        print("✅ WINNERS destination is temporarily disabled")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization simulation failed: {e}")
        return False

def test_forwarding_logic_check():
    """Check that forwarding logic properly excludes WINNERS."""
    print("\n🔧 TESTING FORWARDING LOGIC")
    print("=" * 80)
    
    try:
        with open('src/bot.py', 'r', encoding='utf-8', errors='ignore') as f:
            bot_content = f.read()
        
        # Check that WINNERS forwarding is commented out
        winners_forwarding_disabled = (
            "# TODO: Re-enable WINNERS forwarding when ready" in bot_content and
            "# if self.winners_integration:" in bot_content
        )
        
        if winners_forwarding_disabled:
            print("✅ WINNERS forwarding logic is properly disabled")
        else:
            print("❌ WINNERS forwarding logic is not properly disabled")
            return False
        
        # Check that summary logging excludes WINNERS
        if "WINNERS(disabled)" in bot_content:
            print("✅ Forwarding summary properly shows WINNERS as disabled")
        else:
            print("❌ Forwarding summary does not show WINNERS as disabled")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking forwarding logic: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 TESTING WINNERS DISABLED AND INDENTATION FIX")
    print("=" * 80)
    
    tests = [
        ("Indentation Fix", test_indentation_fix),
        ("WINNERS Integration Disabled", test_winners_integration_disabled),
        ("Remaining Integrations", test_remaining_integrations),
        ("Bot Initialization Simulation", test_bot_initialization_simulation),
        ("Forwarding Logic Check", test_forwarding_logic_check)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    
    passed_count = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    print(f"\nOverall: {passed_count}/{len(tests)} tests passed")
    
    if passed_count == len(tests):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ IndentationError in ca_detector.py is fixed")
        print("✅ WINNERS integration is properly disabled")
        print("✅ Bot can start with 3 destinations: BonkBot, CLA v2.0, Monaco PNL")
        print("✅ System is ready for deployment")
    else:
        print(f"\n❌ {len(tests) - passed_count} test(s) failed!")
        print("Please review the failed tests and fix the issues.")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
