"""Structured logging system for CLA v2.0 Bot with consistent formatting and correlation IDs."""

import uuid
import time
from datetime import datetime
from typing import Any, Dict, Optional
from contextvars import ContextVar
from loguru import logger
import json

# Context variable for correlation ID
correlation_id_var: ContextVar[str] = ContextVar('correlation_id', default='')

class StructuredLogger:
    """Structured logger with consistent formatting and correlation tracking."""
    
    def __init__(self, component_name: str):
        """Initialize structured logger for a component."""
        self.component_name = component_name
        self.start_time = time.time()
        
    def _format_message(self, level: str, message: str, **kwargs) -> Dict[str, Any]:
        """Format log message with structured data."""
        correlation_id = correlation_id_var.get()
        
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'component': self.component_name,
            'message': message,
            'correlation_id': correlation_id if correlation_id else None,
            'uptime_seconds': round(time.time() - self.start_time, 2)
        }
        
        # Add any additional context
        if kwargs:
            log_data['context'] = kwargs
        
        return log_data
    
    def info(self, message: str, **kwargs):
        """Log info message with structured format."""
        log_data = self._format_message('INFO', message, **kwargs)
        logger.info(f"📊 {self.component_name}: {message}", extra=log_data)
    
    def debug(self, message: str, **kwargs):
        """Log debug message with structured format."""
        log_data = self._format_message('DEBUG', message, **kwargs)
        logger.debug(f"🔍 {self.component_name}: {message}", extra=log_data)
    
    def warning(self, message: str, **kwargs):
        """Log warning message with structured format."""
        log_data = self._format_message('WARNING', message, **kwargs)
        logger.warning(f"⚠️ {self.component_name}: {message}", extra=log_data)
    
    def error(self, message: str, error: Optional[Exception] = None, **kwargs):
        """Log error message with structured format."""
        if error:
            kwargs['error_type'] = type(error).__name__
            kwargs['error_message'] = str(error)
        
        log_data = self._format_message('ERROR', message, **kwargs)
        logger.error(f"❌ {self.component_name}: {message}", extra=log_data)
    
    def critical(self, message: str, error: Optional[Exception] = None, **kwargs):
        """Log critical message with structured format."""
        if error:
            kwargs['error_type'] = type(error).__name__
            kwargs['error_message'] = str(error)
        
        log_data = self._format_message('CRITICAL', message, **kwargs)
        logger.critical(f"🚨 {self.component_name}: {message}", extra=log_data)
    
    def performance(self, operation: str, duration_ms: float, **kwargs):
        """Log performance metrics with structured format."""
        kwargs['operation'] = operation
        kwargs['duration_ms'] = round(duration_ms, 2)
        
        if duration_ms > 1000:
            level = 'WARNING'
            emoji = '🐌'
        elif duration_ms > 500:
            level = 'INFO'
            emoji = '⚠️'
        else:
            level = 'DEBUG'
            emoji = '⚡'
        
        message = f"Performance: {operation} took {duration_ms:.1f}ms"
        log_data = self._format_message(level, message, **kwargs)
        
        if level == 'WARNING':
            logger.warning(f"{emoji} {self.component_name}: {message}", extra=log_data)
        elif level == 'INFO':
            logger.info(f"{emoji} {self.component_name}: {message}", extra=log_data)
        else:
            logger.debug(f"{emoji} {self.component_name}: {message}", extra=log_data)
    
    def business_event(self, event_type: str, event_data: Dict[str, Any]):
        """Log business events with structured format."""
        message = f"Business Event: {event_type}"
        log_data = self._format_message('INFO', message, event_type=event_type, **event_data)
        logger.info(f"💼 {self.component_name}: {message}", extra=log_data)
    
    def security_event(self, event_type: str, severity: str, **kwargs):
        """Log security events with structured format."""
        kwargs['event_type'] = event_type
        kwargs['severity'] = severity
        
        message = f"Security Event: {event_type} (severity: {severity})"
        log_data = self._format_message('WARNING', message, **kwargs)
        logger.warning(f"🔒 {self.component_name}: {message}", extra=log_data)

class CorrelationContext:
    """Context manager for correlation ID tracking."""
    
    def __init__(self, correlation_id: Optional[str] = None):
        """Initialize correlation context."""
        self.correlation_id = correlation_id or str(uuid.uuid4())[:8]
        self.token = None
    
    def __enter__(self):
        """Enter correlation context."""
        self.token = correlation_id_var.set(self.correlation_id)
        return self.correlation_id
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit correlation context."""
        if self.token:
            correlation_id_var.reset(self.token)

class LoggingMetrics:
    """Collect and track logging metrics."""
    
    def __init__(self):
        """Initialize logging metrics."""
        self.metrics = {
            'total_logs': 0,
            'logs_by_level': {
                'DEBUG': 0,
                'INFO': 0,
                'WARNING': 0,
                'ERROR': 0,
                'CRITICAL': 0
            },
            'logs_by_component': {},
            'performance_logs': 0,
            'business_events': 0,
            'security_events': 0,
            'slow_operations': 0
        }
    
    def record_log(self, level: str, component: str, duration_ms: Optional[float] = None):
        """Record a log entry in metrics."""
        self.metrics['total_logs'] += 1
        
        if level in self.metrics['logs_by_level']:
            self.metrics['logs_by_level'][level] += 1
        
        if component not in self.metrics['logs_by_component']:
            self.metrics['logs_by_component'][component] = 0
        self.metrics['logs_by_component'][component] += 1
        
        if duration_ms and duration_ms > 500:
            self.metrics['slow_operations'] += 1
    
    def record_performance_log(self):
        """Record a performance log entry."""
        self.metrics['performance_logs'] += 1
    
    def record_business_event(self):
        """Record a business event log entry."""
        self.metrics['business_events'] += 1
    
    def record_security_event(self):
        """Record a security event log entry."""
        self.metrics['security_events'] += 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current logging metrics."""
        return self.metrics.copy()
    
    def get_summary(self) -> str:
        """Get formatted metrics summary."""
        total = self.metrics['total_logs']
        if total == 0:
            return "No logs recorded"
        
        summary = [
            f"📊 Logging Metrics Summary:",
            f"   Total logs: {total}",
            f"   By level: {self.metrics['logs_by_level']}",
            f"   Performance logs: {self.metrics['performance_logs']}",
            f"   Business events: {self.metrics['business_events']}",
            f"   Security events: {self.metrics['security_events']}",
            f"   Slow operations: {self.metrics['slow_operations']}",
            f"   Top components: {dict(sorted(self.metrics['logs_by_component'].items(), key=lambda x: x[1], reverse=True)[:5])}"
        ]
        
        return '\n'.join(summary)

# Global logging metrics instance
logging_metrics = LoggingMetrics()

def get_logger(component_name: str) -> StructuredLogger:
    """Get a structured logger for a component."""
    return StructuredLogger(component_name)

def with_correlation_id(correlation_id: Optional[str] = None):
    """Decorator to add correlation ID to function execution."""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            with CorrelationContext(correlation_id):
                return await func(*args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            with CorrelationContext(correlation_id):
                return func(*args, **kwargs)
        
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

def log_performance(operation_name: str):
    """Decorator to automatically log function performance."""
    def decorator(func):
        component_logger = get_logger(func.__module__.split('.')[-1])
        
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                component_logger.performance(operation_name, duration_ms)
                logging_metrics.record_performance_log()
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                component_logger.error(f"Performance tracking failed for {operation_name}", error=e, duration_ms=duration_ms)
                raise
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration_ms = (time.time() - start_time) * 1000
                component_logger.performance(operation_name, duration_ms)
                logging_metrics.record_performance_log()
                return result
            except Exception as e:
                duration_ms = (time.time() - start_time) * 1000
                component_logger.error(f"Performance tracking failed for {operation_name}", error=e, duration_ms=duration_ms)
                raise
        
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
