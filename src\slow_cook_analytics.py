"""
Slow Cook Analytics - Phase 1 Pattern Analysis and Statistics Collection
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from loguru import logger
import os

@dataclass
class SlowCookMetrics:
    """Comprehensive metrics for slow cook pattern analysis."""
    timestamp: datetime
    total_filtered_cas: int
    multi_mention_filtered: int
    slow_cook_candidates: int
    cross_group_patterns: int
    rescue_attempts: int
    rescue_successes: int
    mention_count_distribution: Dict[int, int]
    time_span_distribution: Dict[int, int]  # hours -> count
    group_activity_patterns: Dict[str, int]

@dataclass
class PatternAnalysis:
    """Analysis of CA patterns over time."""
    ca: str
    total_mentions: int
    time_span_hours: float
    groups_appeared: List[str]
    mention_velocity: float  # mentions per hour
    filtered_times: int
    rescued: bool
    rescue_delay_minutes: Optional[float]
    pattern_type: str  # "burst", "steady", "slow_cook", "unknown"

class SlowCookAnalytics:
    """Analytics engine for slow cook pattern detection and analysis."""
    
    def __init__(self):
        self.metrics_history: deque = deque(maxlen=168)  # 7 days of hourly metrics
        self.pattern_database: Dict[str, PatternAnalysis] = {}
        self.daily_summaries: Dict[str, Dict] = {}  # date -> summary
        
        # Analytics configuration
        self.analytics_enabled = True
        self.data_retention_hours = 168  # 7 days
        self.pattern_analysis_threshold = 3  # minimum mentions for analysis
        
        # Pattern classification thresholds
        self.burst_pattern_max_minutes = 10
        self.steady_pattern_max_hours = 2
        self.slow_cook_min_hours = 1
        self.slow_cook_min_mentions = 5
        
        logger.info("🔬 Slow Cook Analytics initialized - Phase 1 pattern analysis active")
    
    async def record_filtered_ca(self, ca: str, mention_count: int, time_span_hours: float,
                                groups: List[str], group_names: List[str]):
        """Record a filtered CA for pattern analysis."""
        try:
            # Calculate pattern metrics
            velocity = mention_count / max(0.1, time_span_hours)  # mentions per hour
            pattern_type = self._classify_pattern(mention_count, time_span_hours, velocity)
            
            # Update or create pattern analysis
            if ca in self.pattern_database:
                pattern = self.pattern_database[ca]
                pattern.total_mentions += mention_count
                pattern.time_span_hours = max(pattern.time_span_hours, time_span_hours)
                pattern.groups_appeared = list(set(pattern.groups_appeared + group_names))
                pattern.mention_velocity = pattern.total_mentions / pattern.time_span_hours
                pattern.filtered_times += 1
                pattern.pattern_type = self._classify_pattern(
                    pattern.total_mentions, pattern.time_span_hours, pattern.mention_velocity
                )
            else:
                self.pattern_database[ca] = PatternAnalysis(
                    ca=ca,
                    total_mentions=mention_count,
                    time_span_hours=time_span_hours,
                    groups_appeared=group_names.copy(),
                    mention_velocity=velocity,
                    filtered_times=1,
                    rescued=False,
                    rescue_delay_minutes=None,
                    pattern_type=pattern_type
                )
            
            # Log significant patterns
            pattern = self.pattern_database[ca]
            if pattern.pattern_type == "slow_cook":
                logger.warning(f"🐌 SLOW COOK PATTERN DETECTED: {ca} | "
                             f"{pattern.total_mentions} mentions | "
                             f"{pattern.time_span_hours:.1f}h | "
                             f"Velocity: {pattern.mention_velocity:.1f}/h | "
                             f"Groups: {len(pattern.groups_appeared)}")
            
        except Exception as e:
            logger.error(f"Error recording filtered CA {ca}: {e}")
    
    async def record_rescue_event(self, ca: str, rescue_delay_minutes: float):
        """Record a successful rescue event."""
        try:
            if ca in self.pattern_database:
                pattern = self.pattern_database[ca]
                pattern.rescued = True
                pattern.rescue_delay_minutes = rescue_delay_minutes
                
                logger.info(f"🚀 RESCUE RECORDED: {ca} | Pattern: {pattern.pattern_type} | "
                          f"Delay: {rescue_delay_minutes:.1f}min | "
                          f"Original mentions: {pattern.total_mentions}")
            
        except Exception as e:
            logger.error(f"Error recording rescue for {ca}: {e}")
    
    def _classify_pattern(self, mentions: int, time_span_hours: float, velocity: float) -> str:
        """Classify CA pattern based on metrics."""
        try:
            if time_span_hours <= (self.burst_pattern_max_minutes / 60):
                return "burst"
            elif time_span_hours <= self.steady_pattern_max_hours:
                return "steady"
            elif (time_span_hours >= self.slow_cook_min_hours and 
                  mentions >= self.slow_cook_min_mentions):
                return "slow_cook"
            else:
                return "unknown"
        except:
            return "unknown"
    
    async def generate_hourly_metrics(self, trending_stats: Dict, rescue_stats: Dict) -> SlowCookMetrics:
        """Generate comprehensive hourly metrics."""
        try:
            now = datetime.now()
            
            # Extract metrics from trending analyzer
            slow_cook_stats = trending_stats.get('slow_cook_stats', {})
            
            metrics = SlowCookMetrics(
                timestamp=now,
                total_filtered_cas=trending_stats.get('noise_filtered', 0),
                multi_mention_filtered=slow_cook_stats.get('multi_mention_filtered', 0),
                slow_cook_candidates=slow_cook_stats.get('slow_cook_candidates', 0),
                cross_group_patterns=slow_cook_stats.get('cross_group_patterns', 0),
                rescue_attempts=rescue_stats.get('rescue_attempts', 0),
                rescue_successes=rescue_stats.get('successful_rescues', 0),
                mention_count_distribution=dict(slow_cook_stats.get('filtered_by_mention_count', {})),
                time_span_distribution=dict(slow_cook_stats.get('time_span_distribution', {})),
                group_activity_patterns={}  # To be populated from group stats
            )
            
            # Add to history
            self.metrics_history.append(metrics)
            
            # Log key insights
            if metrics.slow_cook_candidates > 0:
                logger.warning(f"📊 HOURLY SLOW COOK ALERT: {metrics.slow_cook_candidates} candidates detected | "
                             f"Multi-mention filtered: {metrics.multi_mention_filtered} | "
                             f"Cross-group: {metrics.cross_group_patterns}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error generating hourly metrics: {e}")
            return SlowCookMetrics(
                timestamp=datetime.now(),
                total_filtered_cas=0, multi_mention_filtered=0, slow_cook_candidates=0,
                cross_group_patterns=0, rescue_attempts=0, rescue_successes=0,
                mention_count_distribution={}, time_span_distribution={}, group_activity_patterns={}
            )
    
    async def generate_daily_summary(self) -> Dict:
        """Generate comprehensive daily summary."""
        try:
            now = datetime.now()
            today = now.date().isoformat()
            
            # Get last 24 hours of metrics
            cutoff = now - timedelta(hours=24)
            recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff]
            
            if not recent_metrics:
                return {}
            
            # Aggregate metrics
            total_filtered = sum(m.total_filtered_cas for m in recent_metrics)
            total_multi_mention = sum(m.multi_mention_filtered for m in recent_metrics)
            total_slow_cook = sum(m.slow_cook_candidates for m in recent_metrics)
            total_cross_group = sum(m.cross_group_patterns for m in recent_metrics)
            total_rescues = sum(m.rescue_successes for m in recent_metrics)
            
            # Pattern analysis
            slow_cook_patterns = [p for p in self.pattern_database.values() 
                                if p.pattern_type == "slow_cook"]
            rescued_slow_cooks = [p for p in slow_cook_patterns if p.rescued]
            
            summary = {
                'date': today,
                'total_filtered_cas': total_filtered,
                'multi_mention_filtered': total_multi_mention,
                'slow_cook_candidates': total_slow_cook,
                'cross_group_patterns': total_cross_group,
                'rescue_success_rate': (total_rescues / max(1, total_multi_mention)) * 100,
                'slow_cook_patterns_detected': len(slow_cook_patterns),
                'slow_cook_patterns_rescued': len(rescued_slow_cooks),
                'slow_cook_rescue_rate': (len(rescued_slow_cooks) / max(1, len(slow_cook_patterns))) * 100,
                'pattern_distribution': {
                    'burst': len([p for p in self.pattern_database.values() if p.pattern_type == "burst"]),
                    'steady': len([p for p in self.pattern_database.values() if p.pattern_type == "steady"]),
                    'slow_cook': len(slow_cook_patterns),
                    'unknown': len([p for p in self.pattern_database.values() if p.pattern_type == "unknown"])
                },
                'top_slow_cook_patterns': [
                    {
                        'ca': p.ca,
                        'mentions': p.total_mentions,
                        'time_span_hours': p.time_span_hours,
                        'velocity': p.mention_velocity,
                        'groups': len(p.groups_appeared),
                        'rescued': p.rescued
                    }
                    for p in sorted(slow_cook_patterns, key=lambda x: x.total_mentions, reverse=True)[:10]
                ]
            }
            
            self.daily_summaries[today] = summary
            
            # Log daily summary
            logger.info(f"📈 DAILY SLOW COOK SUMMARY ({today}):")
            logger.info(f"   🎯 Slow cook candidates: {total_slow_cook}")
            logger.info(f"   🐌 Slow cook patterns: {len(slow_cook_patterns)}")
            logger.info(f"   🚀 Rescue rate: {summary['slow_cook_rescue_rate']:.1f}%")
            logger.info(f"   📊 Multi-mention filtered: {total_multi_mention}")
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating daily summary: {e}")
            return {}
    
    async def save_analytics_data(self, filepath: str = "./data/slow_cook_analytics.json"):
        """Save analytics data to file."""
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            data = {
                'last_updated': datetime.now().isoformat(),
                'pattern_database': {
                    ca: asdict(pattern) for ca, pattern in self.pattern_database.items()
                },
                'daily_summaries': self.daily_summaries,
                'recent_metrics': [
                    asdict(m) for m in list(self.metrics_history)[-24:]  # Last 24 hours
                ]
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.debug(f"📁 Analytics data saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving analytics data: {e}")
    
    def get_pattern_insights(self) -> Dict:
        """Get insights about detected patterns."""
        try:
            patterns = list(self.pattern_database.values())
            
            if not patterns:
                return {'message': 'No patterns detected yet'}
            
            slow_cooks = [p for p in patterns if p.pattern_type == "slow_cook"]
            rescued_slow_cooks = [p for p in slow_cooks if p.rescued]
            
            return {
                'total_patterns': len(patterns),
                'slow_cook_patterns': len(slow_cooks),
                'rescued_slow_cooks': len(rescued_slow_cooks),
                'rescue_rate': (len(rescued_slow_cooks) / max(1, len(slow_cooks))) * 100,
                'avg_slow_cook_mentions': sum(p.total_mentions for p in slow_cooks) / max(1, len(slow_cooks)),
                'avg_slow_cook_duration': sum(p.time_span_hours for p in slow_cooks) / max(1, len(slow_cooks)),
                'pattern_types': {
                    'burst': len([p for p in patterns if p.pattern_type == "burst"]),
                    'steady': len([p for p in patterns if p.pattern_type == "steady"]),
                    'slow_cook': len(slow_cooks),
                    'unknown': len([p for p in patterns if p.pattern_type == "unknown"])
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting pattern insights: {e}")
            return {'error': str(e)}
