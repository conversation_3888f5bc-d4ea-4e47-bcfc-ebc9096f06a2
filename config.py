"""Configuration management for CLA v2.0 Telegram Bot."""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class TelegramConfig:
    """Telegram API configuration."""
    def __init__(self):
        self.api_id = int(os.getenv('TELEGRAM_API_ID'))
        self.api_hash = os.getenv('TELEGRAM_API_HASH')
        self.phone = os.getenv('TELEGRAM_PHONE')
        self.session_name = os.getenv('TELEGRAM_SESSION_NAME', 'cla_bot_session')

class TargetGroupConfig:
    """Target group configuration."""
    def __init__(self):
        self.group_id = int(os.getenv('TARGET_GROUP_ID', -1002380594298))
        self.group_name = os.getenv('TARGET_GROUP_NAME', 'FREE WHALE SIGNALS')

        # Additional groups
        additional_ids = os.getenv('ADDITIONAL_GROUP_IDS', '')
        additional_names = os.getenv('ADDITIONAL_GROUP_NAMES', '')
        additional_status = os.getenv('ADDITIONAL_GROUP_STATUS', '')

        self.additional_group_ids = []
        self.additional_group_names = []
        self.additional_group_status = []

        if additional_ids:
            self.additional_group_ids = [int(id.strip()) for id in additional_ids.split(',') if id.strip()]

        if additional_names:
            self.additional_group_names = [name.strip() for name in additional_names.split(',') if name.strip()]

        if additional_status:
            self.additional_group_status = [status.strip().upper() for status in additional_status.split(',') if status.strip()]
        else:
            # Default all to ACTIVE if not specified
            self.additional_group_status = ['ACTIVE'] * len(self.additional_group_ids)

        # Active group IDs only (primary group is always active)
        self.active_group_ids = [self.group_id]
        for i, group_id in enumerate(self.additional_group_ids):
            if i < len(self.additional_group_status) and self.additional_group_status[i] == 'ACTIVE':
                self.active_group_ids.append(group_id)

        # All group IDs for reference (including paused ones)
        self.all_group_ids = [self.group_id] + self.additional_group_ids

        # Group status mapping
        self.group_status = {self.group_id: 'ACTIVE'}
        for i, group_id in enumerate(self.additional_group_ids):
            status = self.additional_group_status[i] if i < len(self.additional_group_status) else 'ACTIVE'
            self.group_status[group_id] = status

class BonkBotConfig:
    """BonkBot integration configuration."""
    def __init__(self):
        self.username = os.getenv('BONKBOT_USERNAME', '@BonkBot_bot')
        chat_id_str = os.getenv('BONKBOT_CHAT_ID')
        self.chat_id = int(chat_id_str) if chat_id_str and chat_id_str.isdigit() else None

class WinnersGroupConfig:
    """WINNERS group configuration."""
    def __init__(self):
        self.group_id = int(os.getenv('WINNERS_GROUP_ID', -1002439728391))
        self.group_name = os.getenv('WINNERS_GROUP_NAME', 'WINNERS')

class CLAv2GroupConfig:
    """CLA v2.0 channel configuration."""
    def __init__(self):
        self.group_id = int(os.getenv('CLA_V2_GROUP_ID', -1002659786727))
        self.group_name = os.getenv('CLA_V2_GROUP_NAME', 'CLA v2.0')

class MonacoPNLConfig:
    """Monaco PNL channel configuration."""
    def __init__(self):
        self.group_id = int(os.getenv('MONACO_PNL_GROUP_ID', -1002666569586))
        self.group_name = os.getenv('MONACO_PNL_GROUP_NAME', 'Monaco PNL Channel')
        self.username = os.getenv('MONACO_PNL_USERNAME', '@MonacoPNL')

class DatabaseConfig:
    """Database configuration."""
    def __init__(self):
        self.path = os.getenv('DATABASE_PATH', './data/cla_bot.db')

class CacheConfig:
    """Cache configuration."""
    def __init__(self):
        self.ca_expiry_hours = int(os.getenv('CA_CACHE_EXPIRY_HOURS', 72))
        self.max_cache_size = int(os.getenv('MAX_CACHE_SIZE', 10000))

class RateLimitConfig:
    """Rate limiting configuration."""
    def __init__(self):
        self.message_rate_limit = int(os.getenv('MESSAGE_RATE_LIMIT', 30))
        self.api_rate_limit = int(os.getenv('API_RATE_LIMIT', 20))

class LoggingConfig:
    """Logging configuration."""
    def __init__(self):
        self.level = os.getenv('LOG_LEVEL', 'INFO')
        self.file = os.getenv('LOG_FILE', './logs/cla_bot.log')

class TrendingConfig:
    """Trending analysis configuration with pump-and-dump protection."""
    def __init__(self):
        self.enabled = os.getenv('TRENDING_ENABLED', 'true').lower() == 'true'

        # Enhanced thresholds for pump-and-dump protection
        self.time_window_minutes = int(os.getenv('TRENDING_TIME_WINDOW_MINUTES', '8'))  # Increased from 5 to 8 minutes
        self.min_mentions = int(os.getenv('TRENDING_MIN_MENTIONS', '6'))  # Increased from 3 to 6 mentions

        # Anti-manipulation filters
        self.min_time_spread_seconds = int(os.getenv('TRENDING_MIN_TIME_SPREAD_SECONDS', '120'))  # 2 minutes minimum spread
        self.max_velocity_mentions_per_minute = float(os.getenv('TRENDING_MAX_VELOCITY', '3.0'))  # Max 3 mentions/minute
        self.min_organic_growth_minutes = int(os.getenv('TRENDING_MIN_ORGANIC_MINUTES', '3'))  # Must span at least 3 minutes

        # Quality filters
        self.require_time_distribution = os.getenv('TRENDING_REQUIRE_TIME_DISTRIBUTION', 'true').lower() == 'true'
        self.pump_detection_enabled = os.getenv('TRENDING_PUMP_DETECTION', 'true').lower() == 'true'

        # High volume groups that use trending analysis and anti-pump protection
        high_volume_str = os.getenv('TRENDING_HIGH_VOLUME_GROUPS', '-1002333406905,-1002202241417')
        self.high_volume_groups = []
        if high_volume_str:
            self.high_volume_groups = [int(id.strip()) for id in high_volume_str.split(',') if id.strip()]

        # Low volume groups that use direct forwarding (no trending analysis)
        low_volume_str = os.getenv('TRENDING_LOW_VOLUME_GROUPS', '-1002380594298,-1002270988204,-1002064145465,-1001763265784,-1002139128702')
        self.low_volume_groups = []
        if low_volume_str:
            self.low_volume_groups = [int(id.strip()) for id in low_volume_str.split(',') if id.strip()]

        # Selective protection mode
        self.selective_protection = os.getenv('TRENDING_SELECTIVE_PROTECTION', 'true').lower() == 'true'

        # Cross-group duplicate prevention behavior
        # True = strict prevention (no rescue), False = allow rescue mechanism
        self.strict_duplicate_prevention = os.getenv('STRICT_DUPLICATE_PREVENTION', 'false').lower() == 'true'

        # Destinations to exclude for trending groups
        exclude_str = os.getenv('TRENDING_EXCLUDE_DESTINATIONS', '')
        self.exclude_destinations = []
        if exclude_str:
            self.exclude_destinations = [dest.strip().upper() for dest in exclude_str.split(',') if dest.strip()]

class RescueConfig:
    """Rescue mechanism configuration."""
    def __init__(self):
        # Rescue mechanism settings
        self.enabled = os.getenv('RESCUE_ENABLED', 'true').lower() == 'true'
        self.rescue_window_hours = int(os.getenv('RESCUE_WINDOW_HOURS', '24'))
        self.max_rescue_cache_size = int(os.getenv('RESCUE_MAX_CACHE_SIZE', '1000'))
        self.max_rescued_cache_size = int(os.getenv('RESCUE_MAX_RESCUED_SIZE', '5000'))

        # Rescue validation settings
        self.enable_final_validation = os.getenv('RESCUE_FINAL_VALIDATION', 'true').lower() == 'true'
        self.validation_timeout_seconds = int(os.getenv('RESCUE_VALIDATION_TIMEOUT', '30'))

class PerformanceConfig:
    """Performance and resource management configuration."""
    def __init__(self):
        # Message processing
        self.message_dedup_window_seconds = int(os.getenv('MESSAGE_DEDUP_WINDOW', '30'))
        self.slow_processing_threshold_ms = int(os.getenv('SLOW_PROCESSING_THRESHOLD', '100'))
        self.very_slow_processing_threshold_ms = int(os.getenv('VERY_SLOW_PROCESSING_THRESHOLD', '500'))

        # Cache settings
        self.ca_lock_cleanup_threshold = int(os.getenv('CA_LOCK_CLEANUP_THRESHOLD', '1000'))
        self.trending_cleanup_interval_messages = int(os.getenv('TRENDING_CLEANUP_INTERVAL', '500'))
        self.periodic_stats_interval_messages = int(os.getenv('STATS_INTERVAL', '100'))

        # Memory management
        self.max_error_history_days = int(os.getenv('MAX_ERROR_HISTORY_DAYS', '7'))
        self.max_message_cache_size = int(os.getenv('MAX_MESSAGE_CACHE_SIZE', '10000'))

class IntegrationConfig:
    """External integration configuration."""
    def __init__(self):
        # Forwarding settings
        self.forwarding_timeout_seconds = int(os.getenv('FORWARDING_TIMEOUT', '30'))
        self.max_forwarding_retries = int(os.getenv('MAX_FORWARDING_RETRIES', '3'))
        self.forwarding_retry_delay_seconds = float(os.getenv('FORWARDING_RETRY_DELAY', '2.0'))

        # Queue settings
        self.max_forwarding_queue_size = int(os.getenv('MAX_FORWARDING_QUEUE_SIZE', '1000'))
        self.queue_processing_timeout_seconds = int(os.getenv('QUEUE_PROCESSING_TIMEOUT', '1'))

        # Status reporting
        self.status_report_interval_hours = int(os.getenv('STATUS_REPORT_INTERVAL', '1'))
        self.max_report_chunk_length = int(os.getenv('MAX_REPORT_CHUNK_LENGTH', '4000'))

class Config:
    """Main configuration class."""

    def __init__(self):
        self.telegram = TelegramConfig()
        self.target_group = TargetGroupConfig()
        self.bonkbot = BonkBotConfig()
        self.winners_group = WinnersGroupConfig()
        self.cla_v2_group = CLAv2GroupConfig()
        self.monaco_pnl = MonacoPNLConfig()
        self.database = DatabaseConfig()
        self.cache = CacheConfig()
        self.rate_limit = RateLimitConfig()
        self.logging = LoggingConfig()
        self.trending = TrendingConfig()
        self.rescue = RescueConfig()
        self.performance = PerformanceConfig()
        self.integration = IntegrationConfig()

        # Ensure directories exist
        self._ensure_directories()

        # Validate configuration
        self._validate_configuration()
    
    def _ensure_directories(self):
        """Create necessary directories."""
        db_dir = Path(self.database.path).parent
        log_dir = Path(self.logging.file).parent

        db_dir.mkdir(parents=True, exist_ok=True)
        log_dir.mkdir(parents=True, exist_ok=True)

    def _validate_configuration(self):
        """Validate configuration parameters."""
        errors = []

        # Validate trending configuration
        if self.trending.min_mentions < 1:
            errors.append("TRENDING_MIN_MENTIONS must be at least 1")

        if self.trending.time_window_minutes < 1:
            errors.append("TRENDING_TIME_WINDOW_MINUTES must be at least 1")

        # Validate group configurations
        if not self.trending.high_volume_groups and not self.trending.low_volume_groups:
            errors.append("At least one high-volume or low-volume group must be configured")

        # Check for overlapping groups
        high_volume_set = set(self.trending.high_volume_groups)
        low_volume_set = set(self.trending.low_volume_groups)
        overlap = high_volume_set & low_volume_set
        if overlap:
            errors.append(f"Groups cannot be both high-volume and low-volume: {overlap}")

        # Validate performance settings
        if self.performance.message_dedup_window_seconds < 1:
            errors.append("MESSAGE_DEDUP_WINDOW must be at least 1 second")

        # Validate rescue settings
        if self.rescue.rescue_window_hours < 1:
            errors.append("RESCUE_WINDOW_HOURS must be at least 1 hour")

        if self.rescue.max_rescue_cache_size < 100:
            errors.append("RESCUE_MAX_CACHE_SIZE should be at least 100")

        # Log validation results
        if errors:
            for error in errors:
                print(f"❌ Configuration Error: {error}")
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
        else:
            print("✅ Configuration validation passed")

    def reload_configuration(self):
        """Reload configuration from environment variables."""
        print("🔄 Reloading configuration...")

        # Store old values for comparison
        old_high_volume = set(self.trending.high_volume_groups)
        old_low_volume = set(self.trending.low_volume_groups)

        # Reinitialize all configurations
        self.__init__()

        # Log changes
        new_high_volume = set(self.trending.high_volume_groups)
        new_low_volume = set(self.trending.low_volume_groups)

        if old_high_volume != new_high_volume:
            print(f"🔄 High-volume groups changed: {old_high_volume} → {new_high_volume}")

        if old_low_volume != new_low_volume:
            print(f"🔄 Low-volume groups changed: {old_low_volume} → {new_low_volume}")

        print("✅ Configuration reloaded successfully")

    def get_configuration_summary(self) -> dict:
        """Get a summary of current configuration."""
        return {
            'trending': {
                'enabled': self.trending.enabled,
                'min_mentions': self.trending.min_mentions,
                'time_window_minutes': self.trending.time_window_minutes,
                'high_volume_groups': len(self.trending.high_volume_groups),
                'low_volume_groups': len(self.trending.low_volume_groups)
            },
            'rescue': {
                'enabled': self.rescue.enabled,
                'window_hours': self.rescue.rescue_window_hours,
                'max_cache_size': self.rescue.max_rescue_cache_size
            },
            'performance': {
                'dedup_window': self.performance.message_dedup_window_seconds,
                'slow_threshold': self.performance.slow_processing_threshold_ms,
                'cleanup_interval': self.performance.trending_cleanup_interval_messages
            },
            'integration': {
                'forwarding_timeout': self.integration.forwarding_timeout_seconds,
                'max_retries': self.integration.max_forwarding_retries,
                'queue_size': self.integration.max_forwarding_queue_size
            }
        }

# Global config instance
config = Config()
