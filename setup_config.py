"""Configuration setup helper for CLA v2.0 Bot."""

import os
import shutil

def setup_environment():
    """Setup environment configuration."""
    print("🔧 CLA v2.0 Bot Configuration Setup")
    print("=" * 50)
    
    # Check if .env already exists
    if os.path.exists('.env'):
        print("⚠️  .env file already exists!")
        overwrite = input("Do you want to overwrite it? (y/N): ").lower().strip()
        if overwrite != 'y':
            print("Configuration cancelled.")
            return False
    
    # Copy example file
    if os.path.exists('.env.example'):
        shutil.copy('.env.example', '.env')
        print("✅ Created .env file from template")
    else:
        print("❌ .env.example not found!")
        return False
    
    print("\n📋 You need to configure the following in your .env file:")
    print("\n🔑 REQUIRED - Telegram API Credentials:")
    print("   1. Go to: https://my.telegram.org/apps")
    print("   2. Create a new application")
    print("   3. Copy your api_id and api_hash")
    print("   4. Edit .env file with your credentials")
    
    print("\n📱 REQUIRED - Your Phone Number:")
    print("   Format: +1234567890 (with country code)")
    
    print("\n🤖 OPTIONAL - BonkBot Integration:")
    print("   1. Start chat with @BonkBot_bot")
    print("   2. Get your chat ID using @userinfobot")
    print("   3. Add BONKBOT_CHAT_ID to .env")
    
    print("\n📝 Edit your .env file now with a text editor")
    print("   Example: notepad .env")
    
    return True

def validate_config():
    """Validate configuration."""
    print("\n🔍 Validating Configuration...")
    
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        return False
    
    # Read .env file
    config_vars = {}
    with open('.env', 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                config_vars[key.strip()] = value.strip()
    
    # Check required variables
    required_vars = ['TELEGRAM_API_ID', 'TELEGRAM_API_HASH', 'TELEGRAM_PHONE']
    missing_vars = []
    
    for var in required_vars:
        if var not in config_vars or not config_vars[var] or config_vars[var] == f'your_{var.lower()}_here':
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing required configuration: {', '.join(missing_vars)}")
        print("   Please edit your .env file with the correct values")
        return False
    
    print("✅ Required configuration found")
    
    # Check optional variables
    optional_vars = ['BONKBOT_USERNAME', 'BONKBOT_CHAT_ID']
    configured_optional = []
    
    for var in optional_vars:
        if var in config_vars and config_vars[var] and not config_vars[var].startswith('your_'):
            configured_optional.append(var)
    
    if configured_optional:
        print(f"✅ Optional configuration: {', '.join(configured_optional)}")
    else:
        print("ℹ️  No optional BonkBot configuration (auto-trading disabled)")
    
    return True

def test_imports():
    """Test that all required packages are installed."""
    print("\n📦 Testing Package Imports...")
    
    required_packages = [
        ('telethon', 'Telethon'),
        ('aiohttp', 'aiohttp'),
        ('aiosqlite', 'aiosqlite'),
        ('dotenv', 'python-dotenv'),
        ('pydantic', 'pydantic'),
        ('pydantic_settings', 'pydantic-settings'),
        ('loguru', 'loguru'),
        ('base58', 'base58'),
        ('asyncio_throttle', 'asyncio-throttle')
    ]
    
    missing_packages = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {pip_name}")
        except ImportError:
            print(f"❌ {pip_name}")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Run: pip install -r requirements.txt")
        return False
    
    print("✅ All packages installed")
    return True

def main():
    """Main setup function."""
    print("🚀 CLA v2.0 Bot Setup")
    print("=" * 30)
    
    # Test imports first
    if not test_imports():
        print("\n❌ Setup failed: Missing packages")
        print("   Run: pip install -r requirements.txt")
        return False
    
    # Setup environment
    if not setup_environment():
        print("\n❌ Setup failed: Environment configuration")
        return False
    
    # Validate configuration
    if not validate_config():
        print("\n❌ Setup failed: Invalid configuration")
        print("   Please edit your .env file with correct values")
        return False
    
    print("\n🎉 Setup Complete!")
    print("\n📋 Next Steps:")
    print("   1. Run: python main.py")
    print("   2. Enter verification code from Telegram")
    print("   3. Bot will start monitoring automatically")
    print("\n📊 Monitor logs in: ./logs/cla_bot.log")
    print("🔧 Test functionality: python test_core_functionality.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
    exit(0 if success else 1)
