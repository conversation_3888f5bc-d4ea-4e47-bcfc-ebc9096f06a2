# CLA v2.0 Bot - Technical Documentation

## 🔧 **ARCHITECTURE OVERVIEW**

### **Core Components:**
1. **Message Reception** (`telegram_client.py`) - Handles Telegram API integration
2. **CA Detection** (`ca_detector.py`) - Extracts and validates contract addresses
3. **Trending Analysis** (`trending_analyzer.py`) - Anti-pump-and-dump intelligence
4. **Forwarding Integrations** - Multi-destination message delivery
5. **Database Management** (`database.py`) - Persistent storage and caching

---

## 🛡️ **RACE CONDITION PROTECTION SYSTEM**

### **1. Async Locks for CA Processing**
```python
# Location: src/ca_detector.py
self.ca_processing_locks: Dict[str, asyncio.Lock] = {}

async def _get_ca_lock(self, ca: str) -> asyncio.Lock:
    if ca not in self.ca_processing_locks:
        self.ca_processing_locks[ca] = asyncio.Lock()
    return self.ca_processing_locks[ca]

# Usage:
async with ca_lock:
    # Process CA atomically
```

### **2. Message-Level Deduplication**
```python
# Location: src/bot.py
self.recent_messages = {}  # message_id -> timestamp
self.message_dedup_window = 30  # seconds

async def _is_duplicate_message(self, message_id: int) -> bool:
    # Check and clean up old messages
    # Return True if duplicate, False if new
```

### **3. Queue-Level Duplicate Prevention**
```python
# Location: src/*_integration.py
self.queued_cas = set()  # Track CAs currently in queue
self.recent_sent_cas = {}  # CA -> timestamp for recent sends

async def _is_ca_recently_sent(self, ca: str) -> bool:
    # 5-minute window for duplicate prevention
```

### **4. Database Atomic Operations**
```python
# Location: src/database.py
cursor = await self.connection.execute("""
    INSERT OR IGNORE INTO contract_addresses 
    (ca, source_message_id, source_group_id) 
    VALUES (?, ?, ?)
""", (ca, message_id, group_id))

return cursor.rowcount > 0  # True if new record inserted
```

---

## 🧠 **ANTI-PUMP-AND-DUMP SYSTEM**

### **Enhanced Configuration:**
```env
# .env file settings
TRENDING_TIME_WINDOW_MINUTES=8
TRENDING_MIN_MENTIONS=6
TRENDING_MIN_TIME_SPREAD_SECONDS=120
TRENDING_MAX_VELOCITY=3.0
TRENDING_MIN_ORGANIC_MINUTES=3
TRENDING_REQUIRE_TIME_DISTRIBUTION=true
TRENDING_PUMP_DETECTION=true
```

### **Detection Algorithms:**
```python
# Location: src/trending_analyzer.py
async def _detect_pump_and_dump_pattern(self, mentions: List[CAMention]) -> tuple[bool, str]:
    # Check 1: Too fast (mentions too close together)
    if total_duration < self.min_time_spread_seconds:
        return True, f"too_fast_spread_{total_duration:.1f}s"
    
    # Check 2: Velocity too high
    velocity = (len(mentions) - 1) / (total_duration / 60)
    if velocity > self.max_velocity_mentions_per_minute:
        return True, f"high_velocity_{velocity:.1f}/min"
    
    # Check 3: Must span minimum organic growth time
    if total_duration < (self.min_organic_growth_minutes * 60):
        return True, f"insufficient_growth_time_{total_duration/60:.1f}min"
    
    # Check 4: Time distribution analysis
    # Prevents front-loaded clustering
```

---

## 📊 **MONITORING & LOGGING**

### **Enhanced Debug Logging:**
```python
# GMGN-specific logging
logger.info(f"🧠 GMGN MESSAGE RECEIVED: ID={message_id}")
logger.info(f"🧠 GMGN CA DETECTOR: Processing message {message_id}")
logger.info(f"🧠 GMGN EXTRACTION: Found {len(cas)} CAs")
logger.info(f"🧠 GMGN TRENDING: Analyzing CA {ca}")
logger.warning(f"🚫 PUMP SCHEME BLOCKED: {ca} | Reason: {reason}")
logger.info(f"🔥 ORGANIC TRENDING DETECTED: {ca}")
```

### **Statistics Tracking:**
```python
self.stats = {
    'messages_processed': 0,
    'cas_detected': 0,
    'cas_sent_to_bonkbot': 0,
    'cas_sent_to_cla_v2': 0,
    'cas_sent_to_monaco_pnl': 0,
    'gmgn_mentions': 0,
    'trending_qualified': 0,
    'noise_filtered': 0,
    'pump_schemes_blocked': 0,
    'organic_trends_passed': 0,
    'duplicate_messages_filtered': 0
}
```

---

## 🔄 **MESSAGE FLOW ARCHITECTURE**

### **1. Message Reception:**
```
Telegram API → telegram_client.py → _handle_message()
    ↓
Message Deduplication Check
    ↓
Group-specific Processing
```

### **2. CA Processing:**
```
ca_detector.py → extract_contract_addresses()
    ↓
Async Lock Acquisition (per CA)
    ↓
Global Duplicate Check
    ↓
Database Atomic Insert
    ↓
Cache Update
```

### **3. GMGN Trending Analysis:**
```
trending_analyzer.py → analyze_ca_mention()
    ↓
Pump-and-Dump Detection
    ↓
Organic Growth Validation
    ↓
Trending Decision (6 mentions/8 minutes)
```

### **4. Multi-Destination Forwarding:**
```
Queue Addition (with duplicate check)
    ↓
Rate-Limited Processing (2-second delays)
    ↓
Parallel Forwarding:
    ├── BonkBot Integration
    ├── CLA v2.0 Integration
    └── Monaco PNL Integration
```

---

## 🗄️ **DATABASE SCHEMA**

### **Contract Addresses Table:**
```sql
CREATE TABLE contract_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT UNIQUE NOT NULL,
    source_message_id INTEGER,
    source_group_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Processed CAs Table:**
```sql
CREATE TABLE processed_cas (
    ca TEXT PRIMARY KEY,
    processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## ⚙️ **CONFIGURATION MANAGEMENT**

### **Environment Variables:**
```env
# Telegram API
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_PHONE=your_phone

# Group Configuration
ADDITIONAL_GROUP_IDS=-1002380594298,-1002270988204,...
ADDITIONAL_GROUP_NAMES=FREE WHALE SIGNALS,Solana Activity Tracker,...
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,...

# Enhanced Trending Configuration
TRENDING_ENABLED=true
TRENDING_TIME_WINDOW_MINUTES=8
TRENDING_MIN_MENTIONS=6
TRENDING_HIGH_VOLUME_GROUPS=-1002202241417
TRENDING_EXCLUDE_DESTINATIONS=WINNERS

# Anti-Pump Protection
TRENDING_MIN_TIME_SPREAD_SECONDS=120
TRENDING_MAX_VELOCITY=3.0
TRENDING_MIN_ORGANIC_MINUTES=3
TRENDING_REQUIRE_TIME_DISTRIBUTION=true
TRENDING_PUMP_DETECTION=true
```

---

## 🧪 **TESTING FRAMEWORK**

### **Test Files:**
1. `test_race_condition_fixes.py` - Race condition protection tests
2. `test_anti_pump_protection.py` - Pump-and-dump detection tests
3. `test_current_bot_fixes.py` - Live bot validation tests
4. `test_config_values.py` - Configuration validation tests

### **Test Coverage:**
- ✅ Async lock functionality
- ✅ Message deduplication
- ✅ Queue duplicate prevention
- ✅ Database atomic operations
- ✅ Pump pattern detection
- ✅ Organic growth validation
- ✅ Configuration loading

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **Pre-Deployment:**
1. ✅ Update .env with enhanced configuration
2. ✅ Run all test suites
3. ✅ Verify database schema
4. ✅ Check Telegram API credentials
5. ✅ Validate group access permissions

### **Post-Deployment:**
1. ✅ Monitor initialization logs
2. ✅ Verify trending configuration loading
3. ✅ Test message reception
4. ✅ Validate forwarding destinations
5. ✅ Monitor for pump scheme detection

---

## 🔧 **MAINTENANCE & MONITORING**

### **Key Metrics to Monitor:**
- Message processing rate
- Duplicate prevention effectiveness
- Pump scheme detection rate
- Trending qualification accuracy
- Forwarding success rate
- Database performance

### **Log Analysis:**
- Search for "PUMP SCHEME BLOCKED" for protection effectiveness
- Monitor "DUPLICATE" messages for race condition prevention
- Track "TRENDING DETECTED" for signal quality
- Watch "ERROR" logs for system issues

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Common Issues:**
1. **High duplicate detection** → Check for message replay attacks
2. **No trending signals** → Verify enhanced thresholds are appropriate
3. **Missing messages** → Check group permissions and API limits
4. **Database locks** → Monitor concurrent access patterns

### **Performance Optimization:**
- Async lock cleanup (1000 lock threshold)
- Message cache cleanup (30-second window)
- Database connection pooling
- Rate limiting optimization

---

*Technical documentation maintained with institutional-grade precision! 🔧*
