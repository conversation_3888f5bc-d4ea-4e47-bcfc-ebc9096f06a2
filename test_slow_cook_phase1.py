#!/usr/bin/env python3
"""
Test Slow Cook Phase 1 Implementation
Verify enhanced logging, statistics collection, and pattern analysis
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_slow_cook_tracking():
    """Test the slow cook tracking functionality."""
    print("🧪 TESTING SLOW COOK PHASE 1 IMPLEMENTATION")
    print("=" * 60)
    
    try:
        from src.trending_analyzer import Trending<PERSON><PERSON>y<PERSON>, CAMention
        from src.slow_cook_analytics import SlowCookAnalytics
        from src.ca_rescue_tracker import CARescueTracker
        
        # Initialize components
        trending_analyzer = TrendingAnalyzer()
        analytics = SlowCookAnalytics()
        rescue_tracker = CARescueTracker()
        
        print("✅ Components initialized successfully")
        
        # Test 1: Simulate slow cook pattern
        await test_slow_cook_pattern_detection(trending_analyzer, analytics)
        
        # Test 2: Test analytics recording
        await test_analytics_recording(analytics)
        
        # Test 3: Test enhanced logging
        await test_enhanced_logging(trending_analyzer)
        
        # Test 4: Test statistics collection
        await test_statistics_collection(trending_analyzer, analytics)
        
        print("\n🎉 ALL PHASE 1 TESTS COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_slow_cook_pattern_detection(trending_analyzer, analytics):
    """Test slow cook pattern detection."""
    print("\n🔍 TEST 1: Slow Cook Pattern Detection")
    print("-" * 40)
    
    # Simulate a slow cook CA: 8 mentions over 3 hours
    test_ca = "SlowCookTestCA123456789012345678901234567890123456"
    gmgn_group_id = -1002202241417
    
    # Create mentions spread over 3 hours
    base_time = datetime.now() - timedelta(hours=3)
    mentions = []
    
    for i in range(8):
        mention_time = base_time + timedelta(minutes=i * 22)  # Every 22 minutes
        mention = CAMention(
            ca=test_ca,
            timestamp=mention_time,
            group_id=gmgn_group_id,
            group_name="🧠 GMGN Featured Signals",
            message_id=9000 + i
        )
        mentions.append(mention)
        
        # Add to trending analyzer
        trending_analyzer.ca_mentions[test_ca].append(mention)
    
    print(f"✅ Created {len(mentions)} mentions over 3 hours")
    
    # Analyze trending status
    result = await trending_analyzer._analyze_trending_status(test_ca)
    
    print(f"📊 Analysis Result:")
    print(f"   CA: {test_ca[:20]}...")
    print(f"   Mentions: {result.mention_count}")
    print(f"   Is Trending: {result.is_trending}")
    print(f"   Time span: {(result.latest_mention - result.first_mention).total_seconds() / 3600:.1f} hours")
    
    # This should be filtered (not trending) but tracked as slow cook
    if not result.is_trending and result.mention_count >= 5:
        print("✅ Correctly identified as filtered slow cook pattern")
    else:
        print("⚠️ Pattern classification unexpected")
    
    # Test analytics recording
    time_span_hours = (result.latest_mention - result.first_mention).total_seconds() / 3600
    groups = [m.group_id for m in mentions]
    group_names = [m.group_name for m in mentions]
    
    await analytics.record_filtered_ca(
        ca=test_ca,
        mention_count=result.mention_count,
        time_span_hours=time_span_hours,
        groups=groups,
        group_names=group_names
    )
    
    print("✅ Analytics recording completed")

async def test_analytics_recording(analytics):
    """Test analytics recording functionality."""
    print("\n📊 TEST 2: Analytics Recording")
    print("-" * 40)
    
    # Test different pattern types
    test_patterns = [
        ("BurstPattern123", 6, 0.1, ["GMGN"], "burst"),
        ("SteadyPattern456", 8, 1.5, ["GMGN", "MEME"], "steady"),
        ("SlowCookPattern789", 12, 4.0, ["GMGN", "MEME"], "slow_cook"),
    ]
    
    for ca, mentions, hours, groups, expected_type in test_patterns:
        await analytics.record_filtered_ca(
            ca=ca,
            mention_count=mentions,
            time_span_hours=hours,
            groups=[1, 2] if len(groups) > 1 else [1],
            group_names=groups
        )
        
        # Check pattern classification
        if ca in analytics.pattern_database:
            pattern = analytics.pattern_database[ca]
            print(f"✅ {ca[:15]}... | Type: {pattern.pattern_type} | Expected: {expected_type}")
            
            if pattern.pattern_type == expected_type:
                print(f"   ✅ Correct classification")
            else:
                print(f"   ⚠️ Classification mismatch")
        else:
            print(f"❌ Pattern not recorded for {ca}")
    
    # Test insights generation
    insights = analytics.get_pattern_insights()
    print(f"\n📈 Pattern Insights:")
    print(f"   Total patterns: {insights.get('total_patterns', 0)}")
    print(f"   Slow cook patterns: {insights.get('slow_cook_patterns', 0)}")
    print(f"   Pattern types: {insights.get('pattern_types', {})}")

async def test_enhanced_logging(trending_analyzer):
    """Test enhanced logging functionality."""
    print("\n📝 TEST 3: Enhanced Logging")
    print("-" * 40)
    
    # Test slow cook statistics
    stats = trending_analyzer.slow_cook_stats
    
    print(f"📊 Slow Cook Statistics:")
    print(f"   Multi-mention filtered: {stats['multi_mention_filtered']}")
    print(f"   Slow cook candidates: {stats['slow_cook_candidates']}")
    print(f"   Cross-group patterns: {stats['cross_group_patterns']}")
    
    # Test statistics methods
    slow_cook_stats = trending_analyzer.get_slow_cook_stats()
    
    if slow_cook_stats:
        print(f"✅ Slow cook stats retrieval working")
        print(f"   Active patterns: {slow_cook_stats.get('active_patterns', 0)}")
        
        pattern_details = slow_cook_stats.get('pattern_details', [])
        if pattern_details:
            print(f"   Pattern details available: {len(pattern_details)}")
        else:
            print(f"   No pattern details yet")
    else:
        print(f"⚠️ Slow cook stats not available")

async def test_statistics_collection(trending_analyzer, analytics):
    """Test comprehensive statistics collection."""
    print("\n📈 TEST 4: Statistics Collection")
    print("-" * 40)
    
    # Generate mock trending stats
    mock_trending_stats = {
        'total_mentions': 100,
        'trending_qualified': 15,
        'noise_filtered': 85,
        'slow_cook_stats': {
            'multi_mention_filtered': 25,
            'slow_cook_candidates': 8,
            'cross_group_patterns': 5,
            'filtered_by_mention_count': {2: 10, 3: 8, 4: 5, 5: 2},
            'time_span_distribution': {1: 5, 2: 8, 3: 7, 4: 5}
        }
    }
    
    mock_rescue_stats = {
        'rescue_attempts': 12,
        'successful_rescues': 8,
        'duplicate_rescue_prevented': 3
    }
    
    # Generate hourly metrics
    metrics = await analytics.generate_hourly_metrics(mock_trending_stats, mock_rescue_stats)
    
    print(f"📊 Generated Hourly Metrics:")
    print(f"   Timestamp: {metrics.timestamp.strftime('%H:%M:%S')}")
    print(f"   Total filtered: {metrics.total_filtered_cas}")
    print(f"   Multi-mention filtered: {metrics.multi_mention_filtered}")
    print(f"   Slow cook candidates: {metrics.slow_cook_candidates}")
    print(f"   Rescue success rate: {(metrics.rescue_successes / max(1, metrics.rescue_attempts)) * 100:.1f}%")
    
    # Test daily summary generation
    daily_summary = await analytics.generate_daily_summary()
    
    if daily_summary:
        print(f"\n📅 Daily Summary Generated:")
        print(f"   Date: {daily_summary.get('date', 'N/A')}")
        print(f"   Slow cook patterns: {daily_summary.get('slow_cook_patterns_detected', 0)}")
        print(f"   Rescue rate: {daily_summary.get('slow_cook_rescue_rate', 0):.1f}%")
    else:
        print(f"⚠️ Daily summary generation needs more data")
    
    # Test data persistence
    await analytics.save_analytics_data("./data/test_slow_cook_analytics.json")
    print(f"✅ Analytics data saved successfully")

async def test_integration_with_existing_system():
    """Test integration with existing bot components."""
    print("\n🔗 TEST 5: Integration Testing")
    print("-" * 40)
    
    try:
        # Test that existing functionality still works
        from src.trending_analyzer import TrendingAnalyzer
        
        analyzer = TrendingAnalyzer()
        
        # Test basic trending analysis still works
        test_ca = "IntegrationTestCA1234567890123456789012345678901234"
        gmgn_group_id = -1002202241417
        
        # Simulate trending CA (6 mentions in 5 minutes)
        base_time = datetime.now()
        for i in range(6):
            mention_time = base_time + timedelta(seconds=i * 50)  # Every 50 seconds
            result = await analyzer.analyze_ca_mention(
                ca=test_ca,
                group_id=gmgn_group_id,
                group_name="🧠 GMGN Featured Signals",
                message_id=8000 + i
            )
        
        if result.is_trending:
            print("✅ Normal trending analysis still working")
        else:
            print("⚠️ Normal trending analysis may be affected")
        
        # Test that slow cook tracking doesn't interfere
        trending_stats = analyzer.get_trending_stats()
        if trending_stats:
            print("✅ Statistics retrieval still working")
        else:
            print("⚠️ Statistics retrieval may be affected")
        
        print("✅ Integration tests completed")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")

async def main():
    """Main test function."""
    print("🚀 Starting Slow Cook Phase 1 Tests...")
    
    success = await test_slow_cook_tracking()
    
    if success:
        await test_integration_with_existing_system()
        print("\n🎉 ALL TESTS PASSED - PHASE 1 IMPLEMENTATION READY!")
        print("\n📋 PHASE 1 FEATURES VERIFIED:")
        print("   ✅ Enhanced logging for slow cook patterns")
        print("   ✅ Statistics collection and analysis")
        print("   ✅ Pattern classification system")
        print("   ✅ Analytics data persistence")
        print("   ✅ Integration with existing system")
        print("\n🔄 NEXT STEPS:")
        print("   📊 Monitor real-world data collection")
        print("   📈 Analyze pattern distributions")
        print("   🔧 Design Phase 2 multi-tier windows")
        print("   🎯 Optimize slow cook detection thresholds")
    else:
        print("\n❌ TESTS FAILED - REVIEW IMPLEMENTATION")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal test error: {e}")
        sys.exit(1)
