"""Test script to verify HIGH PRIORITY fixes are working correctly."""

import asyncio
import sys
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, 'src')

async def test_race_condition_fixes():
    """Test that race condition fixes are working."""
    print("🔒 Testing Race Condition Fixes...")
    
    try:
        from src.ca_rescue_tracker import ca_rescue_tracker
        
        # Test concurrent rescue attempts for same CA
        test_ca = "TestRaceConditionCA123456789"
        
        async def attempt_rescue(group_id, group_name, attempt_id):
            """Simulate rescue attempt."""
            try:
                result = await ca_rescue_tracker.check_for_rescue(
                    test_ca, group_id, group_name, f"Test message {attempt_id}", None
                )
                print(f"  Attempt {attempt_id}: {result}")
                return result
            except Exception as e:
                print(f"  Attempt {attempt_id} error: {e}")
                return False
        
        # Add CA to rescue eligible first
        await ca_rescue_tracker.add_filtered_ca(
            test_ca, -1002333406905, "🚀 MEME 1000X", 1, "Test message"
        )
        
        # Simulate concurrent rescue attempts
        tasks = []
        for i in range(5):
            task = attempt_rescue(-1002380594298, "FREE WHALE SIGNALS", i)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Only one should succeed
        successful_rescues = sum(1 for r in results if r is True)
        print(f"  ✅ Successful rescues: {successful_rescues} (should be 1)")
        print(f"  🚫 Failed rescues: {len(results) - successful_rescues}")
        
        if successful_rescues == 1:
            print("  ✅ Race condition protection working correctly!")
        else:
            print("  ❌ Race condition protection failed!")
        
    except Exception as e:
        print(f"  ❌ Error testing race conditions: {e}")

async def test_group_classification():
    """Test centralized group classification."""
    print("\n🏷️ Testing Group Classification Consolidation...")
    
    try:
        from src.group_manager import group_manager
        
        # Test high-volume group classification
        meme_1000x_id = -1002333406905
        gmgn_id = -1002202241417
        
        print(f"  MEME 1000X ({meme_1000x_id}): {group_manager.get_group_type(meme_1000x_id)}")
        print(f"  GMGN ({gmgn_id}): {group_manager.get_group_type(gmgn_id)}")
        
        # Test low-volume group classification
        free_whale_id = -1002380594298
        solana_tracker_id = -1002270988204
        
        print(f"  FREE WHALE ({free_whale_id}): {group_manager.get_group_type(free_whale_id)}")
        print(f"  Solana Tracker ({solana_tracker_id}): {group_manager.get_group_type(solana_tracker_id)}")
        
        # Test summary
        summary = group_manager.get_group_summary()
        print(f"  📊 Total monitored groups: {summary['total_monitored']}")
        print(f"  🔥 High-volume groups: {summary['high_volume_count']}")
        print(f"  ⚡ Low-volume groups: {summary['low_volume_count']}")
        
        print("  ✅ Group classification working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing group classification: {e}")

async def test_bounded_caches():
    """Test bounded cache implementation."""
    print("\n💾 Testing Bounded Cache Implementation...")
    
    try:
        from src.bounded_cache import BoundedLRUCache, BoundedSetCache
        
        # Test LRU Cache
        print("  Testing LRU Cache...")
        lru_cache = BoundedLRUCache(max_size=3, name="TestLRU")
        
        # Add items
        await lru_cache.put("key1", "value1")
        await lru_cache.put("key2", "value2")
        await lru_cache.put("key3", "value3")
        
        size = await lru_cache.size()
        print(f"    Cache size after 3 additions: {size}")
        
        # Add 4th item (should evict oldest)
        await lru_cache.put("key4", "value4")
        size = await lru_cache.size()
        print(f"    Cache size after 4th addition: {size}")
        
        # Check if oldest was evicted
        key1_exists = await lru_cache.contains("key1")
        key4_exists = await lru_cache.contains("key4")
        print(f"    Key1 exists (should be False): {key1_exists}")
        print(f"    Key4 exists (should be True): {key4_exists}")
        
        stats = await lru_cache.get_stats()
        print(f"    Cache stats: {stats}")
        
        # Test Set Cache
        print("  Testing Set Cache...")
        set_cache = BoundedSetCache(max_size=2, name="TestSet")
        
        await set_cache.add("item1")
        await set_cache.add("item2")
        await set_cache.add("item3")  # Should evict item1
        
        item1_exists = await set_cache.contains("item1")
        item3_exists = await set_cache.contains("item3")
        print(f"    Item1 exists (should be False): {item1_exists}")
        print(f"    Item3 exists (should be True): {item3_exists}")
        
        print("  ✅ Bounded caches working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing bounded caches: {e}")

async def test_safe_iteration():
    """Test safe dictionary iteration fixes."""
    print("\n🔄 Testing Safe Dictionary Iteration...")
    
    try:
        # Test the rescue tracker cleanup method
        from src.ca_rescue_tracker import ca_rescue_tracker
        
        print("  Testing rescue tracker cleanup...")
        
        # Add some test entries
        test_cas = [f"TestCA{i}" for i in range(5)]
        for ca in test_cas:
            await ca_rescue_tracker.add_filtered_ca(
                ca, -1002333406905, "🚀 MEME 1000X", 1, "Test message"
            )
        
        # Run cleanup (should not crash)
        await ca_rescue_tracker.cleanup_expired_entries()
        print("  ✅ Cleanup completed without errors")
        
        # Test trending analyzer cleanup
        from src.trending_analyzer import TrendingAnalyzer
        analyzer = TrendingAnalyzer()
        
        print("  Testing trending analyzer cleanup...")
        await analyzer.cleanup_expired_data()
        print("  ✅ Trending analyzer cleanup completed without errors")
        
        print("  ✅ Safe iteration fixes working correctly!")
        
    except Exception as e:
        print(f"  ❌ Error testing safe iteration: {e}")

async def test_memory_usage():
    """Test memory usage improvements."""
    print("\n🧠 Testing Memory Usage...")
    
    try:
        from src.ca_rescue_tracker import ca_rescue_tracker
        
        # Get initial cache stats
        rescue_cache_stats = await ca_rescue_tracker.rescue_eligible_cache.get_stats()
        rescued_cache_stats = await ca_rescue_tracker.rescued_cas_cache.get_stats()
        
        print(f"  Rescue eligible cache: {rescue_cache_stats['current_size']}/{rescue_cache_stats['max_size']}")
        print(f"  Rescued CAs cache: {rescued_cache_stats['current_size']}/{rescued_cache_stats['max_size']}")
        print(f"  Memory utilization: {rescue_cache_stats['utilization']:.1f}%")
        
        print("  ✅ Memory usage monitoring working!")
        
    except Exception as e:
        print(f"  ❌ Error testing memory usage: {e}")

async def run_comprehensive_test():
    """Run all high priority fix tests."""
    print("🔧 HIGH PRIORITY FIXES VERIFICATION")
    print("=" * 50)
    print(f"📅 Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # Run all tests
    await test_race_condition_fixes()
    await test_group_classification()
    await test_bounded_caches()
    await test_safe_iteration()
    await test_memory_usage()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print()
    print("=" * 50)
    print(f"✅ All tests completed in {duration:.2f} seconds")
    print("🎯 HIGH PRIORITY FIXES VERIFICATION COMPLETE")

def main():
    """Main test function."""
    try:
        asyncio.run(run_comprehensive_test())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")

if __name__ == "__main__":
    main()
