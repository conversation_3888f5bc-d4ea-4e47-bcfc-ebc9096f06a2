# CLA v2.0 Telegram Bot - AWS EC2 Deployment Script (PowerShell)
# ==============================================================
# This script deploys the bot to AWS EC2 from Windows
# Usage: .\deploy-to-ec2.ps1 -EC2IP "************" -SSHKey "C:\path\to\key.pem"

param(
    [Parameter(Mandatory=$true)]
    [string]$EC2IP,

    [Parameter(Mandatory=$true)]
    [string]$SSHKey
)

# Configuration
$EC2User = "ubuntu"
$RemoteTemp = "/tmp/cla-bot-deploy"
$InstallDir = "/opt/cla-bot"

Write-Host "🚀 CLA v2.0 Bot - AWS EC2 Deployment (Windows)" -ForegroundColor Blue
Write-Host "=============================================" -ForegroundColor Blue
Write-Host "Target: $EC2User@$EC2IP"
Write-Host "SSH Key: $SSHKey"
Write-Host ""

# Verify SSH key exists
if (-not (Test-Path $SSHKey)) {
    Write-Host "❌ SSH key not found: $SSHKey" -ForegroundColor Red
    exit 1
}

# Test SSH connection
Write-Host "🔍 Testing SSH connection..." -ForegroundColor Blue
$sshTest = ssh -i $SSHKey -o ConnectTimeout=10 -o BatchMode=yes "$EC2User@$EC2IP" "echo 'SSH connection successful'" 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Cannot connect to EC2 instance. Please check:" -ForegroundColor Red
    Write-Host "   - EC2 instance is running"
    Write-Host "   - Security group allows SSH from your IP"
    Write-Host "   - SSH key is correct"
    Write-Host "   - IP address is correct"
    exit 1
}

Write-Host "✅ SSH connection successful" -ForegroundColor Green

# Create deployment package
Write-Host "📦 Creating deployment package..." -ForegroundColor Blue
$TempDir = [System.IO.Path]::GetTempPath() + [System.Guid]::NewGuid().ToString()
New-Item -ItemType Directory -Path $TempDir | Out-Null
$PackageFile = "$TempDir\cla-bot-deployment.tar.gz"

# Use tar (available in Windows 10+) or 7-zip
$currentDir = Get-Location
if (Get-Command tar -ErrorAction SilentlyContinue) {
    tar -czf $PackageFile --exclude='.git' --exclude='__pycache__' --exclude='*.pyc' --exclude='.venv' --exclude='logs' --exclude='data' --exclude='*.log' --exclude='.env' --exclude='*.session' --exclude='*.session-journal' src/ *.py requirements.txt aws-deployment/ deployment/ *.md
} else {
    Write-Host "⚠️ tar not found. Please install 7-zip or use Windows Subsystem for Linux" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ Deployment package created" -ForegroundColor Green

# Transfer package to EC2
Write-Host "📤 Transferring files to EC2..." -ForegroundColor Blue
scp -i $SSHKey $PackageFile "$EC2User@$EC2IP`:$RemoteTemp.tar.gz"

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to transfer files" -ForegroundColor Red
    Remove-Item -Recurse -Force $TempDir
    exit 1
}

# Extract and install on EC2
Write-Host "🔧 Installing on EC2..." -ForegroundColor Blue
$installScript = @"
set -e

echo "🗂️ Extracting deployment package..."
mkdir -p "$RemoteTemp"
cd "$RemoteTemp"
tar -xzf "$RemoteTemp.tar.gz"

echo "🔧 Running installation script..."
sudo bash aws-deployment/aws-install.sh

echo "📋 Copying bot files..."
sudo cp -r . "$InstallDir/"
sudo chown -R cla-bot:cla-bot "$InstallDir"

echo "🧹 Cleaning up..."
rm -rf "$RemoteTemp" "$RemoteTemp.tar.gz"

echo "✅ Installation completed!"
"@

ssh -i $SSHKey "$EC2User@$EC2IP" $installScript

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Installation failed" -ForegroundColor Red
    Remove-Item -Recurse -Force $TempDir
    exit 1
}

# Cleanup local temp files
Remove-Item -Recurse -Force $TempDir

Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Configure environment variables:"
Write-Host "   ssh -i $SSHKey $EC2User@$EC2IP" -ForegroundColor Blue
Write-Host "   sudo nano $InstallDir/.env" -ForegroundColor Blue
Write-Host ""
Write-Host "2. Start the bot service:"
Write-Host "   sudo systemctl start cla-bot" -ForegroundColor Blue
Write-Host ""
Write-Host "3. Check status:"
Write-Host "   cla-bot-status" -ForegroundColor Blue
Write-Host ""
Write-Host "4. View logs:"
Write-Host "   sudo journalctl -u cla-bot -f" -ForegroundColor Blue
Write-Host ""
Write-Host "⚠️ IMPORTANT: Remember to configure your .env file with actual API keys!" -ForegroundColor Red