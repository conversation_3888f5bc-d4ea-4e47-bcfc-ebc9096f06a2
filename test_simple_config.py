"""Test simplified configuration."""

try:
    from config import config
    print("✅ Configuration loaded successfully!")
    print(f"API ID: {config.telegram.api_id}")
    print(f"API Hash: {config.telegram.api_hash}")
    print(f"Phone: {config.telegram.phone}")
    print(f"Group ID: {config.target_group.group_id}")
    print(f"Group Name: {config.target_group.group_name}")
    print(f"BonkBot: {config.bonkbot.username}")
    print(f"Database: {config.database.path}")
    
except Exception as e:
    print(f"❌ Configuration failed: {e}")
    import traceback
    traceback.print_exc()
