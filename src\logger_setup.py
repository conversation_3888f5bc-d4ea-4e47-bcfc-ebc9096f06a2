"""Logging setup for CLA v2.0 Bot."""

import sys
from pathlib import Path
from loguru import logger
from config import config

def setup_logging():
    """Setup logging configuration."""
    
    # Remove default handler
    logger.remove()
    
    # Ensure log directory exists
    log_file_path = Path(config.logging.file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Console handler with colors
    logger.add(
        sys.stdout,
        level=config.logging.level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # File handler
    logger.add(
        config.logging.file,
        level=config.logging.level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="7 days",
        compression="zip"
    )
    
    # Error file handler
    error_log_path = log_file_path.parent / "error.log"
    logger.add(
        str(error_log_path),
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="5 MB",
        retention="30 days"
    )
    
    logger.info("Logging setup completed")
