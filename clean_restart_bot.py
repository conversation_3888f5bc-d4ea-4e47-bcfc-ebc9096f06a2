#!/usr/bin/env python3
"""
Clean restart script for CLA Bot - resolves database locks and session issues
"""
import os
import sys
import time
import sqlite3
import logging
from pathlib import Path
import glob

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('clean_restart.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def clean_database_locks():
    """Clean any database locks"""
    logger.info("Cleaning database locks...")
    
    # Find and close any SQLite database connections
    db_files = [
        './data/cla_bot.db',
        './cla_bot.db',
        'cla_bot.db'
    ]
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Try to connect and close immediately to release any locks
                conn = sqlite3.connect(db_file, timeout=1.0)
                conn.close()
                logger.info(f"Successfully accessed database: {db_file}")
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    logger.warning(f"Database locked: {db_file}")
                    # Try to create a new connection with a longer timeout
                    try:
                        conn = sqlite3.connect(db_file, timeout=10.0)
                        conn.execute("BEGIN IMMEDIATE;")
                        conn.rollback()
                        conn.close()
                        logger.info(f"Released lock on: {db_file}")
                    except Exception as e2:
                        logger.error(f"Could not release lock on {db_file}: {e2}")
                else:
                    logger.error(f"Database error for {db_file}: {e}")
            except Exception as e:
                logger.error(f"Unexpected error with {db_file}: {e}")

def clean_session_files():
    """Clean any Telegram session files"""
    logger.info("Cleaning session files...")
    
    # Look for common session file patterns
    session_patterns = [
        '*.session',
        '*.session-journal',
        'session.db',
        'session.db-journal',
        'telegram.session',
        'telegram.session-journal'
    ]
    
    for pattern in session_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                logger.info(f"Removed session file: {file}")
            except Exception as e:
                logger.warning(f"Could not remove {file}: {e}")

def verify_winners_config():
    """Verify WINNERS configuration is correct"""
    logger.info("Verifying WINNERS configuration...")
    
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            content = f.read()
        
        # Check for TRENDING_EXCLUDE_DESTINATIONS
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('TRENDING_EXCLUDE_DESTINATIONS=') and not line.startswith('#'):
                value = line.split('=', 1)[1].strip()
                if 'WINNERS' in value:
                    logger.error("WINNERS is still excluded!")
                    return False
                else:
                    logger.info(f"WINNERS exclusion properly cleared: {line}")
                    return True
        
        logger.info("No active TRENDING_EXCLUDE_DESTINATIONS found - WINNERS should be enabled")
        return True
    else:
        logger.warning("No .env file found")
        return True

def start_bot_clean():
    """Start the bot with clean environment"""
    logger.info("Starting CLA Bot with clean environment...")
    
    try:
        # Import and start the bot
        import asyncio
        from main import main as bot_main
        
        logger.info("Bot modules imported successfully")
        
        # Run the bot
        asyncio.run(bot_main())
        
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        logger.exception("Full error details:")
        return False
    
    return True

def main():
    """Main cleanup and restart process"""
    logger.info("=" * 60)
    logger.info("CLA Bot Clean Restart Process")
    logger.info("=" * 60)
    
    try:
        # Step 1: Clean database locks
        clean_database_locks()
        
        # Step 2: Clean session files
        clean_session_files()
        
        # Step 3: Verify WINNERS configuration
        if not verify_winners_config():
            logger.error("WINNERS configuration verification failed!")
            return False
        
        # Step 4: Wait a moment for cleanup to complete
        logger.info("Waiting for cleanup to complete...")
        time.sleep(2)
        
        # Step 5: Start the bot
        logger.info("Starting bot with enhanced fixes...")
        start_bot_clean()
        
    except KeyboardInterrupt:
        logger.info("Clean restart interrupted by user")
    except Exception as e:
        logger.error(f"Clean restart failed: {e}")
        logger.exception("Full error details:")
        return False
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Critical error: {e}")
        sys.exit(1)
