# CLA v2 Database Schema Documentation

**⚠️ PRIVATE REPOSITORY - INTERNAL USE ONLY**

This document provides complete database schema documentation for the CLA v2 Telegram bot system.

## 🗄️ Database Overview

**Database Type**: SQLite 3  
**Location**: `./data/cla_bot.db`  
**Journal Mode**: WAL (Write-Ahead Logging)  
**Synchronous Mode**: NORMAL  
**Cache Size**: 10,000 pages  

## 📊 Table Schemas

### 1. contract_addresses

**Purpose**: Primary storage for contract address metadata and trending information.

```sql
CREATE TABLE contract_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT UNIQUE NOT NULL,
    first_seen TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mention_count INTEGER DEFAULT 1,
    source_groups TEXT,  -- JSON array of group IDs
    trending_score REAL DEFAULT 0.0,
    is_trending BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Field Descriptions**:
- `id`: Auto-incrementing primary key
- `ca`: Unique Solana contract address (43-44 characters)
- `first_seen`: Timestamp of first detection
- `last_seen`: Timestamp of most recent mention
- `mention_count`: Total number of mentions across all groups
- `source_groups`: JSON array of group IDs where CA was mentioned
- `trending_score`: Calculated trending score (0.0 - 1.0)
- `is_trending`: Boolean flag for trending status
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

**Example Data**:
```sql
INSERT INTO contract_addresses VALUES (
    1,
    '7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn',
    '2025-07-30 10:15:30',
    '2025-07-30 10:45:22',
    3,
    '[-1002380594298, -1002202241417]',
    0.75,
    1,
    '2025-07-30 10:15:30',
    '2025-07-30 10:45:22'
);
```

### 2. message_cache

**Purpose**: Deduplication cache for processed messages to prevent duplicate processing.

```sql
CREATE TABLE message_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER UNIQUE NOT NULL,
    group_id INTEGER NOT NULL,
    message_text TEXT,
    processed BOOLEAN DEFAULT FALSE,
    ca_extracted TEXT,  -- JSON array of extracted CAs
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processing_time_ms INTEGER DEFAULT 0
);
```

**Field Descriptions**:
- `id`: Auto-incrementing primary key
- `message_id`: Telegram message ID (unique per group)
- `group_id`: Telegram group ID where message originated
- `message_text`: Original message content (truncated for storage)
- `processed`: Boolean flag indicating processing completion
- `ca_extracted`: JSON array of contract addresses extracted from message
- `timestamp`: Message processing timestamp
- `processing_time_ms`: Time taken to process message in milliseconds

**Example Data**:
```sql
INSERT INTO message_cache VALUES (
    1,
    4555,
    -1002380594298,
    'New token alert: 7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn',
    1,
    '["7xKXtg2CW3EL9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn"]',
    '2025-07-30 10:15:30',
    125
);
```

### 3. slow_cook_patterns

**Purpose**: Phase 1 slow cook pattern analysis data storage.

```sql
CREATE TABLE slow_cook_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    group_id INTEGER NOT NULL,
    mention_timestamp REAL NOT NULL,
    pattern_data TEXT,  -- JSON data with analysis results
    velocity REAL DEFAULT 0.0,
    cross_group_count INTEGER DEFAULT 1,
    confidence_score REAL DEFAULT 0.0,
    created_at REAL DEFAULT (julianday('now'))
);
```

**Field Descriptions**:
- `id`: Auto-incrementing primary key
- `ca`: Contract address being analyzed
- `group_id`: Group ID where mention occurred
- `mention_timestamp`: Unix timestamp of mention (high precision)
- `pattern_data`: JSON object with detailed analysis data
- `velocity`: Calculated mention velocity (mentions per minute)
- `cross_group_count`: Number of different groups mentioning this CA
- `confidence_score`: Pattern confidence score (0.0 - 1.0)
- `created_at`: Julian day timestamp for efficient time calculations

**Pattern Data JSON Structure**:
```json
{
    "mention_times": [1722334530.123, 1722334650.456, 1722334770.789],
    "group_distribution": {
        "-1002380594298": 2,
        "-1002202241417": 1
    },
    "velocity_analysis": {
        "current_velocity": 0.33,
        "max_velocity": 0.5,
        "velocity_trend": "stable"
    },
    "time_spread": {
        "min_interval": 120,
        "max_interval": 240,
        "avg_interval": 180
    },
    "pattern_classification": "organic_growth"
}
```

### 4. rescue_tracking_enhanced

**Purpose**: Enhanced rescue tracking for multi-mention CA rescue logic.

```sql
CREATE TABLE rescue_tracking_enhanced (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    rescue_type TEXT NOT NULL,  -- MULTI_MENTION, SLOW_COOK, CROSS_GROUP, VELOCITY_FILTERED
    group_id INTEGER NOT NULL,
    mention_count INTEGER DEFAULT 1,
    first_mention REAL NOT NULL,
    last_mention REAL NOT NULL,
    pattern_confidence REAL DEFAULT 0.0,
    rescue_eligibility TEXT DEFAULT 'PENDING',  -- PENDING, ELIGIBLE, RESCUED, INELIGIBLE
    metadata TEXT,  -- JSON metadata for rescue decision
    created_at REAL DEFAULT (julianday('now')),
    updated_at REAL DEFAULT (julianday('now'))
);
```

**Field Descriptions**:
- `id`: Auto-incrementing primary key
- `ca`: Contract address being tracked for rescue
- `rescue_type`: Type of rescue pattern detected
- `group_id`: Primary group ID for this rescue entry
- `mention_count`: Number of mentions contributing to rescue
- `first_mention`: Julian day timestamp of first mention
- `last_mention`: Julian day timestamp of most recent mention
- `pattern_confidence`: Confidence in rescue pattern (0.0 - 1.0)
- `rescue_eligibility`: Current rescue status
- `metadata`: JSON metadata for rescue decision making
- `created_at`: Record creation timestamp
- `updated_at`: Record last update timestamp

**Rescue Types**:
- `MULTI_MENTION`: Multiple mentions across time windows
- `SLOW_COOK`: Slow cook pattern detected
- `CROSS_GROUP`: Mentions across multiple groups
- `VELOCITY_FILTERED`: Filtered due to high velocity (pump protection)

### 5. trending_analysis

**Purpose**: Detailed trending analysis results and historical data.

```sql
CREATE TABLE trending_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    group_id INTEGER NOT NULL,
    analysis_timestamp REAL NOT NULL,
    trending_result TEXT NOT NULL,  -- QUALIFIED, NOISE_FILTERED, VELOCITY_FILTERED
    trending_score REAL DEFAULT 0.0,
    mention_velocity REAL DEFAULT 0.0,
    time_spread_seconds INTEGER DEFAULT 0,
    cross_group_mentions INTEGER DEFAULT 1,
    analysis_data TEXT,  -- JSON with detailed analysis
    created_at REAL DEFAULT (julianday('now'))
);
```

**Field Descriptions**:
- `id`: Auto-incrementing primary key
- `ca`: Contract address analyzed
- `group_id`: Group where analysis was performed
- `analysis_timestamp`: Unix timestamp of analysis
- `trending_result`: Result of trending analysis
- `trending_score`: Calculated trending score
- `mention_velocity`: Mentions per minute at time of analysis
- `time_spread_seconds`: Time spread between mentions
- `cross_group_mentions`: Number of groups mentioning CA
- `analysis_data`: JSON with detailed analysis results
- `created_at`: Record creation timestamp

## 🔍 Database Indexes

### Performance Indexes

```sql
-- Primary lookup indexes
CREATE INDEX idx_ca_lookup ON contract_addresses(ca);
CREATE INDEX idx_ca_trending ON contract_addresses(is_trending, trending_score);
CREATE INDEX idx_ca_updated ON contract_addresses(updated_at);

-- Message cache indexes
CREATE INDEX idx_message_cache_group ON message_cache(group_id, timestamp);
CREATE INDEX idx_message_cache_processed ON message_cache(processed, timestamp);
CREATE INDEX idx_message_cache_message_id ON message_cache(message_id, group_id);

-- Slow cook pattern indexes
CREATE INDEX idx_slow_cook_ca ON slow_cook_patterns(ca);
CREATE INDEX idx_slow_cook_timestamp ON slow_cook_patterns(mention_timestamp);
CREATE INDEX idx_slow_cook_group ON slow_cook_patterns(group_id, mention_timestamp);
CREATE INDEX idx_slow_cook_confidence ON slow_cook_patterns(confidence_score);

-- Rescue tracking indexes
CREATE INDEX idx_rescue_enhanced_ca ON rescue_tracking_enhanced(ca);
CREATE INDEX idx_rescue_enhanced_type ON rescue_tracking_enhanced(rescue_type);
CREATE INDEX idx_rescue_enhanced_eligibility ON rescue_tracking_enhanced(rescue_eligibility);
CREATE INDEX idx_rescue_enhanced_updated ON rescue_tracking_enhanced(updated_at);

-- Trending analysis indexes
CREATE INDEX idx_trending_analysis_ca ON trending_analysis(ca);
CREATE INDEX idx_trending_analysis_timestamp ON trending_analysis(analysis_timestamp);
CREATE INDEX idx_trending_analysis_result ON trending_analysis(trending_result);
CREATE INDEX idx_trending_analysis_score ON trending_analysis(trending_score);

-- Composite indexes for complex queries
CREATE INDEX idx_ca_group_trending ON contract_addresses(ca, is_trending);
CREATE INDEX idx_slow_cook_ca_group ON slow_cook_patterns(ca, group_id);
CREATE INDEX idx_rescue_ca_type ON rescue_tracking_enhanced(ca, rescue_type);
```

### Query Optimization

**Common Query Patterns**:

```sql
-- Find trending CAs
SELECT ca, trending_score, mention_count 
FROM contract_addresses 
WHERE is_trending = 1 
ORDER BY trending_score DESC;

-- Get recent slow cook patterns
SELECT ca, confidence_score, cross_group_count 
FROM slow_cook_patterns 
WHERE mention_timestamp > (julianday('now') - 1)  -- Last 24 hours
ORDER BY confidence_score DESC;

-- Check for duplicate messages
SELECT COUNT(*) 
FROM message_cache 
WHERE message_id = ? AND group_id = ?;

-- Get rescue eligible CAs
SELECT ca, rescue_type, pattern_confidence 
FROM rescue_tracking_enhanced 
WHERE rescue_eligibility = 'ELIGIBLE' 
ORDER BY pattern_confidence DESC;
```

## 🔧 Database Maintenance

### Cleanup Procedures

```sql
-- Clean old message cache (older than 7 days)
DELETE FROM message_cache 
WHERE timestamp < datetime('now', '-7 days');

-- Clean old slow cook patterns (older than 30 days)
DELETE FROM slow_cook_patterns 
WHERE created_at < (julianday('now') - 30);

-- Clean old trending analysis (older than 14 days)
DELETE FROM trending_analysis 
WHERE created_at < (julianday('now') - 14);

-- Update rescue tracking status for old entries
UPDATE rescue_tracking_enhanced 
SET rescue_eligibility = 'EXPIRED' 
WHERE rescue_eligibility = 'PENDING' 
AND created_at < (julianday('now') - 7);
```

### Database Statistics

```sql
-- Table sizes
SELECT name, COUNT(*) as row_count 
FROM sqlite_master 
CROSS JOIN (
    SELECT COUNT(*) FROM contract_addresses
    UNION ALL SELECT COUNT(*) FROM message_cache
    UNION ALL SELECT COUNT(*) FROM slow_cook_patterns
    UNION ALL SELECT COUNT(*) FROM rescue_tracking_enhanced
    UNION ALL SELECT COUNT(*) FROM trending_analysis
) 
WHERE type = 'table';

-- Database size
SELECT page_count * page_size as size_bytes 
FROM pragma_page_count(), pragma_page_size();

-- Index usage statistics
SELECT name, tbl_name 
FROM sqlite_master 
WHERE type = 'index' 
ORDER BY tbl_name;
```

## 🚀 Performance Configuration

### SQLite Optimization Settings

```sql
-- WAL mode for better concurrency
PRAGMA journal_mode = WAL;

-- Optimize for performance
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = memory;
PRAGMA mmap_size = 268435456;  -- 256MB

-- Auto-vacuum for space management
PRAGMA auto_vacuum = INCREMENTAL;

-- Optimize query planner
PRAGMA optimize;
```

### Connection Pool Settings

```python
# Database connection configuration
DATABASE_CONFIG = {
    'path': './data/cla_bot.db',
    'timeout': 30.0,
    'check_same_thread': False,
    'isolation_level': None,  # Autocommit mode
    'detect_types': sqlite3.PARSE_DECLTYPES | sqlite3.PARSE_COLNAMES
}
```

This database schema documentation provides complete technical details for all tables, indexes, and optimization settings used in the CLA v2 Telegram bot system. All schema definitions, query patterns, and maintenance procedures are documented for internal development and database administration purposes.
