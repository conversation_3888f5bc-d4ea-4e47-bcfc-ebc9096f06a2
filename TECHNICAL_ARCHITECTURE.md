# CLA v2 Technical Architecture Documentation

**⚠️ PRIVATE REPOSITORY - INTERNAL USE ONLY**

This document provides detailed technical architecture information for the CLA v2 Telegram bot system.

## 🏗️ System Architecture Overview

### Component Hierarchy

```
CLA v2 Bot System
├── Core Engine (src/bot.py)
│   ├── Message Reception Layer
│   │   ├── TelegramClientManager (src/telegram_client.py)
│   │   ├── Event Handler Registration
│   │   └── Rate Limiting & Throttling
│   │
│   ├── Processing Pipeline
│   │   ├── MessageParser (src/message_parser.py)
│   │   ├── CADetector (src/ca_detector.py)
│   │   ├── TrendingAnalyzer (src/trending_analyzer.py)
│   │   └── SlowCookAnalytics (src/slow_cook_analytics.py)
│   │
│   ├── Data Management Layer
│   │   ├── DatabaseManager (src/database.py)
│   │   ├── BoundedCache (src/bounded_cache.py)
│   │   └── CARescueTracker (src/ca_rescue_tracker.py)
│   │
│   └── Integration Layer
│       ├── BonkBotIntegration (src/bonkbot_integration.py)
│       ├── CLAv2Integration (src/cla_v2_integration.py)
│       ├── MonacoPNLIntegration (src/monaco_pnl_integration.py)
│       └── WinnersIntegration (src/winners_integration.py)
│
├── Configuration Management
│   ├── ConfigurationClasses (config.py)
│   ├── GroupManager (src/group_manager.py)
│   └── Environment Variables (.env)
│
├── Monitoring & Analytics
│   ├── EnhancedStatsTracker (src/enhanced_stats_tracker.py)
│   ├── HighVolumeCAAnalyzer (src/high_volume_ca_analyzer.py)
│   └── Logger Setup (src/logger_setup.py)
│
└── Support Systems
    ├── Error Handling (src/error_handler.py)
    ├── Dependency Container (src/dependency_container.py)
    └── Testing Framework (tests/)
```

## 🔄 Data Flow Architecture

### Message Processing Flow

```mermaid
sequenceDiagram
    participant TG as Telegram Groups
    participant TC as TelegramClient
    participant MP as MessageParser
    participant CD as CADetector
    participant TA as TrendingAnalyzer
    participant SC as SlowCookAnalytics
    participant DB as Database
    participant INT as Integrations

    TG->>TC: New Message Event
    TC->>MP: Parse Message Text
    MP->>MP: Extract URLs & CAs
    MP->>CD: Validate CAs
    CD->>DB: Check Duplicates
    DB-->>CD: Duplicate Status
    CD->>TA: Apply Trending Analysis
    TA->>SC: Record Pattern Data
    SC->>DB: Store Pattern
    TA-->>CD: Trending Result
    CD->>INT: Forward Qualified CAs
    INT->>TG: Send to Destinations
```

### Database Interaction Patterns

```mermaid
graph TD
    A[Message Received] --> B[Extract CAs]
    B --> C{CA in Cache?}
    C -->|Yes| D[Check Duplicate Window]
    C -->|No| E[Validate CA]
    E --> F[Store in Database]
    F --> G[Add to Cache]
    D --> H{Within Window?}
    H -->|Yes| I[Skip - Duplicate]
    H -->|No| J[Process CA]
    G --> J
    J --> K[Apply Trending Analysis]
    K --> L[Record Slow Cook Pattern]
    L --> M[Forward to Destinations]
```

## 🧩 Module Deep Dive

### 1. Core Bot Engine (`src/bot.py`)

**Class**: `CLABot`

**Responsibilities**:
- Orchestrates entire message processing pipeline
- Manages component lifecycle and initialization
- Handles error recovery and graceful shutdown
- Coordinates parallel forwarding to destinations

**Key Methods**:
```python
async def initialize() -> bool:
    # Initialize all components and integrations
    
async def _handle_message(event) -> None:
    # Main message processing entry point
    
async def _process_for_cas(message_text, message_id, chat_id) -> None:
    # CA detection and processing pipeline
    
async def _forward_cas_to_destinations(cas, message_text, source_group, chat_id) -> None:
    # Parallel forwarding to all 4 destinations
```

**Dependencies**:
- DatabaseManager: Data persistence
- TelegramClientManager: Telegram API interface
- All integration modules: Destination forwarding
- Configuration classes: Runtime settings

### 2. Message Parser (`src/message_parser.py`)

**Class**: `MessageParser`

**Responsibilities**:
- Extract contract addresses from message text
- Parse URLs from supported platforms
- Validate Solana contract address format
- Clean and normalize message content

**URL Extraction Patterns**:
```python
self.url_patterns = {
    'dexscreener': re.compile(r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{43,44})'),
    'pump_fun': re.compile(r'pump\.fun/([1-9A-HJ-NP-Za-km-z]{43,44})'),
    'solscan': re.compile(r'solscan\.io/token/([1-9A-HJ-NP-Za-km-z]{43,44})'),
    'axiom': re.compile(r'axiom\.trade/t/([1-9A-HJ-NP-Za-km-z]{43,44})'),
    'gmgn': re.compile(r'gmgn\.ai/sol/token/([1-9A-HJ-NP-Za-km-z]{43,44})')
}
```

**Validation Logic**:
```python
def _validate_solana_ca(self, ca: str) -> bool:
    # 1. Check length (43-44 characters)
    # 2. Validate base58 encoding
    # 3. Ensure 32-byte decoded length
    # 4. Check not all zeros
```

### 3. Trending Analyzer (`src/trending_analyzer.py`)

**Class**: `TrendingAnalyzer`

**Responsibilities**:
- Implement anti-pump protection mechanisms
- Apply selective protection based on group volume
- Calculate trending scores and confidence levels
- Manage mention velocity and time spread analysis

**Selective Protection Algorithm**:
```python
def analyze_ca_trending(self, ca: str, group_id: int) -> TrendingResult:
    if group_id in HIGH_VOLUME_GROUPS:
        # Enhanced trending analysis
        return self._enhanced_trending_analysis(ca, group_id)
    else:
        # Direct forwarding with global duplicate prevention
        return self._direct_forwarding_analysis(ca, group_id)
```

**Anti-Pump Mechanisms**:
1. **Velocity Filtering**: Max 3 mentions per minute
2. **Time Spread**: Minimum 120 seconds between mentions
3. **Organic Growth Window**: 3-minute natural distribution
4. **Cross-Group Validation**: Multi-group mention bonus

### 4. Slow Cook Analytics (`src/slow_cook_analytics.py`)

**Class**: `SlowCookAnalytics`

**Responsibilities**:
- Track multi-mention patterns across groups
- Analyze organic growth vs pump schemes
- Calculate pattern confidence scores
- Maintain 24-hour pattern memory

**Pattern Analysis Algorithm**:
```python
def analyze_pattern(self, ca: str, group_id: int, timestamp: float) -> PatternResult:
    # 1. Record mention with metadata
    # 2. Calculate velocity over time windows
    # 3. Check cross-group distribution
    # 4. Apply confidence scoring
    # 5. Determine pattern classification
```

**Configuration Parameters**:
```python
SLOW_COOK_CONFIG = {
    'min_mentions': 3,           # Minimum mentions for analysis
    'time_window': 3600,         # 1 hour analysis window
    'cross_group_bonus': 1.5,    # Cross-group mention bonus
    'velocity_threshold': 0.5,   # Max mentions/minute for organic
    'pattern_memory': 86400      # 24 hour retention
}
```

### 5. Database Manager (`src/database.py`)

**Class**: `DatabaseManager`

**Responsibilities**:
- SQLite database connection management
- Table creation and schema management
- CRUD operations for all data types
- Performance optimization and indexing

**Connection Configuration**:
```python
# WAL mode for better concurrency
await self.connection.execute("PRAGMA journal_mode=WAL")
# Optimize for performance
await self.connection.execute("PRAGMA synchronous=NORMAL")
await self.connection.execute("PRAGMA cache_size=10000")
await self.connection.execute("PRAGMA temp_store=memory")
```

**Key Tables**:
- `contract_addresses`: CA metadata and trending scores
- `message_cache`: Message processing deduplication
- `slow_cook_patterns`: Phase 1 pattern analysis data
- `rescue_tracking_enhanced`: Multi-mention rescue logic

### 6. Integration Modules

**Common Interface Pattern**:
```python
class BaseIntegration:
    async def initialize() -> bool:
        # Setup integration-specific configuration
        
    async def send_multiple_cas(self, cas: List[str], message_text: str, source_group: str) -> int:
        # Send CAs to destination, return count sent
        
    async def _format_ca_message(self, ca: str, source_message: str, source_group: str) -> str:
        # Format message for specific destination
```

**Integration Implementations**:
- **BonkBotIntegration**: Direct message to @BonkBot_bot
- **CLAv2Integration**: Channel message with formatted content
- **MonacoPNLIntegration**: Admin identity channel forwarding
- **WinnersIntegration**: Group message with source attribution

## 🔧 Configuration Architecture

### Configuration Class Hierarchy

```python
# config.py structure
class TelegramConfig:
    # API credentials and session management
    
class TargetGroupConfig:
    # Source group definitions and status
    
class DestinationConfig:
    # Output channel configurations
    
class CacheConfig:
    # Memory and database cache settings
    
class TrendingConfig:
    # Trending analysis parameters
    
class SlowCookConfig:
    # Phase 1 pattern analysis settings
```

### Group Manager (`src/group_manager.py`)

**Centralized Group Classification**:
```python
HIGH_VOLUME_GROUPS = [
    -1002333406905,  # 🚀 MEME 1000X
    -1002202241417   # 🧠 GMGN Featured Signals
]

LOW_VOLUME_GROUPS = [
    -1002380594298,  # FREE WHALE SIGNALS
    -1002270988204,  # Solana Activity Tracker
    -1002064145465,  # 🌹 MANIFEST
    -1001763265784,  # 👤 Mark Degens
    -1002139128702,  # 💎 FINDERTRENDING
    -1002356333152   # BUGSIE
]
```

## 🚀 Performance Architecture

### Caching Strategy

**Multi-Level Caching**:
1. **In-Memory Cache**: Recent CAs and messages (BoundedCache)
2. **Database Cache**: Persistent storage with indexes
3. **Application Cache**: Component-specific caching

**Cache Implementation**:
```python
class BoundedCache:
    def __init__(self, max_size: int):
        self.cache = {}
        self.access_order = []
        self.max_size = max_size
        
    def get(self, key):
        # LRU access pattern
        
    def put(self, key, value):
        # Bounded size with eviction
```

### Parallel Processing

**Async Forwarding Pattern**:
```python
async def _forward_cas_to_destinations(self, cas, message_text, source_group, chat_id):
    # Create parallel tasks for all destinations
    tasks = [
        self._forward_to_bonkbot_parallel(cas, message_text, source_group, chat_id),
        self._forward_to_cla_v2_parallel(cas, message_text, source_group, chat_id),
        self._forward_to_monaco_pnl_parallel(cas, message_text, source_group, chat_id),
        self._forward_to_winners_parallel(cas, message_text, source_group, chat_id)
    ]
    
    # Execute all tasks concurrently
    results = await asyncio.gather(*tasks, return_exceptions=True)
```

### Database Optimization

**Index Strategy**:
```sql
-- Primary lookup indexes
CREATE INDEX idx_ca_lookup ON contract_addresses(ca);
CREATE INDEX idx_message_cache_group ON message_cache(group_id, timestamp);

-- Performance indexes
CREATE INDEX idx_ca_trending ON contract_addresses(is_trending, trending_score);
CREATE INDEX idx_slow_cook_timestamp ON slow_cook_patterns(mention_timestamp);

-- Composite indexes for complex queries
CREATE INDEX idx_rescue_enhanced_ca_type ON rescue_tracking_enhanced(ca, rescue_type);
```

## 🔍 Monitoring Architecture

### Statistics Collection

**Enhanced Stats Tracker**:
```python
class EnhancedStatsTracker:
    def record_message_processed(self, group_id: int):
        # Track message volume by group
        
    def record_ca_detected(self, group_id: int, ca_count: int):
        # Track CA detection rates
        
    def record_ca_forwarded(self, group_id: int, destination: str, count: int):
        # Track forwarding success rates
        
    def record_trending_analysis(self, group_id: int, qualified: bool):
        # Track trending analysis effectiveness
```

### Performance Metrics

**Key Performance Indicators**:
- Message processing latency (ms per message)
- CA detection accuracy (detected vs actual)
- Trending qualification rate (qualified vs total)
- Database query performance (average response time)
- Memory usage (cache hit rates, memory consumption)
- Network performance (API response times)

### Error Handling Architecture

**Comprehensive Error Management**:
```python
class ErrorHandler:
    async def handle_telegram_error(self, error):
        # Telegram API specific error handling
        
    async def handle_database_error(self, error):
        # Database connection and query error handling
        
    async def handle_integration_error(self, error, integration_name):
        # Integration-specific error handling with retry logic
```

This technical architecture documentation provides the complete internal structure and design patterns used throughout the CLA v2 Telegram bot system. All components, data flows, and performance optimizations are documented for internal development and maintenance purposes.
