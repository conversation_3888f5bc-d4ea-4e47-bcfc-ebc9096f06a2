#!/usr/bin/env python3
"""
Debug Version of Main Bot
Includes detailed logging to identify where the bot freezes
"""

import asyncio
import signal
import sys
import time
from datetime import datetime

# Add project root to path
sys.path.append('.')

def print_debug(message):
    """Print debug message with timestamp."""
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] DEBUG: {message}")
    sys.stdout.flush()  # Force immediate output

def timeout_handler(signum, frame):
    """Handle timeout for hanging operations."""
    print(f"\n❌ TIMEOUT: Bot froze for more than 60 seconds")
    print("The last debug message shows where it froze")
    sys.exit(1)

async def initialize_with_debug():
    """Initialize bot with detailed debug logging."""
    try:
        print_debug("Starting CLA v2.0 Bot initialization...")
        
        # Step 1: Configuration
        print_debug("Loading configuration...")
        from config import config
        print_debug("✅ Configuration loaded")
        
        # Step 2: Database
        print_debug("Creating DatabaseManager...")
        from src.database import DatabaseManager
        db_manager = DatabaseManager()
        print_debug("✅ DatabaseManager created")
        
        print_debug("Connecting to database...")
        await db_manager.connect()
        print_debug("✅ Database connected")
        
        # Step 3: Bot Creation
        print_debug("Creating CLABot instance...")
        from src.bot import CLABot
        bot = CLABot(db_manager)
        print_debug("✅ CLABot instance created")
        
        # Step 4: Bot Initialization (this is likely where it freezes)
        print_debug("Starting bot initialization...")
        print_debug("This may take 30-60 seconds for Telegram connection...")
        
        success = await bot.initialize()
        
        if success:
            print_debug("✅ Bot initialization completed successfully")
            
            # Step 5: Start the bot
            print_debug("Starting bot message listener...")
            await bot.start()
            
        else:
            print_debug("❌ Bot initialization failed")
            return False
            
    except KeyboardInterrupt:
        print_debug("Bot stopped by user (Ctrl+C)")
        return False
    except Exception as e:
        print_debug(f"❌ Error during initialization: {e}")
        import traceback
        print_debug(f"Traceback: {traceback.format_exc()}")
        return False
    
    return True

async def main_with_timeout():
    """Main function with timeout protection."""
    print("🚀 CLA v2 BOT - DEBUG MODE")
    print("=" * 50)
    print("This version includes detailed logging to identify freeze points")
    print("If the bot freezes, the last DEBUG message shows where")
    print("=" * 50)
    
    # Set 5-minute timeout for the entire initialization
    signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(300)  # 5 minutes
    
    try:
        success = await initialize_with_debug()
        signal.alarm(0)  # Cancel timeout
        
        if success:
            print_debug("🎉 Bot started successfully!")
        else:
            print_debug("❌ Bot failed to start")
            
    except Exception as e:
        signal.alarm(0)  # Cancel timeout
        print_debug(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main_with_timeout())
    except KeyboardInterrupt:
        print("\n⚠️ Bot interrupted by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
