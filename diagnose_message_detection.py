#!/usr/bin/env python3
"""
Comprehensive Message Detection Diagnostic Tool
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.append('.')

from loguru import logger
from config import config
from src.group_manager import GroupManager

async def diagnose_message_detection():
    """Comprehensive diagnosis of message detection issues."""
    
    print("=" * 80)
    print("🔍 MESSAGE DETECTION DIAGNOSTIC REPORT")
    print("=" * 80)
    
    # 1. Configuration Analysis
    print("\n📋 1. CONFIGURATION ANALYSIS")
    print("-" * 40)
    
    try:
        # Check target group configuration
        print(f"Primary group: {config.target_group.group_name} ({config.target_group.group_id})")
        print(f"Additional groups: {len(config.target_group.additional_group_ids)}")
        
        # Show all configured groups
        print(f"\nAll configured groups ({len(config.target_group.all_group_ids)}):")
        for i, group_id in enumerate(config.target_group.all_group_ids):
            if i == 0:
                name = config.target_group.group_name
                status = "ACTIVE"
            else:
                idx = i - 1
                name = config.target_group.additional_group_names[idx] if idx < len(config.target_group.additional_group_names) else f"Group {group_id}"
                status = config.target_group.group_status.get(group_id, "UNKNOWN")
            print(f"  - {name} ({group_id}) [{status}]")
        
        # Show active groups only
        print(f"\nActive groups for message handler ({len(config.target_group.active_group_ids)}):")
        for group_id in config.target_group.active_group_ids:
            if group_id == config.target_group.group_id:
                name = config.target_group.group_name
            else:
                idx = config.target_group.additional_group_ids.index(group_id)
                name = config.target_group.additional_group_names[idx] if idx < len(config.target_group.additional_group_names) else f"Group {group_id}"
            print(f"  - {name} ({group_id})")
        
        print("✅ Configuration analysis complete")
        
    except Exception as e:
        print(f"❌ Configuration analysis failed: {e}")
        return False
    
    # 2. Group Manager Analysis
    print("\n🏢 2. GROUP MANAGER ANALYSIS")
    print("-" * 40)
    
    try:
        group_manager = GroupManager()
        
        # Check group classifications
        high_volume = group_manager.get_high_volume_groups()
        low_volume = group_manager.get_low_volume_groups()
        all_monitored = group_manager.get_all_monitored_groups()
        
        print(f"High-volume groups: {len(high_volume)}")
        for group_id in high_volume:
            name = group_manager.get_group_name(group_id)
            print(f"  - {name} ({group_id})")
        
        print(f"\nLow-volume groups: {len(low_volume)}")
        for group_id in low_volume:
            name = group_manager.get_group_name(group_id)
            print(f"  - {name} ({group_id})")
        
        print(f"\nTotal monitored groups: {len(all_monitored)}")
        
        # Check for discrepancies
        config_active = set(config.target_group.active_group_ids)
        manager_monitored = all_monitored
        
        if config_active == manager_monitored:
            print("✅ Configuration and GroupManager are in sync")
        else:
            print("⚠️ DISCREPANCY DETECTED:")
            print(f"  Config active: {config_active}")
            print(f"  Manager monitored: {manager_monitored}")
            print(f"  Missing from manager: {config_active - manager_monitored}")
            print(f"  Extra in manager: {manager_monitored - config_active}")
        
        print("✅ Group manager analysis complete")
        
    except Exception as e:
        print(f"❌ Group manager analysis failed: {e}")
        return False
    
    # 3. Telegram Client Analysis
    print("\n📱 3. TELEGRAM CLIENT ANALYSIS")
    print("-" * 40)
    
    try:
        from src.telegram_client import TelegramClientManager
        
        # Create client manager
        client_manager = TelegramClientManager()
        
        # Check initialization
        if await client_manager.initialize():
            print("✅ Telegram client initialized")
        else:
            print("❌ Telegram client initialization failed")
            return False
        
        # Check connection
        if await client_manager.connect():
            print("✅ Telegram client connected")
            
            # Get user info
            me = await client_manager.client.get_me()
            print(f"  Connected as: {me.first_name} (@{me.username})")
            print(f"  User ID: {me.id}")
            
        else:
            print("❌ Telegram client connection failed")
            return False
        
        # Check if client is ready for message handling
        if client_manager.client and client_manager.connected:
            print("✅ Client ready for message handling")
        else:
            print("❌ Client not ready for message handling")
        
        # Cleanup
        await client_manager.disconnect()
        print("✅ Telegram client analysis complete")
        
    except Exception as e:
        print(f"❌ Telegram client analysis failed: {e}")
        return False
    
    # 4. Message Handler Setup Analysis
    print("\n🔧 4. MESSAGE HANDLER SETUP ANALYSIS")
    print("-" * 40)
    
    try:
        # Check if the bot would set up handlers correctly
        active_groups = config.target_group.active_group_ids
        
        print(f"Groups that would be passed to message handler: {len(active_groups)}")
        for group_id in active_groups:
            print(f"  - {group_id}")
        
        # Check critical groups
        gmgn_id = -1002202241417
        meme_1000x_id = -1002333406905
        
        if gmgn_id in active_groups:
            print(f"✅ GMGN Featured Signals ({gmgn_id}) is in active groups")
        else:
            print(f"❌ GMGN Featured Signals ({gmgn_id}) is NOT in active groups")
        
        if meme_1000x_id in active_groups:
            print(f"✅ MEME 1000X ({meme_1000x_id}) is in active groups")
        else:
            print(f"❌ MEME 1000X ({meme_1000x_id}) is NOT in active groups")
        
        print("✅ Message handler setup analysis complete")
        
    except Exception as e:
        print(f"❌ Message handler setup analysis failed: {e}")
        return False
    
    # 5. Contract Address Detection Analysis
    print("\n🔍 5. CONTRACT ADDRESS DETECTION ANALYSIS")
    print("-" * 40)
    
    try:
        from src.ca_detector import CADetector
        from src.database import DatabaseManager
        
        # Initialize components
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        ca_detector = CADetector(db_manager)
        
        # Test CA detection with sample messages
        test_messages = [
            "New token: 7xKXtg2CW3El9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn",
            "Check this out: https://pump.fun/7xKXtg2CW3El9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn",
            "CA: 7xKXtg2CW3El9ToiAM6ZeLAVRxMquqv3ggaepMVuCTcn 🚀",
            "No CA in this message",
        ]
        
        print("Testing CA detection with sample messages:")
        for i, message in enumerate(test_messages):
            cas = await ca_detector.process_message(message, f"test_{i}", -1002202241417)
            print(f"  Message {i+1}: {len(cas)} CAs detected")
            if cas:
                print(f"    Detected: {cas}")
        
        # Cleanup
        await db_manager.close()
        print("✅ Contract address detection analysis complete")
        
    except Exception as e:
        print(f"❌ Contract address detection analysis failed: {e}")
        return False
    
    # 6. Summary and Recommendations
    print("\n📊 6. SUMMARY AND RECOMMENDATIONS")
    print("-" * 40)
    
    print("✅ All diagnostic checks passed!")
    print("\nRecommendations:")
    print("1. Verify the bot is actually receiving messages by checking logs")
    print("2. Test with a simple message in one of the monitored groups")
    print("3. Check if Telegram session is properly authenticated")
    print("4. Verify network connectivity to Telegram servers")
    
    print("\n" + "=" * 80)
    print("🎉 DIAGNOSTIC COMPLETE")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(diagnose_message_detection())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\nDiagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Diagnostic failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
