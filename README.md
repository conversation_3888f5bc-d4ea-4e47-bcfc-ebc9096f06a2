# CLA v2 Telegram Bot - Private Repository Technical Documentation

**⚠️ PRIVATE REPOSITORY - INTERNAL USE ONLY**

This repository contains the complete CLA v2 Telegram bot implementation with Phase 1 slow cook pattern analysis. This documentation provides comprehensive technical details for internal development and maintenance.

## 🏗️ System Architecture Overview

The CLA v2 bot is a sophisticated Telegram message processing system that monitors 8 source groups, analyzes contract addresses (CAs), applies trending analysis with anti-pump protection, and forwards qualified signals to 4 destination channels.

### Core Processing Pipeline

```
Telegram Groups (8) → Message Reception → CA Detection → Trending Analysis → 
Slow Cook Pattern Analysis → Anti-Pump Protection → Parallel Forwarding → 
Destination Channels (4)
```

### High-Level Component Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    CLA v2 TELEGRAM BOT SYSTEM                  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Message         │  │ Contract Address│  │ Trending        │ │
│  │ Reception       │  │ Detection       │  │ Analysis        │ │
│  │                 │  │                 │  │                 │ │
│  │ • 8 Groups      │  │ • URL Extraction│  │ • Anti-Pump     │ │
│  │ • Event Handler │  │ • Standalone CAs│  │ • Velocity      │ │
│  │ • Rate Limiting │  │ • Validation    │  │ • Cross-Group   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │        │
│           └─────────────────────┼─────────────────────┘        │
│                                 │                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │              PHASE 1 SLOW COOK SYSTEM                  │   │
│  │                                                         │   │
│  │  ┌─────────────────┐  ┌─────────────────┐              │   │
│  │  │ SlowCookAnalytics│  │ CARescueTracker │              │   │
│  │  │                 │  │                 │              │   │
│  │  │ • Pattern Store │  │ • Multi-mention │              │   │
│  │  │ • Cross-group   │  │ • Rescue Logic  │              │   │
│  │  │ • Time Windows  │  │ • Eligibility   │              │   │
│  │  └─────────────────┘  └─────────────────┘              │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                 │                              │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                PARALLEL FORWARDING                      │   │
│  │                                                         │   │
│  │  BonkBot ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← │   │
│  │  CLA v2.0 ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ←│   │
│  │  Monaco PNL ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ←│   │
│  │  WINNERS ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

## 📊 Data Flow and Processing Logic

### Message Processing Decision Tree

```mermaid
graph TD
    A[Message Received] --> B{Valid Group?}
    B -->|No| C[Discard]
    B -->|Yes| D{Contains Text?}
    D -->|No| C
    D -->|Yes| E[Extract Contract Addresses]
    E --> F{CAs Found?}
    F -->|No| G[Log No CAs]
    F -->|Yes| H[Validate CAs]
    H --> I{Valid CAs?}
    I -->|No| G
    I -->|Yes| J[Apply Trending Analysis]
    J --> K{High Volume Group?}
    K -->|Yes| L[Enhanced Trending + Anti-Pump]
    K -->|No| M[Direct Forwarding]
    L --> N{Trending Qualified?}
    N -->|No| O[Noise Filtered]
    N -->|Yes| P[Record Slow Cook Pattern]
    M --> P
    P --> Q[Parallel Forward to 4 Destinations]
    Q --> R[Update Statistics]
```

## 🔧 Core Modules Deep Dive

### 1. Message Reception System (`src/telegram_client.py`)

**Purpose**: Handles Telegram API connection, authentication, and message event handling.

**Key Components**:
- **TelegramClientManager**: Main client wrapper with connection management
- **Event Handler Registration**: Registers message handlers for specific group IDs
- **Rate Limiting**: Built-in throttling to prevent API rate limits
- **Connection Recovery**: Automatic reconnection on network failures

**Technical Implementation**:
```python
# Event handler registration pattern
@self.client.on(events.NewMessage(chats=active_group_ids))
async def message_handler_wrapper(event):
    async with self.throttler:
        await self.message_handler(event)
```

**Configuration Dependencies**:
- `TELEGRAM_API_ID`: Telegram API application ID
- `TELEGRAM_API_HASH`: Telegram API application hash
- `TELEGRAM_SESSION_NAME`: Session file name for authentication persistence

### 2. Contract Address Detection (`src/message_parser.py`)

**Purpose**: Extracts and validates Solana contract addresses from Telegram messages.

**Detection Methods**:
1. **URL Extraction**: Extracts CAs from platform URLs (GMGN, pump.fun, dexscreener, etc.)
2. **Standalone Detection**: Finds CAs in message text using regex patterns
3. **Validation**: Validates using base58 decoding and length checks

**URL Patterns Supported**:
```python
url_patterns = {
    'dexscreener': r'dexscreener\.com/solana/([1-9A-HJ-NP-Za-km-z]{43,44})',
    'pump_fun': r'pump\.fun/([1-9A-HJ-NP-Za-km-z]{43,44})',
    'solscan': r'solscan\.io/token/([1-9A-HJ-NP-Za-km-z]{43,44})',
    'axiom': r'axiom\.trade/t/([1-9A-HJ-NP-Za-km-z]{43,44})',
    'gmgn': r'gmgn\.ai/sol/token/([1-9A-HJ-NP-Za-km-z]{43,44})'
}
```

**Validation Logic**:
- Length: 43-44 characters (Solana standard)
- Base58 encoding validation
- Non-zero address check
- Deduplication with standalone CA priority

### 3. Trending Analysis System (`src/trending_analyzer.py`)

**Purpose**: Implements sophisticated trending analysis with anti-pump protection.

**Selective Protection Strategy**:
- **High-Volume Groups**: Enhanced trending analysis (6 mentions in 8 minutes)
- **Low-Volume Groups**: Direct forwarding with global duplicate prevention

**Anti-Pump Protection Mechanisms**:
1. **Velocity Analysis**: Maximum 3 mentions per minute
2. **Time Spread**: Minimum 120 seconds between mentions
3. **Organic Growth**: 3-minute window for natural mention distribution
4. **Cross-Group Validation**: Mentions across multiple groups increase confidence

**Algorithm Implementation**:
```python
def analyze_ca_trending(self, ca: str, group_id: int) -> TrendingResult:
    # 1. Record mention with timestamp
    # 2. Calculate velocity (mentions per minute)
    # 3. Check time spread between mentions
    # 4. Apply group-specific thresholds
    # 5. Return trending decision with confidence score
```

## 📋 Monitored Groups and Destinations

### Source Groups (8 Total)

| Group Name | Group ID | Type | Purpose |
|------------|----------|------|---------|
| 🧠 GMGN Featured Signals | -1002202241417 | High-Volume | Premium signals with enhanced analysis |
| 🚀 MEME 1000X | -1002333406905 | High-Volume | High-frequency meme token signals |
| FREE WHALE SIGNALS | -1002380594298 | Low-Volume | Community whale tracking |
| Solana Activity Tracker | -1002270988204 | Low-Volume | General Solana ecosystem activity |
| 🌹 MANIFEST | -1002064145465 | Low-Volume | Curated token discoveries |
| 👤 Mark Degens | -1001763265784 | Low-Volume | Individual trader signals |
| 💎 FINDERTRENDING | -1002139128702 | Low-Volume | Trending token identification |
| BUGSIE | -1002356333152 | Low-Volume | Community-driven signals |

### Destination Channels (4 Total)

| Destination | Type | Integration | Purpose |
|-------------|------|-------------|---------|
| BonkBot | Bot | `@BonkBot_bot` | Automated trading bot integration |
| CLA v2.0 | Channel | -1002659786727 | Primary signal distribution channel |
| Monaco PNL | Channel | -1002666569586 | Signal forwarding (PNL tracking removed) |
| WINNERS | Group | -1002439728391 | Community signal sharing |

## 🐌 Phase 1 Slow Cook Pattern Analysis

### SlowCookAnalytics (`src/slow_cook_analytics.py`)

**Purpose**: Tracks multi-mention patterns across groups to identify organic growth vs pump schemes.

**Pattern Analysis Logic**:
1. **Mention Recording**: Timestamp and group ID for each CA mention
2. **Velocity Calculation**: Mentions per minute over time windows
3. **Cross-Group Detection**: Bonus scoring for mentions across different groups
4. **Pattern Confidence**: Scoring algorithm based on time distribution and group diversity

**Configuration Parameters**:
```python
SLOW_COOK_CONFIG = {
    'min_mentions': 3,           # Minimum mentions to analyze
    'time_window': 3600,         # 1 hour analysis window
    'cross_group_bonus': 1.5,    # Bonus for cross-group mentions
    'velocity_threshold': 0.5,   # Max mentions per minute for organic growth
    'pattern_memory': 86400      # 24 hour pattern retention
}
```

### CA Rescue Tracker (`src/ca_rescue_tracker.py`)

**Purpose**: Implements multi-mention rescue logic for tokens that gain momentum over time.

**Rescue Categories**:
- **MULTI_MENTION**: Multiple mentions across time windows
- **SLOW_COOK**: Slow cook pattern detected
- **CROSS_GROUP**: Mentions across multiple groups
- **VELOCITY_FILTERED**: Filtered due to high velocity (pump protection)

## 🚀 Quick Start Guide

### 1. Installation and Setup

```bash
# Clone repository
git clone https://github.com/bigdrako1/clav2.git
cd clav2

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env with your Telegram credentials
```

### 2. Telegram Authentication

```bash
# Authenticate with Telegram
python authenticate_telegram.py
```

### 3. Start the Bot

```bash
# Start main bot
python main.py
```

### 4. Monitor Operations

```bash
# Monitor logs in real-time
tail -f logs/cla_bot.log

# Check performance statistics
python monitor_performance.py
```

## 🔧 Configuration Management

### Environment Variables (.env)

```bash
# Telegram API Configuration
TELEGRAM_API_ID=29232008
TELEGRAM_API_HASH=431f5f680d1975acfdd9050089661772
TELEGRAM_PHONE=+************
TELEGRAM_SESSION_NAME=cla_bot_session

# Group Configuration
TARGET_GROUP_ID=-1002380594298
TARGET_GROUP_NAME=FREE WHALE SIGNALS

# Additional Groups (comma-separated)
ADDITIONAL_GROUP_IDS=-1002270988204,-1002064145465,-1001763265784,-1002139128702,-1002202241417,-1002333406905,-1002356333152
ADDITIONAL_GROUP_NAMES=Solana Activity Tracker,🌹 MANIFEST,👤 Mark Degens,💎 FINDERTRENDING,🧠 GMGN Featured Signals,🚀 MEME 1000X,BUGSIE
ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE,ACTIVE

# Destination Configuration
BONKBOT_USER_ID=5312785865
CLA_V2_GROUP_ID=-1002659786727
MONACO_PNL_GROUP_ID=-1002666569586
WINNERS_GROUP_ID=-1002439728391

# Database Configuration
DATABASE_PATH=./data/cla_bot.db
DATABASE_BACKUP_ENABLED=true
DATABASE_BACKUP_INTERVAL=3600

# Cache Configuration
CA_CACHE_EXPIRY_HOURS=168
MESSAGE_CACHE_SIZE=10000
DUPLICATE_PREVENTION_HOURS=24
```

## 🗄️ Database Schema

### Core Tables

```sql
-- Contract addresses with metadata
CREATE TABLE contract_addresses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT UNIQUE NOT NULL,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    mention_count INTEGER DEFAULT 1,
    source_groups TEXT,  -- JSON array of group IDs
    trending_score REAL DEFAULT 0.0,
    is_trending BOOLEAN DEFAULT FALSE
);

-- Message processing cache
CREATE TABLE message_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    message_id INTEGER UNIQUE NOT NULL,
    group_id INTEGER NOT NULL,
    message_text TEXT,
    processed BOOLEAN DEFAULT FALSE,
    ca_extracted TEXT,  -- JSON array of extracted CAs
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Slow cook pattern tracking (Phase 1)
CREATE TABLE slow_cook_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    group_id INTEGER NOT NULL,
    mention_timestamp REAL NOT NULL,
    pattern_data TEXT,  -- JSON data with analysis results
    created_at REAL DEFAULT (julianday('now'))
);

-- Enhanced rescue tracking
CREATE TABLE rescue_tracking_enhanced (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    ca TEXT NOT NULL,
    rescue_type TEXT NOT NULL,  -- MULTI_MENTION, SLOW_COOK, etc.
    group_id INTEGER NOT NULL,
    mention_count INTEGER DEFAULT 1,
    first_mention REAL NOT NULL,
    last_mention REAL NOT NULL,
    pattern_confidence REAL DEFAULT 0.0,
    created_at REAL DEFAULT (julianday('now'))
);
```

### Performance Indexes

```sql
-- Optimize CA lookups
CREATE INDEX idx_ca_lookup ON contract_addresses(ca);
CREATE INDEX idx_ca_trending ON contract_addresses(is_trending, trending_score);

-- Optimize message cache queries
CREATE INDEX idx_message_cache_group ON message_cache(group_id, timestamp);
CREATE INDEX idx_message_cache_processed ON message_cache(processed, timestamp);

-- Optimize slow cook pattern queries
CREATE INDEX idx_slow_cook_ca ON slow_cook_patterns(ca);
CREATE INDEX idx_slow_cook_timestamp ON slow_cook_patterns(mention_timestamp);

-- Optimize rescue tracking queries
CREATE INDEX idx_rescue_enhanced_ca ON rescue_tracking_enhanced(ca);
CREATE INDEX idx_rescue_enhanced_type ON rescue_tracking_enhanced(rescue_type);
```

## 🚀 Deployment and Operations

### Adding New Source Groups

1. **Update Environment Variables**:
   ```bash
   # Add group ID to ADDITIONAL_GROUP_IDS
   ADDITIONAL_GROUP_IDS=-1002270988204,...,-1002NEWGROUP

   # Add group name to ADDITIONAL_GROUP_NAMES
   ADDITIONAL_GROUP_NAMES=Existing Groups,New Group Name

   # Add status to ADDITIONAL_GROUP_STATUS
   ADDITIONAL_GROUP_STATUS=ACTIVE,ACTIVE,...,ACTIVE
   ```

2. **Verify Group Access**:
   ```python
   python verify_group_access.py -g -1002NEWGROUP
   ```

3. **Update Group Manager Classification**:
   ```python
   # In src/group_manager.py
   HIGH_VOLUME_GROUPS = [-1002333406905, -1002202241417, -1002NEWGROUP]  # If high-volume
   ```

### Adding New Destination Channels

1. **Create Integration Module**:
   ```python
   # src/new_destination_integration.py
   class NewDestinationIntegration:
       def __init__(self, telegram_client):
           self.telegram_client = telegram_client

       async def send_multiple_cas(self, cas, message_text, source_group):
           # Implementation for new destination
   ```

2. **Update Bot Integration**:
   ```python
   # In src/bot.py
   from src.new_destination_integration import NewDestinationIntegration

   # Initialize in __init__
   self.new_destination_integration = None

   # Add to parallel forwarding
   async def _forward_to_new_destination_parallel(self, cas, message_text, source_group, chat_id):
       # Implementation
   ```

3. **Update Configuration**:
   ```bash
   # Add to .env
   NEW_DESTINATION_ID=-1002NEWDEST
   ```

### Performance Monitoring

**Key Metrics to Monitor**:
- Message processing rate (messages/minute)
- CA detection accuracy (detected vs actual)
- Trending analysis effectiveness (qualified vs noise)
- Database query performance (average response time)
- Memory usage (cache sizes, pattern storage)
- Network connectivity (Telegram API response times)

**Monitoring Commands**:
```bash
# Real-time log monitoring
tail -f logs/cla_bot.log | grep -E "📊|⚠️|❌"

# Performance statistics
python monitor_performance.py --interval 60

# Database health check
python check_database_health.py

# Memory usage analysis
python analyze_memory_usage.py
```

## 🔧 Development and Maintenance

### Code Organization Principles

1. **Single Responsibility**: Each module handles one specific aspect
2. **Dependency Injection**: Components receive dependencies rather than creating them
3. **Async/Await**: All I/O operations use async patterns
4. **Error Handling**: Comprehensive try/catch with logging
5. **Configuration Driven**: Behavior controlled through configuration files

### Testing Strategy

```bash
# Unit tests for individual components
python -m pytest tests/test_message_parser.py
python -m pytest tests/test_ca_detector.py

# Integration tests for full pipeline
python test_full_pipeline.py

# Performance tests
python test_performance_benchmarks.py

# Live system tests (with real Telegram groups)
python test_live_message_reception.py
```

### Debugging Tools

```bash
# CA detection testing
python test_ca_detection.py

# Message handler verification
python test_message_handler_registration.py

# Group access verification
python test_group_access_simple.py

# Phase 1 component testing
python test_slow_cook_phase1.py
```

## 📚 Additional Documentation

- **PHASE1_SLOW_COOK_DOCUMENTATION.md**: Complete Phase 1 implementation guide
- **TECHNICAL_ARCHITECTURE.md**: Detailed system architecture and component relationships
- **DATABASE_SCHEMA.md**: Complete database documentation with examples
- **DEPLOYMENT_GUIDE.md**: Step-by-step deployment and configuration instructions
- **TROUBLESHOOTING.md**: Common issues and solutions

This documentation provides the complete technical foundation for understanding, maintaining, and extending the CLA v2 Telegram bot system. All implementation details, configuration options, and operational procedures are documented for internal development use.
