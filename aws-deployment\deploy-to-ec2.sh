#!/bin/bash
# CLA v2.0 Telegram Bot - AWS EC2 Deployment Script
# =================================================
# This script deploys the bot to AWS EC2 from your local machine
# Usage: ./deploy-to-ec2.sh <EC2_IP> <SSH_KEY_PATH>

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check arguments
if [ $# -ne 2 ]; then
    echo -e "${RED}Usage: $0 <EC2_IP> <SSH_KEY_PATH>${NC}"
    echo -e "Example: $0 ************ ~/.ssh/cla-bot-key.pem"
    exit 1
fi

EC2_IP="$1"
SSH_KEY="$2"
EC2_USER="ubuntu"
REMOTE_TEMP="/tmp/cla-bot-deploy"
INSTALL_DIR="/opt/cla-bot"

echo -e "${BLUE}🚀 CLA v2.0 Bot - AWS EC2 Deployment${NC}"
echo -e "${BLUE}===================================${NC}"
echo -e "Target: ${EC2_USER}@${EC2_IP}"
echo -e "SSH Key: ${SSH_KEY}"
echo ""

# Verify SSH key exists
if [[ ! -f "$SSH_KEY" ]]; then
    echo -e "${RED}❌ SSH key not found: $SSH_KEY${NC}"
    exit 1
fi

# Verify SSH key permissions
if [[ "$(stat -c %a "$SSH_KEY")" != "400" ]]; then
    echo -e "${YELLOW}⚠️ Fixing SSH key permissions...${NC}"
    chmod 400 "$SSH_KEY"
fi

# Test SSH connection
echo -e "${BLUE}🔍 Testing SSH connection...${NC}"
if ! ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o BatchMode=yes "$EC2_USER@$EC2_IP" "echo 'SSH connection successful'" 2>/dev/null; then
    echo -e "${RED}❌ Cannot connect to EC2 instance. Please check:${NC}"
    echo -e "   - EC2 instance is running"
    echo -e "   - Security group allows SSH from your IP"
    echo -e "   - SSH key is correct"
    echo -e "   - IP address is correct"
    exit 1
fi

echo -e "${GREEN}✅ SSH connection successful${NC}"

# Create deployment package
echo -e "${BLUE}📦 Creating deployment package...${NC}"
TEMP_DIR=$(mktemp -d)
PACKAGE_FILE="$TEMP_DIR/cla-bot-deployment.tar.gz"

# Files to include in deployment
tar -czf "$PACKAGE_FILE" \
    --exclude='.git' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.venv' \
    --exclude='logs/*' \
    --exclude='data/*' \
    --exclude='*.log' \
    --exclude='.env' \
    --exclude='*.session' \
    --exclude='*.session-journal' \
    src/ \
    *.py \
    requirements.txt \
    aws-deployment/ \
    deployment/ \
    *.md

echo -e "${GREEN}✅ Deployment package created: $(du -h "$PACKAGE_FILE" | cut -f1)${NC}"

# Transfer package to EC2
echo -e "${BLUE}📤 Transferring files to EC2...${NC}"
scp -i "$SSH_KEY" "$PACKAGE_FILE" "$EC2_USER@$EC2_IP:$REMOTE_TEMP.tar.gz"

# Extract and install on EC2
echo -e "${BLUE}🔧 Installing on EC2...${NC}"
ssh -i "$SSH_KEY" "$EC2_USER@$EC2_IP" << EOF
set -e

echo "🗂️ Extracting deployment package..."
mkdir -p "$REMOTE_TEMP"
cd "$REMOTE_TEMP"
tar -xzf "$REMOTE_TEMP.tar.gz"

echo "🔧 Running installation script..."
sudo bash aws-deployment/aws-install.sh

echo "📋 Copying bot files..."
sudo cp -r . "$INSTALL_DIR/"
sudo chown -R cla-bot:cla-bot "$INSTALL_DIR"

echo "🧹 Cleaning up..."
rm -rf "$REMOTE_TEMP" "$REMOTE_TEMP.tar.gz"

echo "✅ Installation completed!"
EOF

# Cleanup local temp files
rm -rf "$TEMP_DIR"

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Next Steps:${NC}"
echo -e "1. Configure environment variables:"
echo -e "   ${BLUE}ssh -i $SSH_KEY $EC2_USER@$EC2_IP${NC}"
echo -e "   ${BLUE}sudo nano $INSTALL_DIR/.env${NC}"
echo ""
echo -e "2. Start the bot service:"
echo -e "   ${BLUE}sudo systemctl start cla-bot${NC}"
echo ""
echo -e "3. Check status:"
echo -e "   ${BLUE}cla-bot-status${NC}"
echo ""
echo -e "4. View logs:"
echo -e "   ${BLUE}sudo journalctl -u cla-bot -f${NC}"
echo ""
echo -e "${RED}⚠️ IMPORTANT: Remember to configure your .env file with actual API keys!${NC}"